!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).mlMatrix={})}(this,(function(t){"use strict";const e=Object.prototype.toString;function r(t){const r=e.call(t);return r.endsWith("Array]")&&!r.includes("Big")}function s(t){var e,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!r(t))throw new TypeError("input must be an array");if(0===t.length)throw new TypeError("input must not be empty");if(void 0!==s.output){if(!r(s.output))throw new TypeError("output option must be an array if specified");e=s.output}else e=new Array(t.length);var o=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!r(t))throw new TypeError("input must be an array");if(0===t.length)throw new TypeError("input must not be empty");var s=e.fromIndex,o=void 0===s?0:s,i=e.toIndex,n=void 0===i?t.length:i;if(o<0||o>=t.length||!Number.isInteger(o))throw new Error("fromIndex must be a positive integer smaller than length");if(n<=o||n>t.length||!Number.isInteger(n))throw new Error("toIndex must be an integer greater than fromIndex and at most equal to length");for(var h=t[o],u=o+1;u<n;u++)t[u]<h&&(h=t[u]);return h}(t),i=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!r(t))throw new TypeError("input must be an array");if(0===t.length)throw new TypeError("input must not be empty");var s=e.fromIndex,o=void 0===s?0:s,i=e.toIndex,n=void 0===i?t.length:i;if(o<0||o>=t.length||!Number.isInteger(o))throw new Error("fromIndex must be a positive integer smaller than length");if(n<=o||n>t.length||!Number.isInteger(n))throw new Error("toIndex must be an integer greater than fromIndex and at most equal to length");for(var h=t[o],u=o+1;u<n;u++)t[u]>h&&(h=t[u]);return h}(t);if(o===i)throw new RangeError("minimum and maximum input values are equal. Cannot rescale a constant array");var n=s.min,h=void 0===n?s.autoMinMax?o:0:n,u=s.max,l=void 0===u?s.autoMinMax?i:1:u;if(h>=l)throw new RangeError("min option must be smaller than max option");for(var a=(l-h)/(i-o),f=0;f<t.length;f++)e[f]=(t[f]-o)*a+h;return e}const o=" ".repeat(2),i=" ".repeat(4);function n(t,e={}){const{maxRows:r=15,maxColumns:s=10,maxNumSize:n=8,padMinus:u="auto"}=e;return`${t.constructor.name} {\n${o}[\n${i}${function(t,e,r,s,o){const{rows:n,columns:u}=t,l=Math.min(n,e),a=Math.min(u,r),f=[];if("auto"===o){o=!1;t:for(let e=0;e<l;e++)for(let r=0;r<a;r++)if(t.get(e,r)<0){o=!0;break t}}for(let e=0;e<l;e++){let r=[];for(let i=0;i<a;i++)r.push(h(t.get(e,i),s,o));f.push(`${r.join(" ")}`)}a!==u&&(f[f.length-1]+=` ... ${u-r} more columns`);l!==n&&f.push(`... ${n-e} more rows`);return f.join(`\n${i}`)}(t,r,s,n,u)}\n${o}]\n${o}rows: ${t.rows}\n${o}columns: ${t.columns}\n}`}function h(t,e,r){return(t>=0&&r?` ${u(t,e-1)}`:u(t,e)).padEnd(e)}function u(t,e){let r=t.toString();if(r.length<=e)return r;let s=t.toFixed(e);if(s.length>e&&(s=t.toFixed(Math.max(0,e-(s.length-e)))),s.length<=e&&!s.startsWith("0.000")&&!s.startsWith("-0.000"))return s;let o=t.toExponential(e);return o.length>e&&(o=t.toExponential(Math.max(0,e-(o.length-e)))),o.slice(0)}function l(t,e,r){let s=r?t.rows:t.rows-1;if(e<0||e>s)throw new RangeError("Row index out of range")}function a(t,e,r){let s=r?t.columns:t.columns-1;if(e<0||e>s)throw new RangeError("Column index out of range")}function f(t,e){if(e.to1DArray&&(e=e.to1DArray()),e.length!==t.columns)throw new RangeError("vector size must be the same as the number of columns");return e}function c(t,e){if(e.to1DArray&&(e=e.to1DArray()),e.length!==t.rows)throw new RangeError("vector size must be the same as the number of rows");return e}function m(t,e){if(!r(e))throw new TypeError("row indices must be an array");for(let r=0;r<e.length;r++)if(e[r]<0||e[r]>=t.rows)throw new RangeError("row indices are out of range")}function g(t,e){if(!r(e))throw new TypeError("column indices must be an array");for(let r=0;r<e.length;r++)if(e[r]<0||e[r]>=t.columns)throw new RangeError("column indices are out of range")}function w(t,e,r,s,o){if(5!==arguments.length)throw new RangeError("expected 4 arguments");if(d("startRow",e),d("endRow",r),d("startColumn",s),d("endColumn",o),e>r||s>o||e<0||e>=t.rows||r<0||r>=t.rows||s<0||s>=t.columns||o<0||o>=t.columns)throw new RangeError("Submatrix indices are out of range")}function p(t,e=0){let r=[];for(let s=0;s<t;s++)r.push(e);return r}function d(t,e){if("number"!=typeof e)throw new TypeError(`${t} must be a number`)}function y(t){if(t.isEmpty())throw new Error("Empty matrix has no elements to index")}class M{static from1DArray(t,e,r){if(t*e!==r.length)throw new RangeError("data length does not match given dimensions");let s=new x(t,e);for(let o=0;o<t;o++)for(let t=0;t<e;t++)s.set(o,t,r[o*e+t]);return s}static rowVector(t){let e=new x(1,t.length);for(let r=0;r<t.length;r++)e.set(0,r,t[r]);return e}static columnVector(t){let e=new x(t.length,1);for(let r=0;r<t.length;r++)e.set(r,0,t[r]);return e}static zeros(t,e){return new x(t,e)}static ones(t,e){return new x(t,e).fill(1)}static rand(t,e,r={}){if("object"!=typeof r)throw new TypeError("options must be an object");const{random:s=Math.random}=r;let o=new x(t,e);for(let r=0;r<t;r++)for(let t=0;t<e;t++)o.set(r,t,s());return o}static randInt(t,e,r={}){if("object"!=typeof r)throw new TypeError("options must be an object");const{min:s=0,max:o=1e3,random:i=Math.random}=r;if(!Number.isInteger(s))throw new TypeError("min must be an integer");if(!Number.isInteger(o))throw new TypeError("max must be an integer");if(s>=o)throw new RangeError("min must be smaller than max");let n=o-s,h=new x(t,e);for(let r=0;r<t;r++)for(let t=0;t<e;t++){let e=s+Math.round(i()*n);h.set(r,t,e)}return h}static eye(t,e,r){void 0===e&&(e=t),void 0===r&&(r=1);let s=Math.min(t,e),o=this.zeros(t,e);for(let t=0;t<s;t++)o.set(t,t,r);return o}static diag(t,e,r){let s=t.length;void 0===e&&(e=s),void 0===r&&(r=e);let o=Math.min(s,e,r),i=this.zeros(e,r);for(let e=0;e<o;e++)i.set(e,e,t[e]);return i}static min(t,e){t=this.checkMatrix(t),e=this.checkMatrix(e);let r=t.rows,s=t.columns,o=new x(r,s);for(let i=0;i<r;i++)for(let r=0;r<s;r++)o.set(i,r,Math.min(t.get(i,r),e.get(i,r)));return o}static max(t,e){t=this.checkMatrix(t),e=this.checkMatrix(e);let r=t.rows,s=t.columns,o=new this(r,s);for(let i=0;i<r;i++)for(let r=0;r<s;r++)o.set(i,r,Math.max(t.get(i,r),e.get(i,r)));return o}static checkMatrix(t){return M.isMatrix(t)?t:new x(t)}static isMatrix(t){return null!=t&&"Matrix"===t.klass}get size(){return this.rows*this.columns}apply(t){if("function"!=typeof t)throw new TypeError("callback must be a function");for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)t.call(this,e,r);return this}to1DArray(){let t=[];for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)t.push(this.get(e,r));return t}to2DArray(){let t=[];for(let e=0;e<this.rows;e++){t.push([]);for(let r=0;r<this.columns;r++)t[e].push(this.get(e,r))}return t}toJSON(){return this.to2DArray()}isRowVector(){return 1===this.rows}isColumnVector(){return 1===this.columns}isVector(){return 1===this.rows||1===this.columns}isSquare(){return this.rows===this.columns}isEmpty(){return 0===this.rows||0===this.columns}isSymmetric(){if(this.isSquare()){for(let t=0;t<this.rows;t++)for(let e=0;e<=t;e++)if(this.get(t,e)!==this.get(e,t))return!1;return!0}return!1}isDistance(){if(!this.isSymmetric())return!1;for(let t=0;t<this.rows;t++)if(0!==this.get(t,t))return!1;return!0}isEchelonForm(){let t=0,e=0,r=-1,s=!0,o=!1;for(;t<this.rows&&s;){for(e=0,o=!1;e<this.columns&&!1===o;)0===this.get(t,e)?e++:1===this.get(t,e)&&e>r?(o=!0,r=e):(s=!1,o=!0);t++}return s}isReducedEchelonForm(){let t=0,e=0,r=-1,s=!0,o=!1;for(;t<this.rows&&s;){for(e=0,o=!1;e<this.columns&&!1===o;)0===this.get(t,e)?e++:1===this.get(t,e)&&e>r?(o=!0,r=e):(s=!1,o=!0);for(let r=e+1;r<this.rows;r++)0!==this.get(t,r)&&(s=!1);t++}return s}echelonForm(){let t=this.clone(),e=0,r=0;for(;e<t.rows&&r<t.columns;){let s=e;for(let o=e;o<t.rows;o++)t.get(o,r)>t.get(s,r)&&(s=o);if(0===t.get(s,r))r++;else{t.swapRows(e,s);let o=t.get(e,r);for(let s=r;s<t.columns;s++)t.set(e,s,t.get(e,s)/o);for(let s=e+1;s<t.rows;s++){let o=t.get(s,r)/t.get(e,r);t.set(s,r,0);for(let i=r+1;i<t.columns;i++)t.set(s,i,t.get(s,i)-t.get(e,i)*o)}e++,r++}}return t}reducedEchelonForm(){let t=this.echelonForm(),e=t.columns,r=t.rows,s=r-1;for(;s>=0;)if(0===t.maxRow(s))s--;else{let o=0,i=!1;for(;o<r&&!1===i;)1===t.get(s,o)?i=!0:o++;for(let r=0;r<s;r++){let i=t.get(r,o);for(let n=o;n<e;n++){let e=t.get(r,n)-i*t.get(s,n);t.set(r,n,e)}}s--}return t}set(){throw new Error("set method is unimplemented")}get(){throw new Error("get method is unimplemented")}repeat(t={}){if("object"!=typeof t)throw new TypeError("options must be an object");const{rows:e=1,columns:r=1}=t;if(!Number.isInteger(e)||e<=0)throw new TypeError("rows must be a positive integer");if(!Number.isInteger(r)||r<=0)throw new TypeError("columns must be a positive integer");let s=new x(this.rows*e,this.columns*r);for(let t=0;t<e;t++)for(let e=0;e<r;e++)s.setSubMatrix(this,this.rows*t,this.columns*e);return s}fill(t){for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,t);return this}neg(){return this.mulS(-1)}getRow(t){l(this,t);let e=[];for(let r=0;r<this.columns;r++)e.push(this.get(t,r));return e}getRowVector(t){return x.rowVector(this.getRow(t))}setRow(t,e){l(this,t),e=f(this,e);for(let r=0;r<this.columns;r++)this.set(t,r,e[r]);return this}swapRows(t,e){l(this,t),l(this,e);for(let r=0;r<this.columns;r++){let s=this.get(t,r);this.set(t,r,this.get(e,r)),this.set(e,r,s)}return this}getColumn(t){a(this,t);let e=[];for(let r=0;r<this.rows;r++)e.push(this.get(r,t));return e}getColumnVector(t){return x.columnVector(this.getColumn(t))}setColumn(t,e){a(this,t),e=c(this,e);for(let r=0;r<this.rows;r++)this.set(r,t,e[r]);return this}swapColumns(t,e){a(this,t),a(this,e);for(let r=0;r<this.rows;r++){let s=this.get(r,t);this.set(r,t,this.get(r,e)),this.set(r,e,s)}return this}addRowVector(t){t=f(this,t);for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)+t[r]);return this}subRowVector(t){t=f(this,t);for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)-t[r]);return this}mulRowVector(t){t=f(this,t);for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)*t[r]);return this}divRowVector(t){t=f(this,t);for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)/t[r]);return this}addColumnVector(t){t=c(this,t);for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)+t[e]);return this}subColumnVector(t){t=c(this,t);for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)-t[e]);return this}mulColumnVector(t){t=c(this,t);for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)*t[e]);return this}divColumnVector(t){t=c(this,t);for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)/t[e]);return this}mulRow(t,e){l(this,t);for(let r=0;r<this.columns;r++)this.set(t,r,this.get(t,r)*e);return this}mulColumn(t,e){a(this,t);for(let r=0;r<this.rows;r++)this.set(r,t,this.get(r,t)*e);return this}max(t){if(this.isEmpty())return NaN;switch(t){case"row":{const t=new Array(this.rows).fill(Number.NEGATIVE_INFINITY);for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.get(e,r)>t[e]&&(t[e]=this.get(e,r));return t}case"column":{const t=new Array(this.columns).fill(Number.NEGATIVE_INFINITY);for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.get(e,r)>t[r]&&(t[r]=this.get(e,r));return t}case void 0:{let t=this.get(0,0);for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.get(e,r)>t&&(t=this.get(e,r));return t}default:throw new Error(`invalid option: ${t}`)}}maxIndex(){y(this);let t=this.get(0,0),e=[0,0];for(let r=0;r<this.rows;r++)for(let s=0;s<this.columns;s++)this.get(r,s)>t&&(t=this.get(r,s),e[0]=r,e[1]=s);return e}min(t){if(this.isEmpty())return NaN;switch(t){case"row":{const t=new Array(this.rows).fill(Number.POSITIVE_INFINITY);for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.get(e,r)<t[e]&&(t[e]=this.get(e,r));return t}case"column":{const t=new Array(this.columns).fill(Number.POSITIVE_INFINITY);for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.get(e,r)<t[r]&&(t[r]=this.get(e,r));return t}case void 0:{let t=this.get(0,0);for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.get(e,r)<t&&(t=this.get(e,r));return t}default:throw new Error(`invalid option: ${t}`)}}minIndex(){y(this);let t=this.get(0,0),e=[0,0];for(let r=0;r<this.rows;r++)for(let s=0;s<this.columns;s++)this.get(r,s)<t&&(t=this.get(r,s),e[0]=r,e[1]=s);return e}maxRow(t){if(l(this,t),this.isEmpty())return NaN;let e=this.get(t,0);for(let r=1;r<this.columns;r++)this.get(t,r)>e&&(e=this.get(t,r));return e}maxRowIndex(t){l(this,t),y(this);let e=this.get(t,0),r=[t,0];for(let s=1;s<this.columns;s++)this.get(t,s)>e&&(e=this.get(t,s),r[1]=s);return r}minRow(t){if(l(this,t),this.isEmpty())return NaN;let e=this.get(t,0);for(let r=1;r<this.columns;r++)this.get(t,r)<e&&(e=this.get(t,r));return e}minRowIndex(t){l(this,t),y(this);let e=this.get(t,0),r=[t,0];for(let s=1;s<this.columns;s++)this.get(t,s)<e&&(e=this.get(t,s),r[1]=s);return r}maxColumn(t){if(a(this,t),this.isEmpty())return NaN;let e=this.get(0,t);for(let r=1;r<this.rows;r++)this.get(r,t)>e&&(e=this.get(r,t));return e}maxColumnIndex(t){a(this,t),y(this);let e=this.get(0,t),r=[0,t];for(let s=1;s<this.rows;s++)this.get(s,t)>e&&(e=this.get(s,t),r[0]=s);return r}minColumn(t){if(a(this,t),this.isEmpty())return NaN;let e=this.get(0,t);for(let r=1;r<this.rows;r++)this.get(r,t)<e&&(e=this.get(r,t));return e}minColumnIndex(t){a(this,t),y(this);let e=this.get(0,t),r=[0,t];for(let s=1;s<this.rows;s++)this.get(s,t)<e&&(e=this.get(s,t),r[0]=s);return r}diag(){let t=Math.min(this.rows,this.columns),e=[];for(let r=0;r<t;r++)e.push(this.get(r,r));return e}norm(t="frobenius"){switch(t){case"max":return this.max();case"frobenius":return Math.sqrt(this.dot(this));default:throw new RangeError(`unknown norm type: ${t}`)}}cumulativeSum(){let t=0;for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)t+=this.get(e,r),this.set(e,r,t);return this}dot(t){M.isMatrix(t)&&(t=t.to1DArray());let e=this.to1DArray();if(e.length!==t.length)throw new RangeError("vectors do not have the same size");let r=0;for(let s=0;s<e.length;s++)r+=e[s]*t[s];return r}mmul(t){t=x.checkMatrix(t);let e=this.rows,r=this.columns,s=t.columns,o=new x(e,s),i=new Float64Array(r);for(let n=0;n<s;n++){for(let e=0;e<r;e++)i[e]=t.get(e,n);for(let t=0;t<e;t++){let e=0;for(let s=0;s<r;s++)e+=this.get(t,s)*i[s];o.set(t,n,e)}}return o}mpow(t){if(!this.isSquare())throw new RangeError("Matrix must be square");if(!Number.isInteger(t)||t<0)throw new RangeError("Exponent must be a non-negative integer");let e=x.eye(this.rows),r=this;for(let s=t;s>=1;s/=2)1&s&&(e=e.mmul(r)),r=r.mmul(r);return e}strassen2x2(t){t=x.checkMatrix(t);let e=new x(2,2);const r=this.get(0,0),s=t.get(0,0),o=this.get(0,1),i=t.get(0,1),n=this.get(1,0),h=t.get(1,0),u=this.get(1,1),l=t.get(1,1),a=(r+u)*(s+l),f=(n+u)*s,c=r*(i-l),m=u*(h-s),g=(r+o)*l,w=a+m-g+(o-u)*(h+l),p=c+g,d=f+m,y=a-f+c+(n-r)*(s+i);return e.set(0,0,w),e.set(0,1,p),e.set(1,0,d),e.set(1,1,y),e}strassen3x3(t){t=x.checkMatrix(t);let e=new x(3,3);const r=this.get(0,0),s=this.get(0,1),o=this.get(0,2),i=this.get(1,0),n=this.get(1,1),h=this.get(1,2),u=this.get(2,0),l=this.get(2,1),a=this.get(2,2),f=t.get(0,0),c=t.get(0,1),m=t.get(0,2),g=t.get(1,0),w=t.get(1,1),p=t.get(1,2),d=t.get(2,0),y=t.get(2,1),M=t.get(2,2),b=(r-i)*(-c+w),E=(-r+i+n)*(f-c+w),S=(i+n)*(-f+c),v=r*f,R=(-r+u+l)*(f-m+p),I=(-r+u)*(m-p),k=(u+l)*(-f+m),T=(-o+l+a)*(w+d-y),N=(o-a)*(w-y),V=o*d,C=(l+a)*(-d+y),q=(-o+n+h)*(p+d-M),A=(o-h)*(p-M),D=(n+h)*(-d+M),F=v+V+s*g,z=(r+s+o-i-n-l-a)*w+E+S+v+T+V+C,$=v+R+k+(r+s+o-n-h-u-l)*p+V+q+D,j=b+n*(-f+c+g-w-p-d+M)+E+v+V+q+A,P=b+E+S+v+h*y,L=V+q+A+D+i*m,U=v+R+I+l*(-f+m+g-w-p-d+y)+T+N+V,O=T+N+V+C+u*c,_=v+R+I+k+a*M;return e.set(0,0,F),e.set(0,1,z),e.set(0,2,$),e.set(1,0,j),e.set(1,1,P),e.set(1,2,L),e.set(2,0,U),e.set(2,1,O),e.set(2,2,_),e}mmulStrassen(t){t=x.checkMatrix(t);let e=this.clone(),r=e.rows,s=e.columns,o=t.rows,i=t.columns;function n(t,e,r){let s=t.rows,o=t.columns;if(s===e&&o===r)return t;{let s=M.zeros(e,r);return s=s.setSubMatrix(t,0,0),s}}s!==o&&console.warn(`Multiplying ${r} x ${s} and ${o} x ${i} matrix: dimensions do not match.`);let h=Math.max(r,o),u=Math.max(s,i);return e=n(e,h,u),function t(e,r,s,o){if(s<=512||o<=512)return e.mmul(r);s%2==1&&o%2==1?(e=n(e,s+1,o+1),r=n(r,s+1,o+1)):s%2==1?(e=n(e,s+1,o),r=n(r,s+1,o)):o%2==1&&(e=n(e,s,o+1),r=n(r,s,o+1));let i=parseInt(e.rows/2,10),h=parseInt(e.columns/2,10),u=e.subMatrix(0,i-1,0,h-1),l=r.subMatrix(0,i-1,0,h-1),a=e.subMatrix(0,i-1,h,e.columns-1),f=r.subMatrix(0,i-1,h,r.columns-1),c=e.subMatrix(i,e.rows-1,0,h-1),m=r.subMatrix(i,r.rows-1,0,h-1),g=e.subMatrix(i,e.rows-1,h,e.columns-1),w=r.subMatrix(i,r.rows-1,h,r.columns-1),p=t(M.add(u,g),M.add(l,w),i,h),d=t(M.add(c,g),l,i,h),y=t(u,M.sub(f,w),i,h),b=t(g,M.sub(m,l),i,h),x=t(M.add(u,a),w,i,h),E=t(M.sub(c,u),M.add(l,f),i,h),S=t(M.sub(a,g),M.add(m,w),i,h),v=M.add(p,b);v.sub(x),v.add(S);let R=M.add(y,x),I=M.add(d,b),k=M.sub(p,d);k.add(y),k.add(E);let T=M.zeros(2*v.rows,2*v.columns);return T=T.setSubMatrix(v,0,0),T=T.setSubMatrix(R,v.rows,0),T=T.setSubMatrix(I,0,v.columns),T=T.setSubMatrix(k,v.rows,v.columns),T.subMatrix(0,s-1,0,o-1)}(e,t=n(t,h,u),h,u)}scaleRows(t={}){if("object"!=typeof t)throw new TypeError("options must be an object");const{min:e=0,max:r=1}=t;if(!Number.isFinite(e))throw new TypeError("min must be a number");if(!Number.isFinite(r))throw new TypeError("max must be a number");if(e>=r)throw new RangeError("min must be smaller than max");let o=new x(this.rows,this.columns);for(let t=0;t<this.rows;t++){const i=this.getRow(t);i.length>0&&s(i,{min:e,max:r,output:i}),o.setRow(t,i)}return o}scaleColumns(t={}){if("object"!=typeof t)throw new TypeError("options must be an object");const{min:e=0,max:r=1}=t;if(!Number.isFinite(e))throw new TypeError("min must be a number");if(!Number.isFinite(r))throw new TypeError("max must be a number");if(e>=r)throw new RangeError("min must be smaller than max");let o=new x(this.rows,this.columns);for(let t=0;t<this.columns;t++){const i=this.getColumn(t);i.length&&s(i,{min:e,max:r,output:i}),o.setColumn(t,i)}return o}flipRows(){const t=Math.ceil(this.columns/2);for(let e=0;e<this.rows;e++)for(let r=0;r<t;r++){let t=this.get(e,r),s=this.get(e,this.columns-1-r);this.set(e,r,s),this.set(e,this.columns-1-r,t)}return this}flipColumns(){const t=Math.ceil(this.rows/2);for(let e=0;e<this.columns;e++)for(let r=0;r<t;r++){let t=this.get(r,e),s=this.get(this.rows-1-r,e);this.set(r,e,s),this.set(this.rows-1-r,e,t)}return this}kroneckerProduct(t){t=x.checkMatrix(t);let e=this.rows,r=this.columns,s=t.rows,o=t.columns,i=new x(e*s,r*o);for(let n=0;n<e;n++)for(let e=0;e<r;e++)for(let r=0;r<s;r++)for(let h=0;h<o;h++)i.set(s*n+r,o*e+h,this.get(n,e)*t.get(r,h));return i}kroneckerSum(t){if(t=x.checkMatrix(t),!this.isSquare()||!t.isSquare())throw new Error("Kronecker Sum needs two Square Matrices");let e=this.rows,r=t.rows,s=this.kroneckerProduct(x.eye(r,r)),o=x.eye(e,e).kroneckerProduct(t);return s.add(o)}transpose(){let t=new x(this.columns,this.rows);for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)t.set(r,e,this.get(e,r));return t}sortRows(t=b){for(let e=0;e<this.rows;e++)this.setRow(e,this.getRow(e).sort(t));return this}sortColumns(t=b){for(let e=0;e<this.columns;e++)this.setColumn(e,this.getColumn(e).sort(t));return this}subMatrix(t,e,r,s){w(this,t,e,r,s);let o=new x(e-t+1,s-r+1);for(let i=t;i<=e;i++)for(let e=r;e<=s;e++)o.set(i-t,e-r,this.get(i,e));return o}subMatrixRow(t,e,r){if(void 0===e&&(e=0),void 0===r&&(r=this.columns-1),e>r||e<0||e>=this.columns||r<0||r>=this.columns)throw new RangeError("Argument out of range");let s=new x(t.length,r-e+1);for(let o=0;o<t.length;o++)for(let i=e;i<=r;i++){if(t[o]<0||t[o]>=this.rows)throw new RangeError(`Row index out of range: ${t[o]}`);s.set(o,i-e,this.get(t[o],i))}return s}subMatrixColumn(t,e,r){if(void 0===e&&(e=0),void 0===r&&(r=this.rows-1),e>r||e<0||e>=this.rows||r<0||r>=this.rows)throw new RangeError("Argument out of range");let s=new x(r-e+1,t.length);for(let o=0;o<t.length;o++)for(let i=e;i<=r;i++){if(t[o]<0||t[o]>=this.columns)throw new RangeError(`Column index out of range: ${t[o]}`);s.set(i-e,o,this.get(i,t[o]))}return s}setSubMatrix(t,e,r){if((t=x.checkMatrix(t)).isEmpty())return this;w(this,e,e+t.rows-1,r,r+t.columns-1);for(let s=0;s<t.rows;s++)for(let o=0;o<t.columns;o++)this.set(e+s,r+o,t.get(s,o));return this}selection(t,e){m(this,t),g(this,e);let r=new x(t.length,e.length);for(let s=0;s<t.length;s++){let o=t[s];for(let t=0;t<e.length;t++){let i=e[t];r.set(s,t,this.get(o,i))}}return r}trace(){let t=Math.min(this.rows,this.columns),e=0;for(let r=0;r<t;r++)e+=this.get(r,r);return e}clone(){return this.constructor.copy(this,new x(this.rows,this.columns))}static copy(t,e){for(const[r,s,o]of t.entries())e.set(r,s,o);return e}sum(t){switch(t){case"row":return function(t){let e=p(t.rows);for(let r=0;r<t.rows;++r)for(let s=0;s<t.columns;++s)e[r]+=t.get(r,s);return e}(this);case"column":return function(t){let e=p(t.columns);for(let r=0;r<t.rows;++r)for(let s=0;s<t.columns;++s)e[s]+=t.get(r,s);return e}(this);case void 0:return function(t){let e=0;for(let r=0;r<t.rows;r++)for(let s=0;s<t.columns;s++)e+=t.get(r,s);return e}(this);default:throw new Error(`invalid option: ${t}`)}}product(t){switch(t){case"row":return function(t){let e=p(t.rows,1);for(let r=0;r<t.rows;++r)for(let s=0;s<t.columns;++s)e[r]*=t.get(r,s);return e}(this);case"column":return function(t){let e=p(t.columns,1);for(let r=0;r<t.rows;++r)for(let s=0;s<t.columns;++s)e[s]*=t.get(r,s);return e}(this);case void 0:return function(t){let e=1;for(let r=0;r<t.rows;r++)for(let s=0;s<t.columns;s++)e*=t.get(r,s);return e}(this);default:throw new Error(`invalid option: ${t}`)}}mean(t){const e=this.sum(t);switch(t){case"row":for(let t=0;t<this.rows;t++)e[t]/=this.columns;return e;case"column":for(let t=0;t<this.columns;t++)e[t]/=this.rows;return e;case void 0:return e/this.size;default:throw new Error(`invalid option: ${t}`)}}variance(t,e={}){if("object"==typeof t&&(e=t,t=void 0),"object"!=typeof e)throw new TypeError("options must be an object");const{unbiased:s=!0,mean:o=this.mean(t)}=e;if("boolean"!=typeof s)throw new TypeError("unbiased must be a boolean");switch(t){case"row":if(!r(o))throw new TypeError("mean must be an array");return function(t,e,r){const s=t.rows,o=t.columns,i=[];for(let n=0;n<s;n++){let s=0,h=0,u=0;for(let e=0;e<o;e++)u=t.get(n,e)-r[n],s+=u,h+=u*u;e?i.push((h-s*s/o)/(o-1)):i.push((h-s*s/o)/o)}return i}(this,s,o);case"column":if(!r(o))throw new TypeError("mean must be an array");return function(t,e,r){const s=t.rows,o=t.columns,i=[];for(let n=0;n<o;n++){let o=0,h=0,u=0;for(let e=0;e<s;e++)u=t.get(e,n)-r[n],o+=u,h+=u*u;e?i.push((h-o*o/s)/(s-1)):i.push((h-o*o/s)/s)}return i}(this,s,o);case void 0:if("number"!=typeof o)throw new TypeError("mean must be a number");return function(t,e,r){const s=t.rows,o=t.columns,i=s*o;let n=0,h=0,u=0;for(let e=0;e<s;e++)for(let s=0;s<o;s++)u=t.get(e,s)-r,n+=u,h+=u*u;return e?(h-n*n/i)/(i-1):(h-n*n/i)/i}(this,s,o);default:throw new Error(`invalid option: ${t}`)}}standardDeviation(t,e){"object"==typeof t&&(e=t,t=void 0);const r=this.variance(t,e);if(void 0===t)return Math.sqrt(r);for(let t=0;t<r.length;t++)r[t]=Math.sqrt(r[t]);return r}center(t,e={}){if("object"==typeof t&&(e=t,t=void 0),"object"!=typeof e)throw new TypeError("options must be an object");const{center:s=this.mean(t)}=e;switch(t){case"row":if(!r(s))throw new TypeError("center must be an array");return function(t,e){for(let r=0;r<t.rows;r++)for(let s=0;s<t.columns;s++)t.set(r,s,t.get(r,s)-e[r])}(this,s),this;case"column":if(!r(s))throw new TypeError("center must be an array");return function(t,e){for(let r=0;r<t.rows;r++)for(let s=0;s<t.columns;s++)t.set(r,s,t.get(r,s)-e[s])}(this,s),this;case void 0:if("number"!=typeof s)throw new TypeError("center must be a number");return function(t,e){for(let r=0;r<t.rows;r++)for(let s=0;s<t.columns;s++)t.set(r,s,t.get(r,s)-e)}(this,s),this;default:throw new Error(`invalid option: ${t}`)}}scale(t,e={}){if("object"==typeof t&&(e=t,t=void 0),"object"!=typeof e)throw new TypeError("options must be an object");let s=e.scale;switch(t){case"row":if(void 0===s)s=function(t){const e=[];for(let r=0;r<t.rows;r++){let s=0;for(let e=0;e<t.columns;e++)s+=t.get(r,e)**2/(t.columns-1);e.push(Math.sqrt(s))}return e}(this);else if(!r(s))throw new TypeError("scale must be an array");return function(t,e){for(let r=0;r<t.rows;r++)for(let s=0;s<t.columns;s++)t.set(r,s,t.get(r,s)/e[r])}(this,s),this;case"column":if(void 0===s)s=function(t){const e=[];for(let r=0;r<t.columns;r++){let s=0;for(let e=0;e<t.rows;e++)s+=t.get(e,r)**2/(t.rows-1);e.push(Math.sqrt(s))}return e}(this);else if(!r(s))throw new TypeError("scale must be an array");return function(t,e){for(let r=0;r<t.rows;r++)for(let s=0;s<t.columns;s++)t.set(r,s,t.get(r,s)/e[s])}(this,s),this;case void 0:if(void 0===s)s=function(t){const e=t.size-1;let r=0;for(let s=0;s<t.columns;s++)for(let o=0;o<t.rows;o++)r+=t.get(o,s)**2/e;return Math.sqrt(r)}(this);else if("number"!=typeof s)throw new TypeError("scale must be a number");return function(t,e){for(let r=0;r<t.rows;r++)for(let s=0;s<t.columns;s++)t.set(r,s,t.get(r,s)/e)}(this,s),this;default:throw new Error(`invalid option: ${t}`)}}toString(t){return n(this,t)}[Symbol.iterator](){return this.entries()}*entries(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)yield[t,e,this.get(t,e)]}*values(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)yield this.get(t,e)}}function b(t,e){return t-e}M.prototype.klass="Matrix","undefined"!=typeof Symbol&&(M.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(){return n(this)}),M.random=M.rand,M.randomInt=M.randInt,M.diagonal=M.diag,M.prototype.diagonal=M.prototype.diag,M.identity=M.eye,M.prototype.negate=M.prototype.neg,M.prototype.tensorProduct=M.prototype.kroneckerProduct;class x extends M{data;#t(t,e){if(this.data=[],!(Number.isInteger(e)&&e>=0))throw new TypeError("nColumns must be a positive integer");for(let r=0;r<t;r++)this.data.push(new Float64Array(e));this.rows=t,this.columns=e}constructor(t,e){if(super(),x.isMatrix(t))this.#t(t.rows,t.columns),x.copy(t,this);else if(Number.isInteger(t)&&t>=0)this.#t(t,e);else{if(!r(t))throw new TypeError("First argument must be a positive number or an array");{const r=t;if("number"!=typeof(e=(t=r.length)?r[0].length:0))throw new TypeError("Data must be a 2D array with at least one element");this.data=[];for(let s=0;s<t;s++){if(r[s].length!==e)throw new RangeError("Inconsistent array dimensions");if(!r[s].every((t=>"number"==typeof t)))throw new TypeError("Input data contains non-numeric values");this.data.push(Float64Array.from(r[s]))}this.rows=t,this.columns=e}}}set(t,e,r){return this.data[t][e]=r,this}get(t,e){return this.data[t][e]}removeRow(t){return l(this,t),this.data.splice(t,1),this.rows-=1,this}addRow(t,e){return void 0===e&&(e=t,t=this.rows),l(this,t,!0),e=Float64Array.from(f(this,e)),this.data.splice(t,0,e),this.rows+=1,this}removeColumn(t){a(this,t);for(let e=0;e<this.rows;e++){const r=new Float64Array(this.columns-1);for(let s=0;s<t;s++)r[s]=this.data[e][s];for(let s=t+1;s<this.columns;s++)r[s-1]=this.data[e][s];this.data[e]=r}return this.columns-=1,this}addColumn(t,e){void 0===e&&(e=t,t=this.columns),a(this,t,!0),e=c(this,e);for(let r=0;r<this.rows;r++){const s=new Float64Array(this.columns+1);let o=0;for(;o<t;o++)s[o]=this.data[r][o];for(s[o++]=e[r];o<this.columns+1;o++)s[o]=this.data[r][o-1];this.data[r]=s}return this.columns+=1,this}}!function(t,e){t.prototype.add=function(t){return"number"==typeof t?this.addS(t):this.addM(t)},t.prototype.addS=function(t){for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)+t);return this},t.prototype.addM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)+t.get(e,r));return this},t.add=function(t,r){return new e(t).add(r)},t.prototype.sub=function(t){return"number"==typeof t?this.subS(t):this.subM(t)},t.prototype.subS=function(t){for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)-t);return this},t.prototype.subM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)-t.get(e,r));return this},t.sub=function(t,r){return new e(t).sub(r)},t.prototype.subtract=t.prototype.sub,t.prototype.subtractS=t.prototype.subS,t.prototype.subtractM=t.prototype.subM,t.subtract=t.sub,t.prototype.mul=function(t){return"number"==typeof t?this.mulS(t):this.mulM(t)},t.prototype.mulS=function(t){for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)*t);return this},t.prototype.mulM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)*t.get(e,r));return this},t.mul=function(t,r){return new e(t).mul(r)},t.prototype.multiply=t.prototype.mul,t.prototype.multiplyS=t.prototype.mulS,t.prototype.multiplyM=t.prototype.mulM,t.multiply=t.mul,t.prototype.div=function(t){return"number"==typeof t?this.divS(t):this.divM(t)},t.prototype.divS=function(t){for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)/t);return this},t.prototype.divM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)/t.get(e,r));return this},t.div=function(t,r){return new e(t).div(r)},t.prototype.divide=t.prototype.div,t.prototype.divideS=t.prototype.divS,t.prototype.divideM=t.prototype.divM,t.divide=t.div,t.prototype.mod=function(t){return"number"==typeof t?this.modS(t):this.modM(t)},t.prototype.modS=function(t){for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)%t);return this},t.prototype.modM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)%t.get(e,r));return this},t.mod=function(t,r){return new e(t).mod(r)},t.prototype.modulus=t.prototype.mod,t.prototype.modulusS=t.prototype.modS,t.prototype.modulusM=t.prototype.modM,t.modulus=t.mod,t.prototype.and=function(t){return"number"==typeof t?this.andS(t):this.andM(t)},t.prototype.andS=function(t){for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)&t);return this},t.prototype.andM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)&t.get(e,r));return this},t.and=function(t,r){return new e(t).and(r)},t.prototype.or=function(t){return"number"==typeof t?this.orS(t):this.orM(t)},t.prototype.orS=function(t){for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)|t);return this},t.prototype.orM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)|t.get(e,r));return this},t.or=function(t,r){return new e(t).or(r)},t.prototype.xor=function(t){return"number"==typeof t?this.xorS(t):this.xorM(t)},t.prototype.xorS=function(t){for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)^t);return this},t.prototype.xorM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)^t.get(e,r));return this},t.xor=function(t,r){return new e(t).xor(r)},t.prototype.leftShift=function(t){return"number"==typeof t?this.leftShiftS(t):this.leftShiftM(t)},t.prototype.leftShiftS=function(t){for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)<<t);return this},t.prototype.leftShiftM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)<<t.get(e,r));return this},t.leftShift=function(t,r){return new e(t).leftShift(r)},t.prototype.signPropagatingRightShift=function(t){return"number"==typeof t?this.signPropagatingRightShiftS(t):this.signPropagatingRightShiftM(t)},t.prototype.signPropagatingRightShiftS=function(t){for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)>>t);return this},t.prototype.signPropagatingRightShiftM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)>>t.get(e,r));return this},t.signPropagatingRightShift=function(t,r){return new e(t).signPropagatingRightShift(r)},t.prototype.rightShift=function(t){return"number"==typeof t?this.rightShiftS(t):this.rightShiftM(t)},t.prototype.rightShiftS=function(t){for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)>>>t);return this},t.prototype.rightShiftM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)>>>t.get(e,r));return this},t.rightShift=function(t,r){return new e(t).rightShift(r)},t.prototype.zeroFillRightShift=t.prototype.rightShift,t.prototype.zeroFillRightShiftS=t.prototype.rightShiftS,t.prototype.zeroFillRightShiftM=t.prototype.rightShiftM,t.zeroFillRightShift=t.rightShift,t.prototype.not=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,~this.get(t,e));return this},t.not=function(t){return new e(t).not()},t.prototype.abs=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.abs(this.get(t,e)));return this},t.abs=function(t){return new e(t).abs()},t.prototype.acos=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.acos(this.get(t,e)));return this},t.acos=function(t){return new e(t).acos()},t.prototype.acosh=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.acosh(this.get(t,e)));return this},t.acosh=function(t){return new e(t).acosh()},t.prototype.asin=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.asin(this.get(t,e)));return this},t.asin=function(t){return new e(t).asin()},t.prototype.asinh=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.asinh(this.get(t,e)));return this},t.asinh=function(t){return new e(t).asinh()},t.prototype.atan=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.atan(this.get(t,e)));return this},t.atan=function(t){return new e(t).atan()},t.prototype.atanh=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.atanh(this.get(t,e)));return this},t.atanh=function(t){return new e(t).atanh()},t.prototype.cbrt=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.cbrt(this.get(t,e)));return this},t.cbrt=function(t){return new e(t).cbrt()},t.prototype.ceil=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.ceil(this.get(t,e)));return this},t.ceil=function(t){return new e(t).ceil()},t.prototype.clz32=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.clz32(this.get(t,e)));return this},t.clz32=function(t){return new e(t).clz32()},t.prototype.cos=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.cos(this.get(t,e)));return this},t.cos=function(t){return new e(t).cos()},t.prototype.cosh=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.cosh(this.get(t,e)));return this},t.cosh=function(t){return new e(t).cosh()},t.prototype.exp=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.exp(this.get(t,e)));return this},t.exp=function(t){return new e(t).exp()},t.prototype.expm1=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.expm1(this.get(t,e)));return this},t.expm1=function(t){return new e(t).expm1()},t.prototype.floor=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.floor(this.get(t,e)));return this},t.floor=function(t){return new e(t).floor()},t.prototype.fround=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.fround(this.get(t,e)));return this},t.fround=function(t){return new e(t).fround()},t.prototype.log=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.log(this.get(t,e)));return this},t.log=function(t){return new e(t).log()},t.prototype.log1p=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.log1p(this.get(t,e)));return this},t.log1p=function(t){return new e(t).log1p()},t.prototype.log10=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.log10(this.get(t,e)));return this},t.log10=function(t){return new e(t).log10()},t.prototype.log2=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.log2(this.get(t,e)));return this},t.log2=function(t){return new e(t).log2()},t.prototype.round=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.round(this.get(t,e)));return this},t.round=function(t){return new e(t).round()},t.prototype.sign=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.sign(this.get(t,e)));return this},t.sign=function(t){return new e(t).sign()},t.prototype.sin=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.sin(this.get(t,e)));return this},t.sin=function(t){return new e(t).sin()},t.prototype.sinh=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.sinh(this.get(t,e)));return this},t.sinh=function(t){return new e(t).sinh()},t.prototype.sqrt=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.sqrt(this.get(t,e)));return this},t.sqrt=function(t){return new e(t).sqrt()},t.prototype.tan=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.tan(this.get(t,e)));return this},t.tan=function(t){return new e(t).tan()},t.prototype.tanh=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.tanh(this.get(t,e)));return this},t.tanh=function(t){return new e(t).tanh()},t.prototype.trunc=function(){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)this.set(t,e,Math.trunc(this.get(t,e)));return this},t.trunc=function(t){return new e(t).trunc()},t.pow=function(t,r){return new e(t).pow(r)},t.prototype.pow=function(t){return"number"==typeof t?this.powS(t):this.powM(t)},t.prototype.powS=function(t){for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)**t);return this},t.prototype.powM=function(t){if(t=e.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.set(e,r,this.get(e,r)**t.get(e,r));return this}}(M,x);class E extends M{#e;get size(){return this.#e.size}get rows(){return this.#e.rows}get columns(){return this.#e.columns}get diagonalSize(){return this.rows}static isSymmetricMatrix(t){return x.isMatrix(t)&&"SymmetricMatrix"===t.klassType}static zeros(t){return new this(t)}static ones(t){return new this(t).fill(1)}constructor(t){if(super(),x.isMatrix(t)){if(!t.isSymmetric())throw new TypeError("not symmetric data");this.#e=x.copy(t,new x(t.rows,t.rows))}else if(Number.isInteger(t)&&t>=0)this.#e=new x(t,t);else if(this.#e=new x(t),!this.isSymmetric())throw new TypeError("not symmetric data")}clone(){const t=new E(this.diagonalSize);for(const[e,r,s]of this.upperRightEntries())t.set(e,r,s);return t}toMatrix(){return new x(this)}get(t,e){return this.#e.get(t,e)}set(t,e,r){return this.#e.set(t,e,r),this.#e.set(e,t,r),this}removeCross(t){return this.#e.removeRow(t),this.#e.removeColumn(t),this}addCross(t,e){void 0===e&&(e=t,t=this.diagonalSize);const r=e.slice();return r.splice(t,1),this.#e.addRow(t,r),this.#e.addColumn(t,e),this}applyMask(t){if(t.length!==this.diagonalSize)throw new RangeError("Mask size do not match with matrix size");const e=[];for(const[r,s]of t.entries())s||e.push(r);e.reverse();for(const t of e)this.removeCross(t);return this}toCompact(){const{diagonalSize:t}=this,e=new Array(t*(t+1)/2);for(let r=0,s=0,o=0;o<e.length;o++)e[o]=this.get(s,r),++r>=t&&(r=++s);return e}static fromCompact(t){const e=t.length,r=(Math.sqrt(8*e+1)-1)/2;if(!Number.isInteger(r))throw new TypeError(`This array is not a compact representation of a Symmetric Matrix, ${JSON.stringify(t)}`);const s=new E(r);for(let o=0,i=0,n=0;n<e;n++)s.set(o,i,t[n]),++o>=r&&(o=++i);return s}*upperRightEntries(){for(let t=0,e=0;t<this.diagonalSize;void 0){const r=this.get(t,e);yield[t,e,r],++e>=this.diagonalSize&&(e=++t)}}*upperRightValues(){for(let t=0,e=0;t<this.diagonalSize;void 0){const r=this.get(t,e);yield r,++e>=this.diagonalSize&&(e=++t)}}}E.prototype.klassType="SymmetricMatrix";class S extends E{static isDistanceMatrix(t){return E.isSymmetricMatrix(t)&&"DistanceMatrix"===t.klassSubType}constructor(t){if(super(t),!this.isDistance())throw new TypeError("Provided arguments do no produce a distance matrix")}set(t,e,r){return t===e&&(r=0),super.set(t,e,r)}addCross(t,e){return void 0===e&&(e=t,t=this.diagonalSize),(e=e.slice())[t]=0,super.addCross(t,e)}toSymmetricMatrix(){return new E(this)}clone(){const t=new S(this.diagonalSize);for(const[e,r,s]of this.upperRightEntries())e!==r&&t.set(e,r,s);return t}toCompact(){const{diagonalSize:t}=this,e=new Array((t-1)*t/2);for(let r=1,s=0,o=0;o<e.length;o++)e[o]=this.get(s,r),++r>=t&&(r=1+ ++s);return e}static fromCompact(t){const e=t.length;if(0===e)return new this(0);const r=(Math.sqrt(8*e+1)+1)/2;if(!Number.isInteger(r))throw new TypeError(`This array is not a compact representation of a DistanceMatrix, ${JSON.stringify(t)}`);const s=new this(r);for(let o=1,i=0,n=0;n<e;n++)s.set(o,i,t[n]),++o>=r&&(o=1+ ++i);return s}}S.prototype.klassSubType="DistanceMatrix";class v extends M{constructor(t,e,r){super(),this.matrix=t,this.rows=e,this.columns=r}}class R extends v{constructor(t,e,r){m(t,e),g(t,r),super(t,e.length,r.length),this.rowIndices=e,this.columnIndices=r}set(t,e,r){return this.matrix.set(this.rowIndices[t],this.columnIndices[e],r),this}get(t,e){return this.matrix.get(this.rowIndices[t],this.columnIndices[e])}}class I extends M{constructor(t,e={}){const{rows:r=1}=e;if(t.length%r!=0)throw new Error("the data length is not divisible by the number of rows");super(),this.rows=r,this.columns=t.length/r,this.data=t}set(t,e,r){let s=this._calculateIndex(t,e);return this.data[s]=r,this}get(t,e){let r=this._calculateIndex(t,e);return this.data[r]}_calculateIndex(t,e){return t*this.columns+e}}class k extends M{constructor(t){super(),this.data=t,this.rows=t.length,this.columns=t[0].length}set(t,e,r){return this.data[t][e]=r,this}get(t,e){return this.data[t][e]}}class T{constructor(t){let e,r,s,o,i,n,h,u,l,a=(t=k.checkMatrix(t)).clone(),f=a.rows,c=a.columns,m=new Float64Array(f),g=1;for(e=0;e<f;e++)m[e]=e;for(u=new Float64Array(f),r=0;r<c;r++){for(e=0;e<f;e++)u[e]=a.get(e,r);for(e=0;e<f;e++){for(l=Math.min(e,r),i=0,s=0;s<l;s++)i+=a.get(e,s)*u[s];u[e]-=i,a.set(e,r,u[e])}for(o=r,e=r+1;e<f;e++)Math.abs(u[e])>Math.abs(u[o])&&(o=e);if(o!==r){for(s=0;s<c;s++)n=a.get(o,s),a.set(o,s,a.get(r,s)),a.set(r,s,n);h=m[o],m[o]=m[r],m[r]=h,g=-g}if(r<f&&0!==a.get(r,r))for(e=r+1;e<f;e++)a.set(e,r,a.get(e,r)/a.get(r,r))}this.LU=a,this.pivotVector=m,this.pivotSign=g}isSingular(){let t=this.LU,e=t.columns;for(let r=0;r<e;r++)if(0===t.get(r,r))return!0;return!1}solve(t){t=x.checkMatrix(t);let e=this.LU;if(e.rows!==t.rows)throw new Error("Invalid matrix dimensions");if(this.isSingular())throw new Error("LU matrix is singular");let r,s,o,i=t.columns,n=t.subMatrixRow(this.pivotVector,0,i-1),h=e.columns;for(o=0;o<h;o++)for(r=o+1;r<h;r++)for(s=0;s<i;s++)n.set(r,s,n.get(r,s)-n.get(o,s)*e.get(r,o));for(o=h-1;o>=0;o--){for(s=0;s<i;s++)n.set(o,s,n.get(o,s)/e.get(o,o));for(r=0;r<o;r++)for(s=0;s<i;s++)n.set(r,s,n.get(r,s)-n.get(o,s)*e.get(r,o))}return n}get determinant(){let t=this.LU;if(!t.isSquare())throw new Error("Matrix must be square");let e=this.pivotSign,r=t.columns;for(let s=0;s<r;s++)e*=t.get(s,s);return e}get lowerTriangularMatrix(){let t=this.LU,e=t.rows,r=t.columns,s=new x(e,r);for(let o=0;o<e;o++)for(let e=0;e<r;e++)o>e?s.set(o,e,t.get(o,e)):o===e?s.set(o,e,1):s.set(o,e,0);return s}get upperTriangularMatrix(){let t=this.LU,e=t.rows,r=t.columns,s=new x(e,r);for(let o=0;o<e;o++)for(let e=0;e<r;e++)o<=e?s.set(o,e,t.get(o,e)):s.set(o,e,0);return s}get pivotPermutationVector(){return Array.from(this.pivotVector)}}function N(t,e){let r=0;return Math.abs(t)>Math.abs(e)?(r=e/t,Math.abs(t)*Math.sqrt(1+r*r)):0!==e?(r=t/e,Math.abs(e)*Math.sqrt(1+r*r)):0}class V{constructor(t){let e,r,s,o,i=(t=k.checkMatrix(t)).clone(),n=t.rows,h=t.columns,u=new Float64Array(h);for(s=0;s<h;s++){let t=0;for(e=s;e<n;e++)t=N(t,i.get(e,s));if(0!==t){for(i.get(s,s)<0&&(t=-t),e=s;e<n;e++)i.set(e,s,i.get(e,s)/t);for(i.set(s,s,i.get(s,s)+1),r=s+1;r<h;r++){for(o=0,e=s;e<n;e++)o+=i.get(e,s)*i.get(e,r);for(o=-o/i.get(s,s),e=s;e<n;e++)i.set(e,r,i.get(e,r)+o*i.get(e,s))}}u[s]=-t}this.QR=i,this.Rdiag=u}solve(t){t=x.checkMatrix(t);let e=this.QR,r=e.rows;if(t.rows!==r)throw new Error("Matrix row dimensions must agree");if(!this.isFullRank())throw new Error("Matrix is rank deficient");let s,o,i,n,h=t.columns,u=t.clone(),l=e.columns;for(i=0;i<l;i++)for(o=0;o<h;o++){for(n=0,s=i;s<r;s++)n+=e.get(s,i)*u.get(s,o);for(n=-n/e.get(i,i),s=i;s<r;s++)u.set(s,o,u.get(s,o)+n*e.get(s,i))}for(i=l-1;i>=0;i--){for(o=0;o<h;o++)u.set(i,o,u.get(i,o)/this.Rdiag[i]);for(s=0;s<i;s++)for(o=0;o<h;o++)u.set(s,o,u.get(s,o)-u.get(i,o)*e.get(s,i))}return u.subMatrix(0,l-1,0,h-1)}isFullRank(){let t=this.QR.columns;for(let e=0;e<t;e++)if(0===this.Rdiag[e])return!1;return!0}get upperTriangularMatrix(){let t,e,r=this.QR,s=r.columns,o=new x(s,s);for(t=0;t<s;t++)for(e=0;e<s;e++)t<e?o.set(t,e,r.get(t,e)):t===e?o.set(t,e,this.Rdiag[t]):o.set(t,e,0);return o}get orthogonalMatrix(){let t,e,r,s,o=this.QR,i=o.rows,n=o.columns,h=new x(i,n);for(r=n-1;r>=0;r--){for(t=0;t<i;t++)h.set(t,r,0);for(h.set(r,r,1),e=r;e<n;e++)if(0!==o.get(r,r)){for(s=0,t=r;t<i;t++)s+=o.get(t,r)*h.get(t,e);for(s=-s/o.get(r,r),t=r;t<i;t++)h.set(t,e,h.get(t,e)+s*o.get(t,r))}}return h}}class C{constructor(t,e={}){if((t=k.checkMatrix(t)).isEmpty())throw new Error("Matrix must be non-empty");let r=t.rows,s=t.columns;const{computeLeftSingularVectors:o=!0,computeRightSingularVectors:i=!0,autoTranspose:n=!1}=e;let h,u=Boolean(o),l=Boolean(i),a=!1;if(r<s)if(n){h=t.transpose(),r=h.rows,s=h.columns,a=!0;let e=u;u=l,l=e}else h=t.clone(),console.warn("Computing SVD on a matrix with more columns than rows. Consider enabling autoTranspose");else h=t.clone();let f=Math.min(r,s),c=Math.min(r+1,s),m=new Float64Array(c),g=new x(r,f),w=new x(s,s),p=new Float64Array(s),d=new Float64Array(r),y=new Float64Array(c);for(let t=0;t<c;t++)y[t]=t;let M=Math.min(r-1,s),b=Math.max(0,Math.min(s-2,r)),E=Math.max(M,b);for(let t=0;t<E;t++){if(t<M){m[t]=0;for(let e=t;e<r;e++)m[t]=N(m[t],h.get(e,t));if(0!==m[t]){h.get(t,t)<0&&(m[t]=-m[t]);for(let e=t;e<r;e++)h.set(e,t,h.get(e,t)/m[t]);h.set(t,t,h.get(t,t)+1)}m[t]=-m[t]}for(let e=t+1;e<s;e++){if(t<M&&0!==m[t]){let s=0;for(let o=t;o<r;o++)s+=h.get(o,t)*h.get(o,e);s=-s/h.get(t,t);for(let o=t;o<r;o++)h.set(o,e,h.get(o,e)+s*h.get(o,t))}p[e]=h.get(t,e)}if(u&&t<M)for(let e=t;e<r;e++)g.set(e,t,h.get(e,t));if(t<b){p[t]=0;for(let e=t+1;e<s;e++)p[t]=N(p[t],p[e]);if(0!==p[t]){p[t+1]<0&&(p[t]=0-p[t]);for(let e=t+1;e<s;e++)p[e]/=p[t];p[t+1]+=1}if(p[t]=-p[t],t+1<r&&0!==p[t]){for(let e=t+1;e<r;e++)d[e]=0;for(let e=t+1;e<r;e++)for(let r=t+1;r<s;r++)d[e]+=p[r]*h.get(e,r);for(let e=t+1;e<s;e++){let s=-p[e]/p[t+1];for(let o=t+1;o<r;o++)h.set(o,e,h.get(o,e)+s*d[o])}}if(l)for(let e=t+1;e<s;e++)w.set(e,t,p[e])}}let S=Math.min(s,r+1);if(M<s&&(m[M]=h.get(M,M)),r<S&&(m[S-1]=0),b+1<S&&(p[b]=h.get(b,S-1)),p[S-1]=0,u){for(let t=M;t<f;t++){for(let e=0;e<r;e++)g.set(e,t,0);g.set(t,t,1)}for(let t=M-1;t>=0;t--)if(0!==m[t]){for(let e=t+1;e<f;e++){let s=0;for(let o=t;o<r;o++)s+=g.get(o,t)*g.get(o,e);s=-s/g.get(t,t);for(let o=t;o<r;o++)g.set(o,e,g.get(o,e)+s*g.get(o,t))}for(let e=t;e<r;e++)g.set(e,t,-g.get(e,t));g.set(t,t,1+g.get(t,t));for(let e=0;e<t-1;e++)g.set(e,t,0)}else{for(let e=0;e<r;e++)g.set(e,t,0);g.set(t,t,1)}}if(l)for(let t=s-1;t>=0;t--){if(t<b&&0!==p[t])for(let e=t+1;e<s;e++){let r=0;for(let o=t+1;o<s;o++)r+=w.get(o,t)*w.get(o,e);r=-r/w.get(t+1,t);for(let o=t+1;o<s;o++)w.set(o,e,w.get(o,e)+r*w.get(o,t))}for(let e=0;e<s;e++)w.set(e,t,0);w.set(t,t,1)}let v=S-1,R=Number.EPSILON;for(;S>0;){let t,e;for(t=S-2;t>=-1&&-1!==t;t--){const e=Number.MIN_VALUE+R*Math.abs(m[t]+Math.abs(m[t+1]));if(Math.abs(p[t])<=e||Number.isNaN(p[t])){p[t]=0;break}}if(t===S-2)e=4;else{let r;for(r=S-1;r>=t&&r!==t;r--){let e=(r!==S?Math.abs(p[r]):0)+(r!==t+1?Math.abs(p[r-1]):0);if(Math.abs(m[r])<=R*e){m[r]=0;break}}r===t?e=3:r===S-1?e=1:(e=2,t=r)}switch(t++,e){case 1:{let e=p[S-2];p[S-2]=0;for(let r=S-2;r>=t;r--){let o=N(m[r],e),i=m[r]/o,n=e/o;if(m[r]=o,r!==t&&(e=-n*p[r-1],p[r-1]=i*p[r-1]),l)for(let t=0;t<s;t++)o=i*w.get(t,r)+n*w.get(t,S-1),w.set(t,S-1,-n*w.get(t,r)+i*w.get(t,S-1)),w.set(t,r,o)}break}case 2:{let e=p[t-1];p[t-1]=0;for(let s=t;s<S;s++){let o=N(m[s],e),i=m[s]/o,n=e/o;if(m[s]=o,e=-n*p[s],p[s]=i*p[s],u)for(let e=0;e<r;e++)o=i*g.get(e,s)+n*g.get(e,t-1),g.set(e,t-1,-n*g.get(e,s)+i*g.get(e,t-1)),g.set(e,s,o)}break}case 3:{const e=Math.max(Math.abs(m[S-1]),Math.abs(m[S-2]),Math.abs(p[S-2]),Math.abs(m[t]),Math.abs(p[t])),o=m[S-1]/e,i=m[S-2]/e,n=p[S-2]/e,h=m[t]/e,a=p[t]/e,f=((i+o)*(i-o)+n*n)/2,c=o*n*(o*n);let d=0;0===f&&0===c||(d=f<0?0-Math.sqrt(f*f+c):Math.sqrt(f*f+c),d=c/(f+d));let y=(h+o)*(h-o)+d,M=h*a;for(let e=t;e<S-1;e++){let o=N(y,M);0===o&&(o=Number.MIN_VALUE);let i=y/o,n=M/o;if(e!==t&&(p[e-1]=o),y=i*m[e]+n*p[e],p[e]=i*p[e]-n*m[e],M=n*m[e+1],m[e+1]=i*m[e+1],l)for(let t=0;t<s;t++)o=i*w.get(t,e)+n*w.get(t,e+1),w.set(t,e+1,-n*w.get(t,e)+i*w.get(t,e+1)),w.set(t,e,o);if(o=N(y,M),0===o&&(o=Number.MIN_VALUE),i=y/o,n=M/o,m[e]=o,y=i*p[e]+n*m[e+1],m[e+1]=-n*p[e]+i*m[e+1],M=n*p[e+1],p[e+1]=i*p[e+1],u&&e<r-1)for(let t=0;t<r;t++)o=i*g.get(t,e)+n*g.get(t,e+1),g.set(t,e+1,-n*g.get(t,e)+i*g.get(t,e+1)),g.set(t,e,o)}p[S-2]=y;break}case 4:if(m[t]<=0&&(m[t]=m[t]<0?-m[t]:0,l))for(let e=0;e<=v;e++)w.set(e,t,-w.get(e,t));for(;t<v&&!(m[t]>=m[t+1]);){let e=m[t];if(m[t]=m[t+1],m[t+1]=e,l&&t<s-1)for(let r=0;r<s;r++)e=w.get(r,t+1),w.set(r,t+1,w.get(r,t)),w.set(r,t,e);if(u&&t<r-1)for(let s=0;s<r;s++)e=g.get(s,t+1),g.set(s,t+1,g.get(s,t)),g.set(s,t,e);t++}S--}}if(a){let t=w;w=g,g=t}this.m=r,this.n=s,this.s=m,this.U=g,this.V=w}solve(t){let e=t,r=this.threshold,s=this.s.length,o=x.zeros(s,s);for(let t=0;t<s;t++)Math.abs(this.s[t])<=r?o.set(t,t,0):o.set(t,t,1/this.s[t]);let i=this.U,n=this.rightSingularVectors,h=n.mmul(o),u=n.rows,l=i.rows,a=x.zeros(u,l);for(let t=0;t<u;t++)for(let e=0;e<l;e++){let r=0;for(let o=0;o<s;o++)r+=h.get(t,o)*i.get(e,o);a.set(t,e,r)}return a.mmul(e)}solveForDiagonal(t){return this.solve(x.diag(t))}inverse(){let t=this.V,e=this.threshold,r=t.rows,s=t.columns,o=new x(r,this.s.length);for(let i=0;i<r;i++)for(let r=0;r<s;r++)Math.abs(this.s[r])>e&&o.set(i,r,t.get(i,r)/this.s[r]);let i=this.U,n=i.rows,h=i.columns,u=new x(r,n);for(let t=0;t<r;t++)for(let e=0;e<n;e++){let r=0;for(let s=0;s<h;s++)r+=o.get(t,s)*i.get(e,s);u.set(t,e,r)}return u}get condition(){return this.s[0]/this.s[Math.min(this.m,this.n)-1]}get norm2(){return this.s[0]}get rank(){let t=Math.max(this.m,this.n)*this.s[0]*Number.EPSILON,e=0,r=this.s;for(let s=0,o=r.length;s<o;s++)r[s]>t&&e++;return e}get diagonal(){return Array.from(this.s)}get threshold(){return Number.EPSILON/2*Math.max(this.m,this.n)*this.s[0]}get leftSingularVectors(){return this.U}get rightSingularVectors(){return this.V}get diagonalMatrix(){return x.diag(this.s)}}function q(t,e,r=!1){return t=k.checkMatrix(t),e=k.checkMatrix(e),r?new C(t).solve(e):t.isSquare()?new T(t).solve(e):new V(t).solve(e)}function A(t,e){let r=[];for(let s=0;s<t;s++)s!==e&&r.push(s);return r}function D(t,e,r,s=1e-9,o=1e-9){if(t>o)return new Array(e.rows+1).fill(0);{let t=e.addRow(r,[0]);for(let e=0;e<t.rows;e++)Math.abs(t.get(e,0))<s&&t.set(e,0,0);return t.to1DArray()}}class F{constructor(t,e={}){const{assumeSymmetric:r=!1}=e;if(!(t=k.checkMatrix(t)).isSquare())throw new Error("Matrix is not a square matrix");if(t.isEmpty())throw new Error("Matrix must be non-empty");let s,o,i=t.columns,n=new x(i,i),h=new Float64Array(i),u=new Float64Array(i),l=t,a=!1;if(a=!!r||t.isSymmetric(),a){for(s=0;s<i;s++)for(o=0;o<i;o++)n.set(s,o,l.get(s,o));!function(t,e,r,s){let o,i,n,h,u,l,a,f;for(u=0;u<t;u++)r[u]=s.get(t-1,u);for(h=t-1;h>0;h--){for(f=0,n=0,l=0;l<h;l++)f+=Math.abs(r[l]);if(0===f)for(e[h]=r[h-1],u=0;u<h;u++)r[u]=s.get(h-1,u),s.set(h,u,0),s.set(u,h,0);else{for(l=0;l<h;l++)r[l]/=f,n+=r[l]*r[l];for(o=r[h-1],i=Math.sqrt(n),o>0&&(i=-i),e[h]=f*i,n-=o*i,r[h-1]=o-i,u=0;u<h;u++)e[u]=0;for(u=0;u<h;u++){for(o=r[u],s.set(u,h,o),i=e[u]+s.get(u,u)*o,l=u+1;l<=h-1;l++)i+=s.get(l,u)*r[l],e[l]+=s.get(l,u)*o;e[u]=i}for(o=0,u=0;u<h;u++)e[u]/=n,o+=e[u]*r[u];for(a=o/(n+n),u=0;u<h;u++)e[u]-=a*r[u];for(u=0;u<h;u++){for(o=r[u],i=e[u],l=u;l<=h-1;l++)s.set(l,u,s.get(l,u)-(o*e[l]+i*r[l]));r[u]=s.get(h-1,u),s.set(h,u,0)}}r[h]=n}for(h=0;h<t-1;h++){if(s.set(t-1,h,s.get(h,h)),s.set(h,h,1),n=r[h+1],0!==n){for(l=0;l<=h;l++)r[l]=s.get(l,h+1)/n;for(u=0;u<=h;u++){for(i=0,l=0;l<=h;l++)i+=s.get(l,h+1)*s.get(l,u);for(l=0;l<=h;l++)s.set(l,u,s.get(l,u)-i*r[l])}}for(l=0;l<=h;l++)s.set(l,h+1,0)}for(u=0;u<t;u++)r[u]=s.get(t-1,u),s.set(t-1,u,0);s.set(t-1,t-1,1),e[0]=0}(i,u,h,n),function(t,e,r,s){let o,i,n,h,u,l,a,f,c,m,g,w,p,d,y,M;for(n=1;n<t;n++)e[n-1]=e[n];e[t-1]=0;let b=0,x=0,E=Number.EPSILON;for(l=0;l<t;l++){for(x=Math.max(x,Math.abs(r[l])+Math.abs(e[l])),a=l;a<t&&!(Math.abs(e[a])<=E*x);)a++;if(a>l)do{for(o=r[l],f=(r[l+1]-o)/(2*e[l]),c=N(f,1),f<0&&(c=-c),r[l]=e[l]/(f+c),r[l+1]=e[l]*(f+c),m=r[l+1],i=o-r[l],n=l+2;n<t;n++)r[n]-=i;for(b+=i,f=r[a],g=1,w=g,p=g,d=e[l+1],y=0,M=0,n=a-1;n>=l;n--)for(p=w,w=g,M=y,o=g*e[n],i=g*f,c=N(f,e[n]),e[n+1]=y*c,y=e[n]/c,g=f/c,f=g*r[n]-y*o,r[n+1]=i+y*(g*o+y*r[n]),u=0;u<t;u++)i=s.get(u,n+1),s.set(u,n+1,y*s.get(u,n)+g*i),s.set(u,n,g*s.get(u,n)-y*i);f=-y*M*p*d*e[l]/m,e[l]=y*f,r[l]=g*f}while(Math.abs(e[l])>E*x);r[l]=r[l]+b,e[l]=0}for(n=0;n<t-1;n++){for(u=n,f=r[n],h=n+1;h<t;h++)r[h]<f&&(u=h,f=r[h]);if(u!==n)for(r[u]=r[n],r[n]=f,h=0;h<t;h++)f=s.get(h,n),s.set(h,n,s.get(h,u)),s.set(h,u,f)}}(i,u,h,n)}else{let t=new x(i,i),e=new Float64Array(i);for(o=0;o<i;o++)for(s=0;s<i;s++)t.set(s,o,l.get(s,o));!function(t,e,r,s){let o,i,n,h,u,l,a,f=0,c=t-1;for(l=f+1;l<=c-1;l++){for(a=0,h=l;h<=c;h++)a+=Math.abs(e.get(h,l-1));if(0!==a){for(n=0,h=c;h>=l;h--)r[h]=e.get(h,l-1)/a,n+=r[h]*r[h];for(i=Math.sqrt(n),r[l]>0&&(i=-i),n-=r[l]*i,r[l]=r[l]-i,u=l;u<t;u++){for(o=0,h=c;h>=l;h--)o+=r[h]*e.get(h,u);for(o/=n,h=l;h<=c;h++)e.set(h,u,e.get(h,u)-o*r[h])}for(h=0;h<=c;h++){for(o=0,u=c;u>=l;u--)o+=r[u]*e.get(h,u);for(o/=n,u=l;u<=c;u++)e.set(h,u,e.get(h,u)-o*r[u])}r[l]=a*r[l],e.set(l,l-1,a*i)}}for(h=0;h<t;h++)for(u=0;u<t;u++)s.set(h,u,h===u?1:0);for(l=c-1;l>=f+1;l--)if(0!==e.get(l,l-1)){for(h=l+1;h<=c;h++)r[h]=e.get(h,l-1);for(u=l;u<=c;u++){for(i=0,h=l;h<=c;h++)i+=r[h]*s.get(h,u);for(i=i/r[l]/e.get(l,l-1),h=l;h<=c;h++)s.set(h,u,s.get(h,u)+i*r[h])}}}(i,t,e,n),function(t,e,r,s,o){let i,n,h,u,l,a,f,c,m,g,w,p,d,y,M,b=t-1,x=0,E=t-1,S=Number.EPSILON,v=0,R=0,I=0,k=0,T=0,N=0,V=0,C=0;for(i=0;i<t;i++)for((i<x||i>E)&&(r[i]=o.get(i,i),e[i]=0),n=Math.max(i-1,0);n<t;n++)R+=Math.abs(o.get(i,n));for(;b>=x;){for(u=b;u>x&&(N=Math.abs(o.get(u-1,u-1))+Math.abs(o.get(u,u)),0===N&&(N=R),!(Math.abs(o.get(u,u-1))<S*N));)u--;if(u===b)o.set(b,b,o.get(b,b)+v),r[b]=o.get(b,b),e[b]=0,b--,C=0;else if(u===b-1){if(f=o.get(b,b-1)*o.get(b-1,b),I=(o.get(b-1,b-1)-o.get(b,b))/2,k=I*I+f,V=Math.sqrt(Math.abs(k)),o.set(b,b,o.get(b,b)+v),o.set(b-1,b-1,o.get(b-1,b-1)+v),c=o.get(b,b),k>=0){for(V=I>=0?I+V:I-V,r[b-1]=c+V,r[b]=r[b-1],0!==V&&(r[b]=c-f/V),e[b-1]=0,e[b]=0,c=o.get(b,b-1),N=Math.abs(c)+Math.abs(V),I=c/N,k=V/N,T=Math.sqrt(I*I+k*k),I/=T,k/=T,n=b-1;n<t;n++)V=o.get(b-1,n),o.set(b-1,n,k*V+I*o.get(b,n)),o.set(b,n,k*o.get(b,n)-I*V);for(i=0;i<=b;i++)V=o.get(i,b-1),o.set(i,b-1,k*V+I*o.get(i,b)),o.set(i,b,k*o.get(i,b)-I*V);for(i=x;i<=E;i++)V=s.get(i,b-1),s.set(i,b-1,k*V+I*s.get(i,b)),s.set(i,b,k*s.get(i,b)-I*V)}else r[b-1]=c+I,r[b]=c+I,e[b-1]=V,e[b]=-V;b-=2,C=0}else{if(c=o.get(b,b),m=0,f=0,u<b&&(m=o.get(b-1,b-1),f=o.get(b,b-1)*o.get(b-1,b)),10===C){for(v+=c,i=x;i<=b;i++)o.set(i,i,o.get(i,i)-c);N=Math.abs(o.get(b,b-1))+Math.abs(o.get(b-1,b-2)),c=m=.75*N,f=-.4375*N*N}if(30===C&&(N=(m-c)/2,N=N*N+f,N>0)){for(N=Math.sqrt(N),m<c&&(N=-N),N=c-f/((m-c)/2+N),i=x;i<=b;i++)o.set(i,i,o.get(i,i)-N);v+=N,c=m=f=.964}for(C+=1,l=b-2;l>=u&&(V=o.get(l,l),T=c-V,N=m-V,I=(T*N-f)/o.get(l+1,l)+o.get(l,l+1),k=o.get(l+1,l+1)-V-T-N,T=o.get(l+2,l+1),N=Math.abs(I)+Math.abs(k)+Math.abs(T),I/=N,k/=N,T/=N,l!==u)&&!(Math.abs(o.get(l,l-1))*(Math.abs(k)+Math.abs(T))<S*(Math.abs(I)*(Math.abs(o.get(l-1,l-1))+Math.abs(V)+Math.abs(o.get(l+1,l+1)))));)l--;for(i=l+2;i<=b;i++)o.set(i,i-2,0),i>l+2&&o.set(i,i-3,0);for(h=l;h<=b-1&&(y=h!==b-1,h!==l&&(I=o.get(h,h-1),k=o.get(h+1,h-1),T=y?o.get(h+2,h-1):0,c=Math.abs(I)+Math.abs(k)+Math.abs(T),0!==c&&(I/=c,k/=c,T/=c)),0!==c);h++)if(N=Math.sqrt(I*I+k*k+T*T),I<0&&(N=-N),0!==N){for(h!==l?o.set(h,h-1,-N*c):u!==l&&o.set(h,h-1,-o.get(h,h-1)),I+=N,c=I/N,m=k/N,V=T/N,k/=I,T/=I,n=h;n<t;n++)I=o.get(h,n)+k*o.get(h+1,n),y&&(I+=T*o.get(h+2,n),o.set(h+2,n,o.get(h+2,n)-I*V)),o.set(h,n,o.get(h,n)-I*c),o.set(h+1,n,o.get(h+1,n)-I*m);for(i=0;i<=Math.min(b,h+3);i++)I=c*o.get(i,h)+m*o.get(i,h+1),y&&(I+=V*o.get(i,h+2),o.set(i,h+2,o.get(i,h+2)-I*T)),o.set(i,h,o.get(i,h)-I),o.set(i,h+1,o.get(i,h+1)-I*k);for(i=x;i<=E;i++)I=c*s.get(i,h)+m*s.get(i,h+1),y&&(I+=V*s.get(i,h+2),s.set(i,h+2,s.get(i,h+2)-I*T)),s.set(i,h,s.get(i,h)-I),s.set(i,h+1,s.get(i,h+1)-I*k)}}}if(0===R)return;for(b=t-1;b>=0;b--)if(I=r[b],k=e[b],0===k)for(u=b,o.set(b,b,1),i=b-1;i>=0;i--){for(f=o.get(i,i)-I,T=0,n=u;n<=b;n++)T+=o.get(i,n)*o.get(n,b);if(e[i]<0)V=f,N=T;else if(u=i,0===e[i]?o.set(i,b,0!==f?-T/f:-T/(S*R)):(c=o.get(i,i+1),m=o.get(i+1,i),k=(r[i]-I)*(r[i]-I)+e[i]*e[i],a=(c*N-V*T)/k,o.set(i,b,a),o.set(i+1,b,Math.abs(c)>Math.abs(V)?(-T-f*a)/c:(-N-m*a)/V)),a=Math.abs(o.get(i,b)),S*a*a>1)for(n=i;n<=b;n++)o.set(n,b,o.get(n,b)/a)}else if(k<0)for(u=b-1,Math.abs(o.get(b,b-1))>Math.abs(o.get(b-1,b))?(o.set(b-1,b-1,k/o.get(b,b-1)),o.set(b-1,b,-(o.get(b,b)-I)/o.get(b,b-1))):(M=z(0,-o.get(b-1,b),o.get(b-1,b-1)-I,k),o.set(b-1,b-1,M[0]),o.set(b-1,b,M[1])),o.set(b,b-1,0),o.set(b,b,1),i=b-2;i>=0;i--){for(g=0,w=0,n=u;n<=b;n++)g+=o.get(i,n)*o.get(n,b-1),w+=o.get(i,n)*o.get(n,b);if(f=o.get(i,i)-I,e[i]<0)V=f,T=g,N=w;else if(u=i,0===e[i]?(M=z(-g,-w,f,k),o.set(i,b-1,M[0]),o.set(i,b,M[1])):(c=o.get(i,i+1),m=o.get(i+1,i),p=(r[i]-I)*(r[i]-I)+e[i]*e[i]-k*k,d=2*(r[i]-I)*k,0===p&&0===d&&(p=S*R*(Math.abs(f)+Math.abs(k)+Math.abs(c)+Math.abs(m)+Math.abs(V))),M=z(c*T-V*g+k*w,c*N-V*w-k*g,p,d),o.set(i,b-1,M[0]),o.set(i,b,M[1]),Math.abs(c)>Math.abs(V)+Math.abs(k)?(o.set(i+1,b-1,(-g-f*o.get(i,b-1)+k*o.get(i,b))/c),o.set(i+1,b,(-w-f*o.get(i,b)-k*o.get(i,b-1))/c)):(M=z(-T-m*o.get(i,b-1),-N-m*o.get(i,b),V,k),o.set(i+1,b-1,M[0]),o.set(i+1,b,M[1]))),a=Math.max(Math.abs(o.get(i,b-1)),Math.abs(o.get(i,b))),S*a*a>1)for(n=i;n<=b;n++)o.set(n,b-1,o.get(n,b-1)/a),o.set(n,b,o.get(n,b)/a)}for(i=0;i<t;i++)if(i<x||i>E)for(n=i;n<t;n++)s.set(i,n,o.get(i,n));for(n=t-1;n>=x;n--)for(i=x;i<=E;i++){for(V=0,h=x;h<=Math.min(n,E);h++)V+=s.get(i,h)*o.get(h,n);s.set(i,n,V)}}(i,u,h,n,t)}this.n=i,this.e=u,this.d=h,this.V=n}get realEigenvalues(){return Array.from(this.d)}get imaginaryEigenvalues(){return Array.from(this.e)}get eigenvectorMatrix(){return this.V}get diagonalMatrix(){let t,e,r=this.n,s=this.e,o=this.d,i=new x(r,r);for(t=0;t<r;t++){for(e=0;e<r;e++)i.set(t,e,0);i.set(t,t,o[t]),s[t]>0?i.set(t,t+1,s[t]):s[t]<0&&i.set(t,t-1,s[t])}return i}}function z(t,e,r,s){let o,i;return Math.abs(r)>Math.abs(s)?(o=s/r,i=r+o*s,[(t+o*e)/i,(e-o*t)/i]):(o=r/s,i=s+o*r,[(o*t+e)/i,(o*e-t)/i])}class ${constructor(t){if(!(t=k.checkMatrix(t)).isSymmetric())throw new Error("Matrix is not symmetric");let e,r,s,o=t,i=o.rows,n=new x(i,i),h=!0;for(r=0;r<i;r++){let t=0;for(s=0;s<r;s++){let i=0;for(e=0;e<s;e++)i+=n.get(s,e)*n.get(r,e);i=(o.get(r,s)-i)/n.get(s,s),n.set(r,s,i),t+=i*i}for(t=o.get(r,r)-t,h&&=t>0,n.set(r,r,Math.sqrt(Math.max(t,0))),s=r+1;s<i;s++)n.set(r,s,0)}this.L=n,this.positiveDefinite=h}isPositiveDefinite(){return this.positiveDefinite}solve(t){t=k.checkMatrix(t);let e=this.L,r=e.rows;if(t.rows!==r)throw new Error("Matrix dimensions do not match");if(!1===this.isPositiveDefinite())throw new Error("Matrix is not positive definite");let s,o,i,n=t.columns,h=t.clone();for(i=0;i<r;i++)for(o=0;o<n;o++){for(s=0;s<i;s++)h.set(i,o,h.get(i,o)-h.get(s,o)*e.get(i,s));h.set(i,o,h.get(i,o)/e.get(i,i))}for(i=r-1;i>=0;i--)for(o=0;o<n;o++){for(s=i+1;s<r;s++)h.set(i,o,h.get(i,o)-h.get(s,o)*e.get(s,i));h.set(i,o,h.get(i,o)/e.get(i,i))}return h}get lowerTriangularMatrix(){return this.L}}class j{constructor(t,e={}){t=k.checkMatrix(t);let{Y:s}=e;const{scaleScores:o=!1,maxIterations:i=1e3,terminationCriteria:n=1e-10}=e;let h;if(s){if(s=r(s)&&"number"==typeof s[0]?x.columnVector(s):k.checkMatrix(s),s.rows!==t.rows)throw new Error("Y should have the same number of rows as X");h=s.getColumnVector(0)}else h=t.getColumnVector(0);let u,l,a,f,c=1;for(let e=0;e<i&&c>n;e++)a=t.transpose().mmul(h).div(h.transpose().mmul(h).get(0,0)),a=a.div(a.norm()),u=t.mmul(a).div(a.transpose().mmul(a).get(0,0)),e>0&&(c=u.clone().sub(f).pow(2).sum()),f=u.clone(),s?(l=s.transpose().mmul(u).div(u.transpose().mmul(u).get(0,0)),l=l.div(l.norm()),h=s.mmul(l).div(l.transpose().mmul(l).get(0,0))):h=u;if(s){let e=t.transpose().mmul(u).div(u.transpose().mmul(u).get(0,0));e=e.div(e.norm());let r=t.clone().sub(u.clone().mmul(e.transpose())),o=h.transpose().mmul(u).div(u.transpose().mmul(u).get(0,0)),i=s.clone().sub(u.clone().mulS(o.get(0,0)).mmul(l.transpose()));this.t=u,this.p=e.transpose(),this.w=a.transpose(),this.q=l,this.u=h,this.s=u.transpose().mmul(u),this.xResidual=r,this.yResidual=i,this.betas=o}else this.w=a.transpose(),this.s=u.transpose().mmul(u).sqrt(),this.t=o?u.clone().div(this.s.get(0,0)):u,this.xResidual=t.sub(u.mmul(a.transpose()))}}t.AbstractMatrix=M,t.CHO=$,t.CholeskyDecomposition=$,t.DistanceMatrix=S,t.EVD=F,t.EigenvalueDecomposition=F,t.LU=T,t.LuDecomposition=T,t.Matrix=x,t.MatrixColumnSelectionView=class extends v{constructor(t,e){g(t,e),super(t,t.rows,e.length),this.columnIndices=e}set(t,e,r){return this.matrix.set(t,this.columnIndices[e],r),this}get(t,e){return this.matrix.get(t,this.columnIndices[e])}},t.MatrixColumnView=class extends v{constructor(t,e){a(t,e),super(t,t.rows,1),this.column=e}set(t,e,r){return this.matrix.set(t,this.column,r),this}get(t){return this.matrix.get(t,this.column)}},t.MatrixFlipColumnView=class extends v{constructor(t){super(t,t.rows,t.columns)}set(t,e,r){return this.matrix.set(t,this.columns-e-1,r),this}get(t,e){return this.matrix.get(t,this.columns-e-1)}},t.MatrixFlipRowView=class extends v{constructor(t){super(t,t.rows,t.columns)}set(t,e,r){return this.matrix.set(this.rows-t-1,e,r),this}get(t,e){return this.matrix.get(this.rows-t-1,e)}},t.MatrixRowSelectionView=class extends v{constructor(t,e){m(t,e),super(t,e.length,t.columns),this.rowIndices=e}set(t,e,r){return this.matrix.set(this.rowIndices[t],e,r),this}get(t,e){return this.matrix.get(this.rowIndices[t],e)}},t.MatrixRowView=class extends v{constructor(t,e){l(t,e),super(t,1,t.columns),this.row=e}set(t,e,r){return this.matrix.set(this.row,e,r),this}get(t,e){return this.matrix.get(this.row,e)}},t.MatrixSelectionView=R,t.MatrixSubView=class extends v{constructor(t,e,r,s,o){w(t,e,r,s,o),super(t,r-e+1,o-s+1),this.startRow=e,this.startColumn=s}set(t,e,r){return this.matrix.set(this.startRow+t,this.startColumn+e,r),this}get(t,e){return this.matrix.get(this.startRow+t,this.startColumn+e)}},t.MatrixTransposeView=class extends v{constructor(t){super(t,t.columns,t.rows)}set(t,e,r){return this.matrix.set(e,t,r),this}get(t,e){return this.matrix.get(e,t)}},t.NIPALS=j,t.Nipals=j,t.QR=V,t.QrDecomposition=V,t.SVD=C,t.SingularValueDecomposition=C,t.SymmetricMatrix=E,t.WrapperMatrix1D=I,t.WrapperMatrix2D=k,t.correlation=function(t,e=t,s={}){t=new x(t);let o=!1;if("object"!=typeof e||x.isMatrix(e)||r(e)?e=new x(e):(s=e,e=t,o=!0),t.rows!==e.rows)throw new TypeError("Both matrices must have the same number of rows");const{center:i=!0,scale:n=!0}=s;i&&(t.center("column"),o||e.center("column")),n&&(t.scale("column"),o||e.scale("column"));const h=t.standardDeviation("column",{unbiased:!0}),u=o?h:e.standardDeviation("column",{unbiased:!0}),l=t.transpose().mmul(e);for(let e=0;e<l.rows;e++)for(let r=0;r<l.columns;r++)l.set(e,r,l.get(e,r)*(1/(h[e]*u[r]))*(1/(t.rows-1)));return l},t.covariance=function(t,e=t,s={}){t=new x(t);let o=!1;if("object"!=typeof e||x.isMatrix(e)||r(e)?e=new x(e):(s=e,e=t,o=!0),t.rows!==e.rows)throw new TypeError("Both matrices must have the same number of rows");const{center:i=!0}=s;i&&(t=t.center("column"),o||(e=e.center("column")));const n=t.transpose().mmul(e);for(let e=0;e<n.rows;e++)for(let r=0;r<n.columns;r++)n.set(e,r,n.get(e,r)*(1/(t.rows-1)));return n},t.default=x,t.determinant=function t(e){if((e=x.checkMatrix(e)).isSquare()){if(0===e.columns)return 1;let r,s,o,i;if(2===e.columns)return r=e.get(0,0),s=e.get(0,1),o=e.get(1,0),i=e.get(1,1),r*i-s*o;if(3===e.columns){let i,n,h;return i=new R(e,[1,2],[1,2]),n=new R(e,[1,2],[0,2]),h=new R(e,[1,2],[0,1]),r=e.get(0,0),s=e.get(0,1),o=e.get(0,2),r*t(i)-s*t(n)+o*t(h)}return new T(e).determinant}throw Error("determinant can only be calculated for a square matrix")},t.inverse=function(t,e=!1){return t=k.checkMatrix(t),e?new C(t).inverse():q(t,x.eye(t.rows))},t.linearDependencies=function(t,e={}){const{thresholdValue:r=1e-9,thresholdError:s=1e-9}=e;let o=(t=x.checkMatrix(t)).rows,i=new x(o,o);for(let e=0;e<o;e++){let n=x.columnVector(t.getRow(e)),h=t.subMatrixRow(A(o,e)).transpose(),u=new C(h).solve(n),l=x.sub(n,h.mmul(u)).abs().max();i.setRow(e,D(l,u,e,r,s))}return i},t.pseudoInverse=function(t,e=Number.EPSILON){if((t=x.checkMatrix(t)).isEmpty())return t.transpose();let r=new C(t,{autoTranspose:!0}),s=r.leftSingularVectors,o=r.rightSingularVectors,i=r.diagonal;for(let t=0;t<i.length;t++)Math.abs(i[t])>e?i[t]=1/i[t]:i[t]=0;return o.mmul(x.diag(i).mmul(s.transpose()))},t.solve=q,t.wrap=function(t,e){if(r(t))return t[0]&&r(t[0])?new k(t):new I(t,e);throw new Error("the argument is not an array")},Object.defineProperty(t,"__esModule",{value:!0})}));
