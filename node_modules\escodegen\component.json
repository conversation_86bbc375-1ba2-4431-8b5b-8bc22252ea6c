{"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/Constellation/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "version": "1.3.3", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://github.com/Constellation"}], "repository": {"type": "git", "url": "http://github.com/Constellation/escodegen.git"}, "dependencies": {"esutils": "~1.0.0", "estraverse": "~1.5.0", "esprima": "~1.1.1"}, "optionalDependencies": {}, "devDependencies": {"esprima-moz": "*", "semver": "*", "chai": "~1.7.2", "gulp": "~3.5.0", "gulp-mocha": "~0.4.1", "gulp-eslint": "~0.1.2", "jshint-stylish": "~0.1.5", "gulp-jshint": "~1.4.0", "commonjs-everywhere": "~0.9.6", "bluebird": "~1.2.0", "bower-registry-client": "~0.2.0"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/escodegen/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "./node_modules/.bin/cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "./node_modules/.bin/cjsify -a path: tools/entry-point.js > escodegen.browser.js"}}