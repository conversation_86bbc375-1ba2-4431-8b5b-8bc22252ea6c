# 微信小程序惯导定位库

一个高性能、易用的室内惯导定位库，专为微信小程序平台优化。

## 🚀 特性

- **简单易用**: 只需几行代码即可集成室内定位功能
- **高精度**: 采用PDR+MLA传感器融合算法，精度可达2米以内
- **实时性**: 50Hz数据采集，处理延迟<50ms
- **低功耗**: 针对移动设备优化，支持长时间运行
- **自适应**: 支持多种运行模式，自动适配不同使用场景
- **稳定性**: 内置异常检测和恢复机制

## 📦 安装

将库文件复制到你的小程序项目中：

```
your-miniprogram/
├── lib/
│   ├── WXInertialNavigation.js
│   └── core/
│       ├── InertialSensorManager.js
│       ├── MotionAnalyzer.js
│       ├── PositionCalculator.js
│       └── QualityController.js
└── pages/
```

## 🎯 快速开始

### 基础用法

```javascript
import WXInertialNavigation from './lib/WXInertialNavigation.js';

// 1. 创建惯导实例
const inertialNav = new WXInertialNavigation({
  // 必需：设置初始位置
  initialPosition: { x: 0, y: 0, z: 0 },
  
  // 可选：运行模式
  mode: 'standard', // 'lite' | 'standard' | 'precise'
  
  // 可选：采样率
  sampleRate: 50
});

// 2. 设置位置更新回调
inertialNav.setCallbacks({
  onLocationUpdate: (location) => {
    console.log('位置更新:', location.position);
    console.log('置信度:', location.confidence);
    console.log('步数:', location.stepCount);
  },
  
  onStepDetected: (stepInfo) => {
    console.log('检测到步态:', stepInfo);
  },
  
  onError: (error) => {
    console.error('定位错误:', error);
  }
});

// 3. 启动定位
async function startNavigation() {
  try {
    const success = await inertialNav.start();
    if (success) {
      console.log('惯导定位启动成功');
    }
  } catch (error) {
    console.error('启动失败:', error);
  }
}

// 4. 获取当前位置
function getCurrentLocation() {
  const location = inertialNav.getCurrentLocation();
  return location.position; // {x, y, z}
}

// 5. 停止定位
function stopNavigation() {
  inertialNav.stop();
}
```

### 在页面中使用

```javascript
// pages/index/index.js
import WXInertialNavigation from '../../lib/WXInertialNavigation.js';

Page({
  data: {
    currentPosition: { x: 0, y: 0, z: 0 },
    isTracking: false,
    stepCount: 0,
    confidence: 0
  },

  onLoad() {
    // 初始化惯导库
    this.initInertialNavigation();
  },

  initInertialNavigation() {
    this.inertialNav = new WXInertialNavigation({
      initialPosition: { x: 0, y: 0, z: 0 },
      mode: 'standard',
      sampleRate: 50,
      calibration: {
        stepLength: 0.75, // 个人步长(米)
        magneticDeclination: 2.0 // 磁偏角(度)
      }
    });

    // 设置回调
    this.inertialNav.setCallbacks({
      onLocationUpdate: (location) => {
        this.setData({
          currentPosition: location.position,
          stepCount: location.stepCount,
          confidence: (location.confidence * 100).toFixed(1)
        });
      },

      onStepDetected: (stepInfo) => {
        console.log('步态检测:', stepInfo);
      },

      onError: (error) => {
        wx.showToast({
          title: '定位异常',
          icon: 'none'
        });
        console.error('定位错误:', error);
      }
    });
  },

  async startTracking() {
    try {
      const success = await this.inertialNav.start();
      if (success) {
        this.setData({ isTracking: true });
        wx.showToast({
          title: '定位已启动',
          icon: 'success'
        });
      }
    } catch (error) {
      wx.showToast({
        title: '启动失败',
        icon: 'none'
      });
    }
  },

  stopTracking() {
    this.inertialNav.stop();
    this.setData({ isTracking: false });
    wx.showToast({
      title: '定位已停止',
      icon: 'success'
    });
  },

  resetPosition() {
    this.inertialNav.reset({ x: 0, y: 0, z: 0 });
    this.setData({
      currentPosition: { x: 0, y: 0, z: 0 },
      stepCount: 0
    });
  }
});
```

## ⚙️ 配置选项

### 构造函数配置

```javascript
const config = {
  // 【必需】初始位置坐标
  initialPosition: {
    x: 0,    // X坐标(米)
    y: 0,    // Y坐标(米)  
    z: 0     // Z坐标(米)
  },
  
  // 【可选】运行模式
  mode: 'standard', // 'lite' | 'standard' | 'precise'
  
  // 【可选】传感器采样率(Hz)
  sampleRate: 50, // 推荐：10-100
  
  // 【可选】功能开关
  enableMLA: true,                    // 启用MLA校正
  enableStepDetection: true,          // 启用步态检测
  enableHeadingCorrection: true,      // 启用航向校正
  
  // 【可选】校准参数
  calibration: {
    stepLength: 0.75,                 // 个人步长(米)
    magneticDeclination: 0,           // 磁偏角(度)
    accelerometerBias: [0, 0, 0],     // 加速计偏置
    gyroscopeBias: [0, 0, 0]          // 陀螺仪偏置
  },
  
  // 【可选】融合参数
  fusion: {
    pdrWeight: 0.7,                   // PDR权重
    mlaWeight: 0.3,                   // MLA权重
    adaptiveWeighting: true,          // 自适应权重
    confidenceThreshold: 0.4,         // 置信度阈值
    smoothingFactor: 0.4              // 平滑因子
  },
  
  // 【可选】质量控制
  quality: {
    outlierThreshold: 3.0,            // 异常值阈值
    minConfidence: 0.2,               // 最小置信度
    maxDeviation: 5.0                 // 最大偏差(米)
  }
};
```

### 运行模式说明

| 模式 | 特点 | 适用场景 | 功耗 | 精度 |
|------|------|----------|------|------|
| `lite` | 轻量级，低功耗 | 简单导航，长时间使用 | 低 | 中等 |
| `standard` | 平衡性能与功耗 | 大多数应用场景 | 中等 | 良好 |
| `precise` | 高精度，高性能 | 精确定位需求 | 高 | 最佳 |

## 📱 API 文档

### 主要方法

#### `new WXInertialNavigation(config)`
创建惯导实例。

**参数:**
- `config` - 配置对象

#### `start(options?): Promise<boolean>`
启动惯导定位。

**参数:**
- `options` - 可选的启动配置

**返回值:**
- `Promise<boolean>` - 启动是否成功

#### `stop(): void`
停止惯导定位。

#### `pause(pause = true): void`
暂停/恢复定位。

**参数:**
- `pause` - true暂停，false恢复

#### `reset(position?): void`
重置到指定位置。

**参数:**
- `position` - 新的位置坐标，默认为初始位置

#### `getCurrentLocation(): Object`
获取当前位置信息。

**返回值:**
```javascript
{
  position: { x: 1.23, y: 4.56, z: 0.00 },
  heading: 45.0,
  velocity: 1.2,
  stepCount: 123,
  confidence: 0.85,
  timestamp: 1640995200000,
  isMoving: true,
  qualityLevel: 'good',
  systemStatus: { ... }
}
```

#### `getTrajectory(limit?): Array`
获取历史轨迹。

**参数:**
- `limit` - 返回的轨迹点数量限制

**返回值:**
- 轨迹点数组

#### `getStatistics(): Object`
获取性能统计。

**返回值:**
```javascript
{
  runtime: 120000,
  totalSteps: 150,
  totalDistance: 112.5,
  averageConfidence: 0.82,
  correctionCount: 8,
  averageProcessingTime: 12.3,
  trajectoryLength: 89,
  isRealtime: true
}
```

#### `setCallbacks(callbacks): void`
设置回调函数。

**参数:**
```javascript
{
  onLocationUpdate: (location) => { },
  onStepDetected: (stepInfo) => { },
  onError: (error) => { },
  onCalibrationRequired: () => { }
}
```

#### `updateConfig(config): void`
更新配置参数。

#### `calibrate(calibrationData): Promise<boolean>`
传感器校准。

#### `exportData(format = 'json'): string`
导出轨迹数据。

**参数:**
- `format` - 导出格式：'json' | 'csv'

### 事件回调

#### onLocationUpdate(location)
位置更新时触发。

**参数:**
- `location` - 位置信息对象

#### onStepDetected(stepInfo)
检测到步态时触发。

**参数:**
```javascript
{
  stepCount: 123,
  stepLength: 0.75,
  timestamp: 1640995200000
}
```

#### onError(error)
发生错误时触发。

**参数:**
```javascript
{
  type: 'sensor_error',
  message: '传感器异常',
  timestamp: 1640995200000,
  state: { ... }
}
```

## 🔧 进阶使用

### 自定义校准

```javascript
// 传感器校准
const calibrationResult = await inertialNav.calibrate({
  stepLength: 0.8,           // 个人步长
  magneticDeclination: 2.5,  // 当地磁偏角
  personalFactor: 1.1        // 个人化因子
});

if (calibrationResult) {
  console.log('校准成功');
} else {
  console.log('校准失败');
}
```

### 设置MLA节点（可选）

```javascript
// 设置MLA校正节点
const mlaNodes = [
  { id: 1, x: 10, y: 5, z: 0, floor: 1, description: '入口' },
  { id: 2, x: 20, y: 15, z: 0, floor: 1, description: '转角' },
  // ... 更多节点
];

// 通过位置计算器设置
inertialNav.positionCalculator.setMlaNodes(mlaNodes);
```

### 性能监控

```javascript
// 获取详细统计信息
const stats = inertialNav.getStatistics();
console.log('平均处理时间:', stats.averageProcessingTime, 'ms');
console.log('定位精度:', (stats.averageConfidence * 100).toFixed(1), '%');
console.log('总步数:', stats.totalSteps);
console.log('总距离:', stats.totalDistance.toFixed(2), '米');

// 获取系统状态
const status = inertialNav.getSystemStatus();
console.log('运行状态:', status.isRunning);
console.log('传感器状态:', status.sensorStatus);
console.log('质量等级:', status.qualityLevel);
```

### 数据导出

```javascript
// 导出JSON格式
const jsonData = inertialNav.exportData('json');
console.log('JSON数据:', jsonData);

// 导出CSV格式
const csvData = inertialNav.exportData('csv');
console.log('CSV数据:', csvData);

// 保存到本地
wx.setStorageSync('trajectory_data', jsonData);
```

## 🎯 最佳实践

### 1. 初始化配置

```javascript
// 推荐配置
const inertialNav = new WXInertialNavigation({
  initialPosition: { x: 0, y: 0, z: 0 },
  mode: 'standard',
  sampleRate: 50,
  
  // 根据具体场景调整
  calibration: {
    stepLength: 0.75,        // 根据用户身高调整
    magneticDeclination: 0   // 根据地理位置设置
  },
  
  fusion: {
    adaptiveWeighting: true, // 启用自适应权重
    smoothingFactor: 0.3     // 适中的平滑因子
  }
});
```

### 2. 错误处理

```javascript
inertialNav.setCallbacks({
  onError: (error) => {
    switch (error.type) {
      case 'sensor_error':
        // 传感器异常，提示用户检查设备
        wx.showModal({
          title: '传感器异常',
          content: '请检查设备传感器是否正常',
          showCancel: false
        });
        break;
        
      case 'calibration_required':
        // 需要校准，引导用户进行校准
        wx.showModal({
          title: '需要校准',
          content: '为了提高定位精度，建议进行传感器校准',
          confirmText: '立即校准',
          success: (res) => {
            if (res.confirm) {
              // 跳转到校准页面
              wx.navigateTo({
                url: '/pages/calibration/calibration'
              });
            }
          }
        });
        break;
        
      default:
        console.error('未知错误:', error);
    }
  }
});
```

### 3. 性能优化

```javascript
// 根据应用场景选择合适的模式
const config = {
  // 导航类应用 - 平衡模式
  mode: 'standard',
  sampleRate: 50,
  
  // 游戏类应用 - 高精度模式
  // mode: 'precise',
  // sampleRate: 100,
  
  // 长时间使用 - 轻量模式
  // mode: 'lite',
  // sampleRate: 25,
};

// 监控性能指标
setInterval(() => {
  const stats = inertialNav.getStatistics();
  if (stats.averageProcessingTime > 50) {
    console.warn('处理时间过长，考虑降低采样率');
  }
}, 10000);
```

### 4. 数据持久化

```javascript
// 定期保存轨迹数据
let saveTimer = null;

inertialNav.setCallbacks({
  onLocationUpdate: (location) => {
    // 防抖保存
    if (saveTimer) clearTimeout(saveTimer);
    saveTimer = setTimeout(() => {
      const trajectory = inertialNav.getTrajectory(100); // 保存最近100个点
      wx.setStorageSync('recent_trajectory', trajectory);
    }, 5000);
  }
});

// 应用恢复时加载数据
onShow() {
  const savedTrajectory = wx.getStorageSync('recent_trajectory');
  if (savedTrajectory && savedTrajectory.length > 0) {
    // 可以用于显示历史轨迹或恢复状态
    console.log('加载历史轨迹:', savedTrajectory.length, '个点');
  }
}
```

## ⚠️ 注意事项

1. **权限要求**: 需要获取位置权限才能使用
2. **真机测试**: 必须在真机上测试，模拟器不支持传感器
3. **初始校准**: 首次使用建议进行传感器校准
4. **环境影响**: 定位精度受磁场环境影响
5. **电量消耗**: 长时间使用会增加电量消耗
6. **数据存储**: 轨迹数据会占用一定内存，注意及时清理

## 🐛 故障排除

### 常见问题

**Q: 定位精度不高？**
A: 
1. 进行传感器校准
2. 检查磁场环境是否稳定
3. 调整融合权重参数
4. 确保设备正确握持

**Q: 步态检测不准确？**
A:
1. 检查加速计校准状态
2. 调整步态检测阈值
3. 确保正常步行速度
4. 避免设备剧烈晃动

**Q: 航向角偏差大？**
A:
1. 进行罗盘校准
2. 远离磁场干扰源
3. 设置正确的磁偏角
4. 调整航向融合参数

**Q: 应用卡顿？**
A:
1. 降低采样率
2. 使用轻量模式
3. 减少历史数据保存量
4. 优化回调函数处理

## 📝 版本历史

### v1.0.0
- 🎉 首个版本发布
- ✅ 完整的PDR算法实现
- ✅ 传感器融合功能
- ✅ 多种运行模式
- ✅ 实时性能监控
- ✅ 数据导出功能

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📧 支持

如有问题或建议，请通过以下方式联系：

- 提交Issue
- 查看文档
- 技术交流群

---

**祝你使用愉快！** 🎉