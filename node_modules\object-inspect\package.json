{"name": "object-inspect", "version": "0.4.0", "description": "string representations of objects in node and the browser", "main": "index.js", "devDependencies": {"tape": "~2.6.0"}, "scripts": {"test": "tape test/*.js"}, "testling": {"files": ["test/*.js", "test/browser/*.js"], "browsers": ["ie/6..latest", "chrome/latest", "firefox/latest", "safari/latest", "opera/latest", "iphone/latest", "ipad/latest", "android/latest"]}, "repository": {"type": "git", "url": "git://github.com/substack/object-inspect.git"}, "homepage": "https://github.com/substack/object-inspect", "keywords": ["inspect", "util.inspect", "object", "stringify", "pretty"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT"}