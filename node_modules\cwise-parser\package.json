{"name": "cwise-parser", "version": "1.0.3", "description": "<PERSON>rser for cwise", "main": "index.js", "directories": {"test": "test"}, "dependencies": {"esprima": "^1.0.3", "uniq": "^1.0.0"}, "devDependencies": {"tape": "^4.0.0"}, "scripts": {"test": "tape test/*.js"}, "repository": {"type": "git", "url": "git://github.com/scijs/cwise-parser.git"}, "keywords": ["cwise", "parser", "n<PERSON><PERSON>", "internals"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "gitHead": "a57b0ecf9a304e991c9bca348b4aa99b5c2a7fc1", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/scijs/cwise-parser/issues"}}