<!--设置页面-->
<view class="settings-page">
  
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">设置</text>
    <text class="page-subtitle">定位模式和系统配置</text>
  </view>

  <!-- 设置列表 -->
  <scroll-view class="settings-content" scroll-y="{{true}}">
    
    <!-- 定位设置 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-icon">🧭</text>
        <text class="section-title">定位设置</text>
      </view>
      
      <!-- 导航模式 -->
      <view class="setting-item" bindtap="showModeSelector">
        <view class="setting-label">
          <text class="label-text">导航模式</text>
          <text class="label-desc">{{getModeDisplayName(navigationMode)}}</text>
        </view>
        <view class="setting-value">
          <text class="value-text">{{navigationMode}}</text>
          <text class="arrow">></text>
        </view>
      </view>
      
      <!-- 步长设置 -->
      <view class="setting-item">
        <view class="setting-label">
          <text class="label-text">步长</text>
          <text class="label-desc">{{stepLength}}米</text>
        </view>
        <view class="setting-control">
          <slider 
            min="0.5" 
            max="1.0" 
            step="0.05" 
            value="{{stepLength}}"
            show-value="{{false}}"
            bindchange="onStepLengthChange"
            activeColor="#667eea"
          />
        </view>
      </view>
      
      <!-- 磁偏角设置 -->
      <view class="setting-item">
        <view class="setting-label">
          <text class="label-text">磁偏角</text>
          <text class="label-desc">{{magneticDeclination}}度</text>
        </view>
        <view class="setting-control">
          <slider 
            min="-20" 
            max="20" 
            step="1" 
            value="{{magneticDeclination}}"
            show-value="{{false}}"
            bindchange="onMagneticDeclinationChange"
            activeColor="#667eea"
          />
        </view>
      </view>
    </view>

    <!-- 用户体验 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-icon">👤</text>
        <text class="section-title">用户体验</text>
      </view>
      
      <!-- 语音提示 -->
      <view class="setting-item">
        <view class="setting-label">
          <text class="label-text">语音提示</text>
          <text class="label-desc">导航语音指引</text>
        </view>
        <view class="setting-control">
          <switch 
            checked="{{voicePrompt}}" 
            bindchange="toggleVoicePrompt"
            color="#667eea"
          />
        </view>
      </view>
      
      <!-- 震动反馈 -->
      <view class="setting-item">
        <view class="setting-label">
          <text class="label-text">震动反馈</text>
          <text class="label-desc">步态检测震动</text>
        </view>
        <view class="setting-control">
          <switch 
            checked="{{vibrationFeedback}}" 
            bindchange="toggleVibrationFeedback"
            color="#667eea"
          />
        </view>
      </view>
    </view>

    <!-- 系统工具 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-icon">🔧</text>
        <text class="section-title">系统工具</text>
      </view>
      
      <!-- 传感器校准 -->
      <view class="setting-item" bindtap="showCalibrationPanel">
        <view class="setting-label">
          <text class="label-text">传感器校准</text>
          <text class="label-desc">校准加速度计和磁力计</text>
        </view>
        <view class="setting-value">
          <text class="arrow">></text>
        </view>
      </view>
      
      <!-- 系统状态 -->
      <view class="setting-item" bindtap="viewSystemStatus">
        <view class="setting-label">
          <text class="label-text">系统状态</text>
          <text class="label-desc">查看定位系统状态</text>
        </view>
        <view class="setting-value">
          <view class="status-indicator {{locationStatus}}"></view>
          <text class="arrow">></text>
        </view>
      </view>
      
      <!-- 导出设置 -->
      <view class="setting-item" bindtap="exportSettings">
        <view class="setting-label">
          <text class="label-text">导出设置</text>
          <text class="label-desc">备份当前配置</text>
        </view>
        <view class="setting-value">
          <text class="arrow">></text>
        </view>
      </view>
    </view>

    <!-- 数据管理 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-icon">📁</text>
        <text class="section-title">数据管理</text>
      </view>
      
      <!-- 重置设置 -->
      <view class="setting-item" bindtap="resetSettings">
        <view class="setting-label">
          <text class="label-text">重置设置</text>
          <text class="label-desc">恢复默认配置</text>
        </view>
        <view class="setting-value">
          <text class="arrow">></text>
        </view>
      </view>
      
      <!-- 清除数据 -->
      <view class="setting-item danger" bindtap="clearData">
        <view class="setting-label">
          <text class="label-text">清除数据</text>
          <text class="label-desc">删除所有应用数据</text>
        </view>
        <view class="setting-value">
          <text class="arrow">></text>
        </view>
      </view>
    </view>

    <!-- 关于 -->
    <view class="settings-section">
      <view class="section-header">
        <text class="section-icon">ℹ️</text>
        <text class="section-title">关于</text>
      </view>
      
      <!-- 版本信息 -->
      <view class="setting-item" bindtap="showAbout">
        <view class="setting-label">
          <text class="label-text">版本信息</text>
          <text class="label-desc">室内导航 v1.0.0</text>
        </view>
        <view class="setting-value">
          <text class="arrow">></text>
        </view>
      </view>
    </view>

    <!-- 底部间距 -->
    <view class="bottom-spacing"></view>
  </scroll-view>

  <!-- 模式选择弹窗 -->
  <view wx:if="{{showModeSelector}}" class="mode-selector-overlay" bindtap="hideModeSelector">
    <view class="mode-selector-popup" catchtap="">
      <view class="popup-header">
        <text class="popup-title">选择导航模式</text>
        <button class="popup-close" bindtap="hideModeSelector" size="mini">×</button>
      </view>
      
      <view class="mode-list">
        <view wx:for="{{modeOptions}}" wx:key="value"
              class="mode-item {{navigationMode === item.value ? 'selected' : ''}}"
              bindtap="selectNavigationMode"
              data-mode="{{item.value}}">
          <view class="mode-info">
            <text class="mode-name">{{item.name}}</text>
            <text class="mode-desc">{{item.desc}}</text>
          </view>
          <view wx:if="{{navigationMode === item.value}}" class="mode-check">✓</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 校准面板 -->
  <view wx:if="{{showCalibrationPanel}}" class="calibration-overlay" bindtap="hideCalibrationPanel">
    <view class="calibration-popup" catchtap="">
      <view class="popup-header">
        <text class="popup-title">传感器校准</text>
        <button class="popup-close" bindtap="hideCalibrationPanel" size="mini">×</button>
      </view>
      
      <view class="calibration-content">
        <view class="calibration-steps">
          <text class="step-title">校准步骤：</text>
          <text class="step-item">1. 将手机放在平整表面上</text>
          <text class="step-item">2. 保持手机静止不动</text>
          <text class="step-item">3. 点击开始校准</text>
          <text class="step-item">4. 等待校准完成</text>
        </view>
        
        <view class="calibration-actions">
          <button class="calibration-button" bindtap="startCalibration">
            开始校准
          </button>
        </view>
      </view>
    </view>
  </view>
</view>