/**
 * 历史页面
 * 显示导航历史记录、轨迹回放和统计信息
 */

const app = getApp();

Page({
  data: {
    // 历史记录
    historyList: [],
    selectedHistory: null,
    
    // 统计信息
    totalNavigations: 0,
    totalDistance: 0,
    totalSteps: 0,
    totalTime: 0,
    averageAccuracy: 0,
    formattedAverageAccuracy: 0,
    maxDistance: 0,
    
    // 界面状态
    showHistoryDetail: false,
    showTrajectoryView: false,
    currentTab: 'history', // history, statistics
    
    // 轨迹数据
    trajectoryData: [],
    
    // 排序和筛选
    sortBy: 'time', // time, distance, duration
    sortOrder: 'desc', // desc, asc
    filterType: 'all' // all, today, week, month
  },

  onLoad() {
    console.log('📖 历史页面加载');
    this.loadHistoryData();
    this.calculateStatistics();
  },

  onShow() {
    console.log('👀 历史页面显示');
    // 刷新数据
    this.loadHistoryData();
    this.calculateStatistics();
  },

  /**
   * 加载历史数据
   */
  loadHistoryData() {
    try {
      // 从本地存储加载历史记录
      const historyData = wx.getStorageSync('navigation_history') || [];
      
      // 按时间排序
      const sortedHistory = historyData.sort((a, b) => {
        return new Date(b.endTime) - new Date(a.endTime);
      });
      
      this.setData({
        historyList: sortedHistory,
        totalNavigations: sortedHistory.length
      });
      
      console.log('📋 历史数据已加载:', sortedHistory.length, '条记录');
      
    } catch (error) {
      console.error('❌ 加载历史数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  /**
   * 计算统计信息
   */
  calculateStatistics() {
    const { historyList } = this.data;
    
    if (historyList.length === 0) {
      this.setData({
        totalDistance: 0,
        totalSteps: 0,
        totalTime: 0,
        averageAccuracy: 0
      });
      return;
    }
    
    let totalDistance = 0;
    let totalSteps = 0;
    let totalTime = 0;
    let totalAccuracy = 0;
    
    // 预处理历史数据，计算格式化后的精度值
    const processedHistoryList = historyList.map(item => ({
      ...item,
      formattedAccuracy: ((item.averageAccuracy || 0) * 100).toFixed(1) + '%'
    }));
    
    processedHistoryList.forEach(item => {
      totalDistance += item.distance || 0;
      totalSteps += item.steps || 0;
      totalTime += item.duration || 0;
      totalAccuracy += item.averageAccuracy || 0;
    });
    
    const formattedAverageAccuracy = historyList.length > 0 
      ? Math.round((totalAccuracy / historyList.length) * 100)
      : 0;
    
    // 计算最长导航距离
    const maxDistance = historyList.length > 0 
      ? Math.max(...historyList.map(item => item.distance || 0))
      : 0;
    
    this.setData({
      historyList: processedHistoryList,
      totalDistance: totalDistance,
      totalSteps: totalSteps,
      totalTime: totalTime,
      averageAccuracy: totalAccuracy / historyList.length,
      formattedAverageAccuracy: formattedAverageAccuracy,
      maxDistance: maxDistance
    });
  },

  /**
   * 切换标签页
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ currentTab: tab });
  },

  /**
   * 查看历史详情
   */
  viewHistoryDetail(e) {
    const index = e.currentTarget.dataset.index;
    const history = this.data.historyList[index];
    
    // 为详情弹窗准备格式化数据
    const formattedHistory = {
      ...history,
      formattedAccuracy: ((history.averageAccuracy || 0) * 100).toFixed(1) + '%'
    };
    
    this.setData({
      selectedHistory: formattedHistory,
      showHistoryDetail: true
    });
  },

  /**
   * 关闭历史详情
   */
  closeHistoryDetail() {
    this.setData({
      selectedHistory: null,
      showHistoryDetail: false
    });
  },

  /**
   * 查看轨迹
   */
  viewTrajectory(e) {
    const index = e.currentTarget.dataset.index;
    const history = this.data.historyList[index];
    
    if (history.trajectory && history.trajectory.length > 0) {
      this.setData({
        trajectoryData: history.trajectory,
        showTrajectoryView: true
      });
    } else {
      wx.showToast({
        title: '暂无轨迹数据',
        icon: 'none'
      });
    }
  },

  /**
   * 关闭轨迹查看
   */
  closeTrajectoryView() {
    this.setData({
      trajectoryData: [],
      showTrajectoryView: false
    });
  },

  /**
   * 删除历史记录
   */
  deleteHistory(e) {
    const index = e.currentTarget.dataset.index;
    const history = this.data.historyList[index];
    
    wx.showModal({
      title: '删除记录',
      content: `确定要删除 ${this.formatDate(history.endTime)} 的导航记录吗？`,
      success: (res) => {
        if (res.confirm) {
          this.performDeleteHistory(index);
        }
      }
    });
  },

  /**
   * 执行删除操作
   */
  performDeleteHistory(index) {
    try {
      const historyList = [...this.data.historyList];
      historyList.splice(index, 1);
      
      // 更新本地存储
      wx.setStorageSync('navigation_history', historyList);
      
      // 更新界面
      this.setData({ historyList });
      this.calculateStatistics();
      
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
      
    } catch (error) {
      console.error('❌ 删除历史记录失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
    }
  },

  /**
   * 清空所有历史
   */
  clearAllHistory() {
    wx.showModal({
      title: '清空历史',
      content: '确定要清空所有导航历史记录吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('navigation_history');
            this.setData({
              historyList: [],
              totalNavigations: 0
            });
            this.calculateStatistics();
            
            wx.showToast({
              title: '已清空',
              icon: 'success'
            });
            
          } catch (error) {
            console.error('❌ 清空历史失败:', error);
            wx.showToast({
              title: '清空失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  /**
   * 导出历史数据
   */
  exportHistoryData() {
    try {
      const exportData = {
        historyList: this.data.historyList,
        statistics: {
          totalNavigations: this.data.totalNavigations,
          totalDistance: this.data.totalDistance,
          totalSteps: this.data.totalSteps,
          totalTime: this.data.totalTime,
          averageAccuracy: this.data.averageAccuracy
        },
        exportTime: new Date().toISOString()
      };
      
      const dataStr = JSON.stringify(exportData, null, 2);
      
      wx.setClipboardData({
        data: dataStr,
        success: () => {
          wx.showToast({
            title: '数据已复制到剪贴板',
            icon: 'success'
          });
        }
      });
      
    } catch (error) {
      console.error('❌ 导出数据失败:', error);
      wx.showToast({
        title: '导出失败',
        icon: 'error'
      });
    }
  },

  /**
   * 分享历史记录
   */
  shareHistory(e) {
    const index = e.currentTarget.dataset.index;
    const history = this.data.historyList[index];
    
    const shareText = `我的导航记录:\n时间: ${this.formatDate(history.endTime)}\n距离: ${history.distance?.toFixed(1)}米\n步数: ${history.steps}\n用时: ${this.formatDuration(history.duration)}`;
    
    wx.setClipboardData({
      data: shareText,
      success: () => {
        wx.showToast({
          title: '记录已复制',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 重新导航
   */
  reNavigate(e) {
    const index = e.currentTarget.dataset.index;
    const history = this.data.historyList[index];
    
    if (history.endPoint) {
      // 跳转到导航页面并设置目标
      wx.switchTab({
        url: `/pages/navigation/navigation?destination=${history.endPoint.id}`
      });
    } else {
      wx.showToast({
        title: '无法重新导航',
        icon: 'none'
      });
    }
  },

  /**
   * 格式化日期
   */
  formatDate(dateStr) {
    const date = new Date(dateStr);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    
    if (date >= today) {
      return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (date >= yesterday) {
      return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    }
  },

  /**
   * 格式化持续时间
   */
  formatDuration(seconds) {
    if (!seconds) return '0分钟';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟${secs}秒`;
    } else {
      return `${secs}秒`;
    }
  },

  /**
   * 格式化距离
   */
  formatDistance(meters) {
    if (!meters) return '0米';
    
    if (meters >= 1000) {
      return `${(meters / 1000).toFixed(1)}公里`;
    } else {
      return `${meters.toFixed(1)}米`;
    }
  },

  /**
   * 获取导航类型图标
   */
  getNavigationIcon(type) {
    const iconMap = {
      walking: '🚶',
      indoor: '🏢',
      outdoor: '🗺️',
      mixed: '🧭'
    };
    return iconMap[type] || '📍';
  },

  /**
   * 跳转到导航页面
   */
  goToNavigation() {
    wx.switchTab({
      url: '/pages/navigation/navigation'
    });
  }
});