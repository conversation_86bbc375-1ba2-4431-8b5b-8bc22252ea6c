#!/usr/bin/env node

import { runTests } from './test/simple-test.js';

async function main() {
    console.log('🚀 开始运行惯导定位库测试...');
    
    try {
        const results = await runTests();
        console.log('\n🎉 测试完成!');
        console.log(`总测试数: ${results.total}`);
        console.log(`通过: ${results.passed} ✅`);
        console.log(`失败: ${results.failed} ❌`);
        console.log(`通过率: ${results.passRate.toFixed(1)}%`);
        
        if (results.failed > 0) {
            process.exit(1);
        }
    } catch (error) {
        console.error('❌ 测试运行失败:', error);
        process.exit(1);
    }
}

main();