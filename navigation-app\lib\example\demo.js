/**
 * 微信小程序惯导定位库使用示例
 * 演示如何在小程序页面中集成和使用惯导定位功能
 */

import WXInertialNavigation from '../WXInertialNavigation.js';

// 示例页面配置
Page({
  data: {
    // 位置信息
    currentPosition: { x: 0, y: 0, z: 0 },
    heading: 0,
    velocity: 0,
    stepCount: 0,
    confidence: 0,
    
    // 状态信息
    isTracking: false,
    isPaused: false,
    trackingDuration: 0,
    
    // 统计信息
    totalDistance: 0,
    averageSpeed: 0,
    qualityLevel: 'unknown',
    
    // 轨迹点（用于绘制）
    trajectoryPoints: [],
    
    // 配置信息
    currentMode: 'standard',
    sampleRate: 50
  },

  /**
   * 页面加载时初始化
   */
  onLoad(options) {
    console.log('🚀 惯导定位演示页面加载');
    
    // 解析启动参数
    if (options.initialX) {
      this.initialPosition = {
        x: parseFloat(options.initialX) || 0,
        y: parseFloat(options.initialY) || 0,
        z: parseFloat(options.initialZ) || 0
      };
    } else {
      this.initialPosition = { x: 0, y: 0, z: 0 };
    }
    
    // 初始化惯导库
    this.initInertialNavigation();
    
    // 启动性能监控
    this.startPerformanceMonitor();
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('📱 页面显示');
    // 如果之前在跟踪，恢复跟踪状态
    if (this.data.isTracking && this.inertialNav) {
      this.inertialNav.pause(false);
    }
  },

  /**
   * 页面隐藏时
   */
  onHide() {
    console.log('🙈 页面隐藏');
    // 暂停跟踪以节省电量
    if (this.data.isTracking && this.inertialNav) {
      this.inertialNav.pause(true);
    }
  },

  /**
   * 页面卸载时
   */
  onUnload() {
    console.log('💥 页面卸载');
    // 停止跟踪并清理资源
    if (this.inertialNav) {
      this.inertialNav.stop();
    }
    
    // 清理定时器
    if (this.performanceTimer) {
      clearInterval(this.performanceTimer);
    }
    
    if (this.durationTimer) {
      clearInterval(this.durationTimer);
    }
  },

  /**
   * 初始化惯导定位库
   */
  initInertialNavigation() {
    console.log('🧭 初始化惯导定位库...');
    
    try {
      // 创建惯导实例
      this.inertialNav = new WXInertialNavigation({
        // 必需配置
        initialPosition: this.initialPosition,
        
        // 基础配置
        mode: this.data.currentMode,
        sampleRate: this.data.sampleRate,
        
        // 功能开关
        enableMLA: true,
        enableStepDetection: true,
        enableHeadingCorrection: true,
        
        // 校准参数（可根据用户设置调整）
        calibration: {
          stepLength: wx.getStorageSync('user_step_length') || 0.75,
          magneticDeclination: wx.getStorageSync('magnetic_declination') || 0,
        },
        
        // 融合参数
        fusion: {
          pdrWeight: 0.7,
          mlaWeight: 0.3,
          adaptiveWeighting: true,
          confidenceThreshold: 0.4
        },
        
        // 质量控制
        quality: {
          outlierThreshold: 3.0,
          minConfidence: 0.2,
          maxDeviation: 5.0
        }
      });
      
      // 设置事件回调
      this.setupCallbacks();
      
      console.log('✅ 惯导定位库初始化成功');
      
    } catch (error) {
      console.error('❌ 惯导定位库初始化失败:', error);
      this.showError('初始化失败', error.message);
    }
  },

  /**
   * 设置事件回调
   */
  setupCallbacks() {
    this.inertialNav.setCallbacks({
      // 位置更新回调
      onLocationUpdate: (location) => {
        this.handleLocationUpdate(location);
      },
      
      // 步态检测回调
      onStepDetected: (stepInfo) => {
        this.handleStepDetected(stepInfo);
      },
      
      // 错误处理回调
      onError: (error) => {
        this.handleError(error);
      },
      
      // 校准需求回调
      onCalibrationRequired: () => {
        this.handleCalibrationRequired();
      }
    });
  },

  /**
   * 处理位置更新
   */
  handleLocationUpdate(location) {
    // 更新页面数据
    this.setData({
      currentPosition: location.position,
      heading: Math.round(location.heading),
      velocity: location.velocity.toFixed(2),
      stepCount: location.stepCount,
      confidence: Math.round(location.confidence * 100),
      qualityLevel: location.qualityLevel
    });
    
    // 添加轨迹点（限制数量以节省内存）
    const trajectoryPoints = [...this.data.trajectoryPoints];
    trajectoryPoints.push({
      x: location.position.x,
      y: location.position.y,
      timestamp: location.timestamp
    });
    
    // 保持最近1000个点
    if (trajectoryPoints.length > 1000) {
      trajectoryPoints.shift();
    }
    
    this.setData({ trajectoryPoints });
    
    // 如果有地图组件，更新地图显示
    if (this.mapComponent) {
      this.mapComponent.updatePosition(location.position);
    }
  },

  /**
   * 处理步态检测
   */
  handleStepDetected(stepInfo) {
    console.log('👣 检测到步态:', stepInfo);
    
    // 可以在这里添加步态相关的UI反馈
    // 比如震动、音效等
    wx.vibrateShort({
      type: 'light'
    });
  },

  /**
   * 处理错误
   */
  handleError(error) {
    console.error('🚨 惯导定位错误:', error);
    
    switch (error.type) {
      case 'sensor_error':
        this.showError('传感器异常', '请检查设备传感器是否正常工作');
        break;
        
      case 'calibration_required':
        this.showCalibrationDialog();
        break;
        
      case 'processing_failed':
        this.showError('处理异常', '数据处理出现问题，请重试');
        break;
        
      default:
        this.showError('未知错误', error.message || '发生未知错误');
    }
  },

  /**
   * 处理校准需求
   */
  handleCalibrationRequired() {
    this.showCalibrationDialog();
  },

  /**
   * 启动惯导定位
   */
  async startTracking() {
    console.log('🚀 启动惯导定位...');
    
    try {
      // 显示加载状态
      wx.showLoading({
        title: '启动中...',
        mask: true
      });
      
      // 启动定位
      const success = await this.inertialNav.start();
      
      wx.hideLoading();
      
      if (success) {
        // 更新状态
        this.setData({ 
          isTracking: true,
          isPaused: false,
          trackingDuration: 0
        });
        
        // 启动时长计时器
        this.startDurationTimer();
        
        // 显示成功提示
        wx.showToast({
          title: '定位已启动',
          icon: 'success',
          duration: 2000
        });
        
        console.log('✅ 惯导定位启动成功');
        
      } else {
        throw new Error('启动失败');
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('❌ 启动惯导定位失败:', error);
      this.showError('启动失败', error.message);
    }
  },

  /**
   * 停止惯导定位
   */
  stopTracking() {
    console.log('⏹️ 停止惯导定位');
    
    if (this.inertialNav) {
      this.inertialNav.stop();
    }
    
    // 更新状态
    this.setData({ 
      isTracking: false,
      isPaused: false
    });
    
    // 停止计时器
    this.stopDurationTimer();
    
    // 显示统计信息
    this.showTrackingStatistics();
    
    wx.showToast({
      title: '定位已停止',
      icon: 'success'
    });
  },

  /**
   * 暂停/恢复定位
   */
  togglePause() {
    const newPauseState = !this.data.isPaused;
    
    if (this.inertialNav) {
      this.inertialNav.pause(newPauseState);
    }
    
    this.setData({ isPaused: newPauseState });
    
    wx.showToast({
      title: newPauseState ? '已暂停' : '已恢复',
      icon: 'success'
    });
  },

  /**
   * 重置位置
   */
  resetPosition() {
    wx.showModal({
      title: '重置位置',
      content: '确定要重置到起始位置吗？',
      success: (res) => {
        if (res.confirm) {
          if (this.inertialNav) {
            this.inertialNav.reset(this.initialPosition);
          }
          
          // 清空轨迹
          this.setData({
            currentPosition: this.initialPosition,
            trajectoryPoints: [],
            stepCount: 0,
            totalDistance: 0
          });
          
          wx.showToast({
            title: '位置已重置',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 传感器校准
   */
  async calibrateSensors() {
    console.log('🔧 开始传感器校准...');
    
    try {
      wx.showLoading({
        title: '校准中...',
        mask: true
      });
      
      // 获取用户设置的校准参数
      const userStepLength = wx.getStorageSync('user_step_length') || 0.75;
      const magneticDeclination = wx.getStorageSync('magnetic_declination') || 0;
      
      const result = await this.inertialNav.calibrate({
        stepLength: userStepLength,
        magneticDeclination: magneticDeclination
      });
      
      wx.hideLoading();
      
      if (result) {
        wx.showToast({
          title: '校准成功',
          icon: 'success'
        });
      } else {
        throw new Error('校准失败');
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('❌ 传感器校准失败:', error);
      this.showError('校准失败', error.message);
    }
  },

  /**
   * 导出轨迹数据
   */
  exportTrajectoryData() {
    try {
      if (!this.inertialNav) {
        wx.showToast({
          title: '没有数据可导出',
          icon: 'none'
        });
        return;
      }
      
      // 获取轨迹数据
      const trajectory = this.inertialNav.getTrajectory();
      const statistics = this.inertialNav.getStatistics();
      
      if (trajectory.length === 0) {
        wx.showToast({
          title: '没有轨迹数据',
          icon: 'none'
        });
        return;
      }
      
      // 生成导出数据
      const exportData = {
        metadata: {
          exportTime: new Date().toISOString(),
          trackingDuration: this.data.trackingDuration,
          totalPoints: trajectory.length,
          initialPosition: this.initialPosition,
          configuration: {
            mode: this.data.currentMode,
            sampleRate: this.data.sampleRate
          }
        },
        statistics: statistics,
        trajectory: trajectory
      };
      
      // 保存到本地存储
      const exportKey = `trajectory_${Date.now()}`;
      wx.setStorageSync(exportKey, exportData);
      
      // 显示成功提示
      wx.showModal({
        title: '导出成功',
        content: `轨迹数据已保存到本地\n包含${trajectory.length}个轨迹点`,
        showCancel: false,
        confirmText: '确定'
      });
      
      console.log('📦 轨迹数据导出成功:', exportKey);
      
    } catch (error) {
      console.error('❌ 导出失败:', error);
      this.showError('导出失败', error.message);
    }
  },

  /**
   * 查看性能统计
   */
  showPerformanceStats() {
    if (!this.inertialNav) {
      wx.showToast({
        title: '系统未运行',
        icon: 'none'
      });
      return;
    }
    
    const stats = this.inertialNav.getStatistics();
    const systemStatus = this.inertialNav.getSystemStatus();
    
    const content = `运行时间: ${(stats.runtime / 1000).toFixed(1)}秒
总步数: ${stats.totalSteps}
总距离: ${stats.totalDistance.toFixed(2)}米
平均置信度: ${(stats.averageConfidence * 100).toFixed(1)}%
处理延迟: ${stats.averageProcessingTime.toFixed(1)}ms
质量等级: ${systemStatus.qualityLevel}`;
    
    wx.showModal({
      title: '性能统计',
      content: content,
      showCancel: false,
      confirmText: '确定'
    });
  },

  /**
   * 启动性能监控
   */
  startPerformanceMonitor() {
    this.performanceTimer = setInterval(() => {
      if (this.inertialNav && this.data.isTracking) {
        const stats = this.inertialNav.getStatistics();
        
        // 更新统计信息
        this.setData({
          totalDistance: stats.totalDistance.toFixed(2),
          averageSpeed: stats.runtime > 0 ? 
            (stats.totalDistance / (stats.runtime / 1000) * 3.6).toFixed(1) : '0.0' // km/h
        });
      }
    }, 2000);
  },

  /**
   * 启动时长计时器
   */
  startDurationTimer() {
    this.durationTimer = setInterval(() => {
      if (this.data.isTracking && !this.data.isPaused) {
        this.setData({
          trackingDuration: this.data.trackingDuration + 1
        });
      }
    }, 1000);
  },

  /**
   * 停止时长计时器
   */
  stopDurationTimer() {
    if (this.durationTimer) {
      clearInterval(this.durationTimer);
      this.durationTimer = null;
    }
  },

  /**
   * 显示跟踪统计信息
   */
  showTrackingStatistics() {
    if (!this.inertialNav) return;
    
    const stats = this.inertialNav.getStatistics();
    const duration = this.data.trackingDuration;
    
    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);
    const seconds = duration % 60;
    
    const timeStr = hours > 0 ? 
      `${hours}小时${minutes}分${seconds}秒` : 
      minutes > 0 ? 
        `${minutes}分${seconds}秒` : 
        `${seconds}秒`;
    
    const content = `跟踪时长: ${timeStr}
总步数: ${stats.totalSteps}步
总距离: ${stats.totalDistance.toFixed(2)}米
平均速度: ${this.data.averageSpeed}km/h
平均置信度: ${(stats.averageConfidence * 100).toFixed(1)}%`;
    
    wx.showModal({
      title: '跟踪统计',
      content: content,
      showCancel: false,
      confirmText: '确定'
    });
  },

  /**
   * 显示校准对话框
   */
  showCalibrationDialog() {
    wx.showModal({
      title: '需要校准',
      content: '为了提高定位精度，建议进行传感器校准。是否立即校准？',
      confirmText: '立即校准',
      success: (res) => {
        if (res.confirm) {
          this.calibrateSensors();
        }
      }
    });
  },

  /**
   * 显示错误信息
   */
  showError(title, message) {
    wx.showModal({
      title: title,
      content: message,
      showCancel: false,
      confirmText: '确定'
    });
  },

  /**
   * 切换运行模式
   */
  switchMode() {
    const modes = ['lite', 'standard', 'precise'];
    const currentIndex = modes.indexOf(this.data.currentMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    const nextMode = modes[nextIndex];
    
    wx.showModal({
      title: '切换模式',
      content: `确定要切换到${nextMode}模式吗？这将重启定位系统。`,
      success: (res) => {
        if (res.confirm) {
          // 停止当前定位
          if (this.data.isTracking) {
            this.inertialNav.stop();
          }
          
          // 更新模式
          this.setData({ currentMode: nextMode });
          
          // 重新初始化
          this.initInertialNavigation();
          
          wx.showToast({
            title: `已切换到${nextMode}模式`,
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 获取格式化的时长
   */
  getFormattedDuration() {
    const duration = this.data.trackingDuration;
    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);
    const seconds = duration % 60;
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
  }
});

// 导出示例配置供其他页面参考
export const exampleConfig = {
  // 基础配置示例
  basic: {
    initialPosition: { x: 0, y: 0, z: 0 },
    mode: 'standard',
    sampleRate: 50
  },
  
  // 高精度配置示例
  precision: {
    initialPosition: { x: 0, y: 0, z: 0 },
    mode: 'precise',
    sampleRate: 100,
    fusion: {
      adaptiveWeighting: true,
      confidenceThreshold: 0.6
    }
  },
  
  // 省电配置示例
  battery: {
    initialPosition: { x: 0, y: 0, z: 0 },
    mode: 'lite',
    sampleRate: 25,
    quality: {
      minConfidence: 0.1
    }
  }
};