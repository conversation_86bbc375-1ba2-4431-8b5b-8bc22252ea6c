/* PDR演示页面样式 */
.pdr-demo-container {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 顶部标题区域 */
.header {
  background-color: #5c6bc0;
  color: white;
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-title {
  font-size: 18px;
  font-weight: bold;
}

.menu-button {
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 16px;
}

/* 传感器数据区域 */
.sensor-data {
  background-color: white;
  margin: 8px;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.data-left {
  flex: 1;
}

.data-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.data-row:last-child {
  border-bottom: none;
}

.data-label {
  color: #666;
  font-size: 14px;
}

.data-value {
  color: #333;
  font-size: 16px;
  font-weight: bold;
  min-width: 60px;
  text-align: right;
}

.controls-right {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 20px;
}

.control-button {
  width: 80px;
  height: 35px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: bold;
  border: none;
}

.init-btn {
  background-color: #9e9e9e;
  color: white;
}

.start-btn {
  background-color: #4caf50;
  color: white;
}

.stop-btn {
  background-color: #f44336;
  color: white;
}

.destroy-btn {
  background-color: #ff9800;
  color: white;
}

.control-button[disabled] {
  opacity: 0.5;
}

/* 统计信息区域 */
.stats-area {
  background-color: white;
  margin: 0 8px 8px 8px;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-left {
  display: flex;
  gap: 40px;
}

.stats-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-label {
  color: #666;
  font-size: 14px;
}

.stats-value {
  color: #333;
  font-size: 16px;
  font-weight: bold;
}

.function-buttons {
  display: flex;
  gap: 10px;
}

.func-button {
  background-color: #2196f3;
  color: white;
  border-radius: 6px;
  font-size: 14px;
  padding: 8px 16px;
  border: none;
}

.func-button[disabled] {
  background-color: #ccc;
}

/* 轨迹显示区域 */
.trajectory-area {
  flex: 1;
  background-color: white;
  margin: 0 8px 8px 8px;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.trajectory-canvas {
  flex: 1;
  position: relative;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  min-height: 300px;
}

/* 坐标系网格 */
.coordinate-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.grid-line {
  position: absolute;
  background-color: #ddd;
}

.grid-line.vertical {
  width: 1px;
  height: 100%;
}

.grid-line.horizontal {
  height: 1px;
  width: 100%;
}

.axis-labels {
  position: relative;
  width: 100%;
  height: 100%;
}

.axis-label {
  position: absolute;
  font-size: 12px;
  color: #666;
}

/* 轨迹点 */
.trajectory-point {
  position: absolute;
  width: 4px;
  height: 4px;
  background-color: #2196f3;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

/* 当前位置指示器 */
.current-position {
  position: absolute;
  transform-origin: center;
  transform: translate(-50%, -50%);
}

.position-arrow {
  color: #f44336;
  font-size: 20px;
  display: block;
  text-align: center;
}

/* 起始点 */
.start-point {
  position: absolute;
  transform: translate(-50%, -50%);
}

.start-marker {
  font-size: 12px;
}

/* 轨迹信息 */
.trajectory-info {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 6px;
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
}

.info-text {
  font-size: 12px;
  color: #666;
}

/* 轨迹控制区域 */
.trajectory-controls {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background-color: #f0f0f0;
  border-radius: 6px;
}

.control-hint {
  font-size: 11px;
  color: #888;
  flex: 1;
}

.reset-view-btn {
  background-color: #2196f3;
  color: white;
  font-size: 12px;
  padding: 4px 12px;
  border: none;
}

/* 状态信息面板 */
.status-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
  z-index: 1000;
  min-width: 280px;
}

.status-header {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.status-close {
  background-color: transparent;
  border: 1px solid #ddd;
  font-size: 16px;
}

.status-content {
  padding: 15px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.status-label {
  color: #666;
  font-size: 14px;
}

.status-value {
  font-size: 14px;
  font-weight: bold;
}

.status-value.active {
  color: #4caf50;
}

.status-value.inactive {
  color: #f44336;
}

.status-value.unsupported {
  color: #999;
}