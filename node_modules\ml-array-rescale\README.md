# array-rescale

  [![NPM version][npm-image]][npm-url]
  [![npm download][download-image]][download-url]

Rescale an array into a range.

## Installation

`$ npm install --save ml-array-rescale`

## Usage

### `rescale(input[, options])`

Rescales the values in the `input` so they fit between two new values.

__Options:__

* `min`: the new minimum value (default: 0)
* `max`: the new maximum value (default: 1)
* `output`: an array to use for output. You can pass `input` here to get an in-place modification.

```js
import rescale from 'ml-array-rescale';

const result = rescale([0, 1, 2, 3, 4]);
// [0, 0.25, 0.5, 0.75, 1]
```

## License

  [MIT](./LICENSE)

[npm-image]: https://img.shields.io/npm/v/ml-array-rescale.svg?style=flat-square
[npm-url]: https://npmjs.org/package/ml-array-rescale
[download-image]: https://img.shields.io/npm/dm/ml-array-rescale.svg?style=flat-square
[download-url]: https://npmjs.org/package/ml-array-rescale
