{"name": "node-abi", "version": "2.30.1", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "main": "index.js", "scripts": {"semantic-release": "semantic-release", "test": "tape test/index.js", "travis-deploy-once": "travis-deploy-once", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "repository": {"type": "git", "url": "https://github.com/lgeiger/node-abi.git"}, "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "homepage": "https://github.com/lgeiger/node-abi#readme", "devDependencies": {"got": "^10.6.0", "semantic-release": "^15.8.0", "tape": "^4.6.3", "travis-deploy-once": "^5.0.1"}, "dependencies": {"semver": "^5.4.1"}}