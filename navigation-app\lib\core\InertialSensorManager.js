/**
 * 惯导传感器管理器
 * 专门为库封装的传感器数据采集和预处理模块
 */

class InertialSensorManager {
  constructor(config = {}) {
    this.config = {
      sampleRate: config.sampleRate || 50,
      enableAccelerometer: true,
      enableGyroscope: true,
      enableMagnetometer: true,
      enableBarometer: false,
      // 数据预处理配置
      enableFiltering: config.enableFiltering !== false,
      filterWindow: config.filterWindow || 5,
      enableCalibration: config.enableCalibration !== false,
      ...config
    };
    
    // 传感器状态
    this.sensorStatus = {
      accelerometer: { available: false, active: false, lastData: null },
      gyroscope: { available: false, active: false, lastData: null },
      magnetometer: { available: false, active: false, lastData: null },
      barometer: { available: false, active: false, lastData: null }
    };
    
    // 数据缓存
    this.dataBuffer = {
      accelerometer: [],
      gyroscope: [],
      magnetometer: [],
      barometer: []
    };
    
    // 校准参数
    this.calibration = {
      accelerometerBias: config.calibration?.accelerometerBias || [0, 0, 0],
      gyroscopeBias: config.calibration?.gyroscopeBias || [0, 0, 0],
      magnetometerBias: config.calibration?.magnetometerBias || [0, 0, 0],
      accelerometerScale: config.calibration?.accelerometerScale || [1, 1, 1],
      magneticDeclination: config.calibration?.magneticDeclination || 0
    };
    
    // 滤波器
    this.filters = {
      accelerometer: new MovingAverageFilter(this.config.filterWindow),
      gyroscope: new MovingAverageFilter(this.config.filterWindow),
      magnetometer: new MovingAverageFilter(this.config.filterWindow)
    };
    
    // 采样控制
    this.samplingInterval = null;
    this.lastSampleTime = 0;
    this.sampleInterval = 1000 / this.config.sampleRate;
    
    // 数据回调
    this.dataCallback = null;
    
    // 运行状态
    this.isRunning = false;
    this.isPaused = false;
    
    // 错误处理
    this.errorCallback = null;
    
    console.log('📡 传感器管理器初始化完成', {
      sampleRate: this.config.sampleRate,
      enableFiltering: this.config.enableFiltering
    });
  }
  
  /**
   * 启动传感器采集
   */
  async start(config = {}) {
    if (this.isRunning) {
      console.warn('⚠️ 传感器管理器已在运行');
      return;
    }
    
    try {
      console.log('🚀 启动传感器采集...');
      
      // 更新配置
      this.updateConfig(config);
      
      // 检查传感器可用性
      await this.checkSensorAvailability();
      
      // 启动传感器，允许部分失败
      const sensorResults = { accelerometer: false, gyroscope: false, magnetometer: false };
      
      // 启动加速计（必需）
      try {
        await this.startAccelerometer();
        sensorResults.accelerometer = true;
      } catch (error) {
        console.error('❌ 加速计启动失败，这将影响定位功能:', error);
        throw error; // 加速计是必需的
      }
      
      // 启动陀螺仪（可选但重要）
      try {
        await this.startGyroscope();
        sensorResults.gyroscope = true;
      } catch (error) {
        console.warn('⚠️ 陀螺仪启动失败，将影响航向精度:', error);
      }
      
      // 启动罗盘（可选）
      try {
        await this.startMagnetometer();
        sensorResults.magnetometer = true;
      } catch (error) {
        console.warn('⚠️ 罗盘启动失败，将使用陀螺仪航向:', error);
      }
      
      if (this.config.enableBarometer) {
        try {
          await this.startBarometer();
        } catch (error) {
          console.warn('⚠️ 气压计启动失败:', error);
        }
      }
      
      // 启动数据采样
      this.startSampling();
      
      this.isRunning = true;
      
      // 检查启动状态
      const activeCount = Object.values(sensorResults).filter(Boolean).length;
      console.log(`✅ 传感器采集启动完成，活跃传感器: ${activeCount}/3`, sensorResults);
      
    } catch (error) {
      console.error('❌ 传感器启动失败:', error);
      this.handleError('sensor_start_failed', error);
      throw error;
    }
  }
  
  /**
   * 停止传感器采集
   */
  stop() {
    if (!this.isRunning) {
      return;
    }
    
    console.log('⏹️ 停止传感器采集');
    
    // 停止采样
    this.stopSampling();
    
    // 停止各个传感器
    this.stopAccelerometer();
    this.stopGyroscope();
    this.stopMagnetometer();
    this.stopBarometer();
    
    // 清空缓存
    this.clearBuffers();
    
    this.isRunning = false;
    this.isPaused = false;
  }
  
  /**
   * 暂停传感器采集
   */
  pause() {
    if (!this.isRunning || this.isPaused) {
      return;
    }
    
    this.isPaused = true;
    console.log('⏸️ 暂停传感器采集');
  }
  
  /**
   * 恢复传感器采集
   */
  resume() {
    if (!this.isRunning || !this.isPaused) {
      return;
    }
    
    this.isPaused = false;
    console.log('▶️ 恢复传感器采集');
  }
  
  /**
   * 重启传感器
   */
  async restart() {
    console.log('🔄 重启传感器管理器');
    this.stop();
    await new Promise(resolve => setTimeout(resolve, 500));
    await this.start();
  }
  
  /**
   * 检查传感器可用性
   */
  async checkSensorAvailability() {
    return new Promise((resolve) => {
      console.log('🔍 检查传感器可用性...');
      
      // 使用 getSystemInfo 检查传感器支持情况
      wx.getSystemInfo({
        success: (res) => {
          // 检查加速计支持
          this.sensorStatus.accelerometer.available = res.platform === 'android' || res.platform === 'ios';
          
          // 检查陀螺仪支持
          this.sensorStatus.gyroscope.available = res.platform === 'android' || res.platform === 'ios';
          
          // 检查罗盘支持
          this.sensorStatus.magnetometer.available = res.platform === 'android' || res.platform === 'ios';
          
          console.log('📊 传感器可用性检查完成:', this.sensorStatus);
          resolve();
        },
        fail: () => {
          // 如果获取系统信息失败，默认认为所有传感器都可用
          this.sensorStatus.accelerometer.available = true;
          this.sensorStatus.gyroscope.available = true;
          this.sensorStatus.magnetometer.available = true;
          console.log('📊 传感器可用性检查完成(默认):', this.sensorStatus);
          resolve();
        }
      });
    });
  }
  
  /**
   * 启动加速计
   */
  async startAccelerometer() {
    if (!this.sensorStatus.accelerometer.available) {
      throw new Error('加速计不可用');
    }
    
    // 如果已经在运行，直接返回
    if (this.sensorStatus.accelerometer.active) {
      console.log('ℹ️ 加速计已在运行中');
      return;
    }
    
    // 确保完全停止之前的操作
    this.stopAccelerometer();
    
    // 等待更长时间确保停止操作完成
    await new Promise(resolve => setTimeout(resolve, 300));
    
    wx.onAccelerometerChange((data) => {
      if (this.isPaused) return;
      
      // 应用校准
      const calibratedData = this.calibrateAccelerometer(data);
      
      // 添加到缓存
      this.dataBuffer.accelerometer.push({
        ...calibratedData,
        timestamp: Date.now()
      });
      
      // 限制缓存大小
      if (this.dataBuffer.accelerometer.length > 100) {
        this.dataBuffer.accelerometer.shift();
      }
      
      this.sensorStatus.accelerometer.lastData = calibratedData;
    });
    
    wx.startAccelerometer({
      interval: 'game', // 20ms采样间隔
      success: () => {
        this.sensorStatus.accelerometer.active = true;
        console.log('✅ 加速计启动成功');
      },
      fail: (error) => {
        console.error('❌ 加速计启动失败:', error);
        this.sensorStatus.accelerometer.active = false;
        
        // 如果是因为已启用的错误，直接使用现有的传感器
        if (error.errMsg && error.errMsg.includes('has enable')) {
          console.log('ℹ️ 检测到传感器已启用，直接使用现有传感器');
          this.sensorStatus.accelerometer.active = true;
          // 设置一个简单的数据模拟，以防传感器回调没有设置
          this.setupFallbackAccelerometer();
        } else {
          throw error;
        }
      }
    });
  }
  
  /**
   * 启动陀螺仪
   */
  async startGyroscope() {
    if (!this.sensorStatus.gyroscope.available) {
      throw new Error('陀螺仪不可用');
    }
    
    // 如果已经在运行，直接返回
    if (this.sensorStatus.gyroscope.active) {
      console.log('ℹ️ 陀螺仪已在运行中');
      return;
    }
    
    // 确保完全停止之前的操作
    this.stopGyroscope();
    
    // 等待更长时间确保停止操作完成
    await new Promise(resolve => setTimeout(resolve, 300));
    
    wx.onGyroscopeChange((data) => {
      if (this.isPaused) return;
      
      // 应用校准
      const calibratedData = this.calibrateGyroscope(data);
      
      // 添加到缓存
      this.dataBuffer.gyroscope.push({
        ...calibratedData,
        timestamp: Date.now()
      });
      
      // 限制缓存大小
      if (this.dataBuffer.gyroscope.length > 100) {
        this.dataBuffer.gyroscope.shift();
      }
      
      this.sensorStatus.gyroscope.lastData = calibratedData;
    });
    
    wx.startGyroscope({
      interval: 'game', // 20ms采样间隔
      success: () => {
        this.sensorStatus.gyroscope.active = true;
        console.log('✅ 陀螺仪启动成功');
      },
      fail: (error) => {
        console.error('❌ 陀螺仪启动失败:', error);
        this.sensorStatus.gyroscope.active = false;
        
        // 如果是因为已启用的错误，尝试强制重启
        if (error.errMsg && error.errMsg.includes('has enable')) {
          console.log('🔄 检测到陀螺仪已启用，尝试强制重启...');
          setTimeout(async () => {
            await this.forceRestartGyroscope();
          }, 500);
        } else {
          throw error;
        }
      }
    });
  }
  
  /**
   * 启动罗盘
   */
  async startMagnetometer() {
    if (!this.sensorStatus.magnetometer.available) {
      throw new Error('罗盘不可用');
    }
    
    // 如果已经在运行，直接返回
    if (this.sensorStatus.magnetometer.active) {
      console.log('ℹ️ 罗盘已在运行中');
      return;
    }
    
    // 确保完全停止之前的操作
    this.stopMagnetometer();
    
    // 等待更长时间确保停止操作完成
    await new Promise(resolve => setTimeout(resolve, 300));
    
    wx.onCompassChange((data) => {
      if (this.isPaused) return;
      
      // 验证罗盘数据质量
      if (!this.isValidCompassData(data)) {
        console.warn('⚠️ 罗盘数据质量不佳，跳过本次更新');
        return;
      }
      
      // 转换并增强罗盘数据格式
      const magnetometerData = this.enhanceCompassData(data);
      
      // 应用校准和滤波
      const calibratedData = this.calibrateMagnetometer(magnetometerData);
      const filteredData = this.applyMagnetometerFiltering(calibratedData);
      
      // 添加到缓存
      this.dataBuffer.magnetometer.push({
        ...filteredData,
        timestamp: Date.now(),
        rawAccuracy: data.accuracy
      });
      
      // 限制缓存大小
      if (this.dataBuffer.magnetometer.length > 100) {
        this.dataBuffer.magnetometer.shift();
      }
      
      this.sensorStatus.magnetometer.lastData = filteredData;
    });
    
    wx.startCompass({
      success: () => {
        this.sensorStatus.magnetometer.active = true;
        console.log('✅ 罗盘启动成功');
      },
      fail: (error) => {
        console.error('❌ 罗盘启动失败:', error);
        this.sensorStatus.magnetometer.active = false;
        
        // 如果是因为已启用的错误，直接使用现有的传感器
        if (error.errMsg && error.errMsg.includes('has enable')) {
          console.log('ℹ️ 检测到罗盘已启用，直接使用现有传感器');
          this.sensorStatus.magnetometer.active = true;
          // 设置一个简单的数据模拟，以防传感器回调没有设置
          this.setupFallbackMagnetometer();
        } else {
          throw error;
        }
      }
    });
  }
  
  /**
   * 启动气压计（如果支持）
   */
  async startBarometer() {
    // 微信小程序暂不支持气压计，保留接口
    this.sensorStatus.barometer.available = false;
    console.log('ℹ️ 气压计暂不支持');
  }
  
  /**
   * 停止各个传感器
   */
  stopAccelerometer() {
    if (!this.sensorStatus.accelerometer.active) {
      return; // 已经停止了
    }
    
    try {
      wx.stopAccelerometer({
        success: () => {
          this.sensorStatus.accelerometer.active = false;
          console.log('⏹️ 加速计已停止');
        },
        fail: (error) => {
          console.log('ℹ️ 加速计停止失败:', error.errMsg || error);
          // 强制设置为停止状态
          this.sensorStatus.accelerometer.active = false;
        }
      });
    } catch (error) {
      console.log('ℹ️ 加速计停止异常:', error.errMsg || error);
      this.sensorStatus.accelerometer.active = false;
    }
  }
  
  stopGyroscope() {
    if (!this.sensorStatus.gyroscope.active) {
      return; // 已经停止了
    }
    
    try {
      wx.stopGyroscope({
        success: () => {
          this.sensorStatus.gyroscope.active = false;
          console.log('⏹️ 陀螺仪已停止');
        },
        fail: (error) => {
          console.log('ℹ️ 陀螺仪停止失败:', error.errMsg || error);
          // 强制设置为停止状态
          this.sensorStatus.gyroscope.active = false;
        }
      });
    } catch (error) {
      console.log('ℹ️ 陀螺仪停止异常:', error.errMsg || error);
      this.sensorStatus.gyroscope.active = false;
    }
  }
  
  stopMagnetometer() {
    if (!this.sensorStatus.magnetometer.active) {
      return; // 已经停止了
    }
    
    try {
      wx.stopCompass({
        success: () => {
          this.sensorStatus.magnetometer.active = false;
          console.log('⏹️ 罗盘已停止');
        },
        fail: (error) => {
          console.log('ℹ️ 罗盘停止失败:', error.errMsg || error);
          // 强制设置为停止状态
          this.sensorStatus.magnetometer.active = false;
        }
      });
    } catch (error) {
      console.log('ℹ️ 罗盘停止异常:', error.errMsg || error);
      this.sensorStatus.magnetometer.active = false;
    }
  }
  
  stopBarometer() {
    // 预留接口
  }
  
  /**
   * 启动数据采样
   */
  startSampling() {
    console.log('🔄 启动数据采样，间隔:', this.sampleInterval + 'ms');
    
    this.samplingInterval = setInterval(() => {
      if (this.isPaused || !this.isRunning) return;
      
      const now = Date.now();
      if (now - this.lastSampleTime < this.sampleInterval) {
        return;
      }
      
      this.lastSampleTime = now;
      
      // 采集当前传感器数据
      const sensorData = this.collectCurrentData();
      
      if (sensorData) {
        console.log('📊 采集到传感器数据:', {
          timestamp: sensorData.timestamp,
          hasAccelerometer: !!sensorData.accelerometer,
          hasGyroscope: !!sensorData.gyroscope,
          hasMagnetometer: !!sensorData.magnetometer,
          accelerometerValues: sensorData.accelerometer ? 
            `[${sensorData.accelerometer.x.toFixed(3)}, ${sensorData.accelerometer.y.toFixed(3)}, ${sensorData.accelerometer.z.toFixed(3)}]` : 'null'
        });
        
        // 触发数据回调
        if (this.dataCallback) {
          this.dataCallback(sensorData);
        } else {
          console.warn('⚠️ 数据回调未设置');
        }
      } else {
        console.warn('⚠️ 未能采集到有效传感器数据');
      }
      
    }, this.sampleInterval);
  }
  
  /**
   * 停止数据采样
   */
  stopSampling() {
    if (this.samplingInterval) {
      clearInterval(this.samplingInterval);
      this.samplingInterval = null;
    }
  }
  
  /**
   * 收集当前传感器数据
   */
  collectCurrentData() {
    const timestamp = Date.now();
    
    // 检查是否有有效数据
    const hasAccelerometer = this.sensorStatus.accelerometer.lastData;
    const hasGyroscope = this.sensorStatus.gyroscope.lastData;
    const hasMagnetometer = this.sensorStatus.magnetometer.lastData;
    
    // 至少需要加速计数据
    if (!hasAccelerometer) {
      console.warn('⚠️ 缺少加速计数据');
      return null;
    }
    
    const rawData = {
      timestamp,
      accelerometer: this.sensorStatus.accelerometer.lastData,
      gyroscope: hasGyroscope ? this.sensorStatus.gyroscope.lastData : null,
      magnetometer: hasMagnetometer ? this.sensorStatus.magnetometer.lastData : null
    };
    
    // 应用滤波
    let processedData = rawData;
    if (this.config.enableFiltering) {
      processedData = this.applyFiltering(rawData);
    }
    
    // 添加调试信息
    if (processedData) {
      console.log('📊 传感器数据采集成功', {
        accelerometer: !!processedData.accelerometer,
        gyroscope: !!processedData.gyroscope,
        magnetometer: !!processedData.magnetometer
      });
    }
    
    return processedData;
  }
  
  /**
   * 应用数据滤波
   */
  applyFiltering(data) {
    const filtered = { ...data };
    
    try {
      if (data.accelerometer) {
        filtered.accelerometer = this.filters.accelerometer.filter(data.accelerometer);
      }
      
      if (data.gyroscope) {
        filtered.gyroscope = this.filters.gyroscope.filter(data.gyroscope);
      }
      
      if (data.magnetometer) {
        filtered.magnetometer = this.filters.magnetometer.filter(data.magnetometer);
      }
      
      return filtered;
    } catch (error) {
      console.warn('⚠️ 滤波处理失败:', error);
      return data; // 返回原始数据
    }
  }
  
  /**
   * 加速计校准
   */
  calibrateAccelerometer(data) {
    const bias = this.calibration.accelerometerBias;
    const scale = this.calibration.accelerometerScale;
    
    return {
      x: (data.x - bias[0]) * scale[0],
      y: (data.y - bias[1]) * scale[1],
      z: (data.z - bias[2]) * scale[2]
    };
  }
  
  /**
   * 陀螺仪校准
   */
  calibrateGyroscope(data) {
    const bias = this.calibration.gyroscopeBias;
    
    return {
      x: data.x - bias[0],
      y: data.y - bias[1],
      z: data.z - bias[2]
    };
  }
  
  /**
   * 磁力计校准
   */
  calibrateMagnetometer(data) {
    // 应用磁偏角校正
    const declination = this.calibration.magneticDeclination;
    const correctedDirection = (data.direction + declination + 360) % 360;
    
    return {
      ...data,
      direction: correctedDirection,
      x: Math.cos((correctedDirection - 90) * Math.PI / 180),
      y: Math.sin((correctedDirection - 90) * Math.PI / 180)
    };
  }
  
  /**
   * 验证罗盘数据质量
   * @param {Object} data - 原始罗盘数据
   * @returns {boolean} 数据是否有效
   */
  isValidCompassData(data) {
    // 检查基本数据结构
    if (!data || typeof data !== 'object') {
      return false;
    }
    
    // 检查方向数据
    if (typeof data.direction !== 'number' || isNaN(data.direction)) {
      return false;
    }
    
    // 检查方向角度范围
    if (data.direction < 0 || data.direction >= 360) {
      return false;
    }
    
    // 检查精度数据
    if (data.accuracy !== undefined) {
      if (typeof data.accuracy !== 'number' || isNaN(data.accuracy)) {
        return false;
      }
      
      // 如果精度太低，可能不可信
      if (data.accuracy < 0 || data.accuracy > 4) {
        return false;
      }
    }
    
    return true;
  }
  
  /**
   * 增强罗盘数据
   * @param {Object} data - 原始罗盘数据
   * @returns {Object} 增强后的数据
   */
  enhanceCompassData(data) {
    // 基本转换
    const direction = data.direction;
    const accuracy = data.accuracy || 3; // 默认中等精度
    
    // 转换为3D磁场向量（模拟）
    // 微信小程序只提供方向角，需要模拟磁场强度
    const magneticStrength = this.estimateMagneticStrength(accuracy);
    
    return {
      x: magneticStrength * Math.cos((direction - 90) * Math.PI / 180),
      y: magneticStrength * Math.sin((direction - 90) * Math.PI / 180),
      z: magneticStrength * 0.1, // 模拟垂直分量
      direction: direction,
      accuracy: accuracy,
      strength: magneticStrength,
      quality: this.assessCompassQuality(data)
    };
  }
  
  /**
   * 估计磁场强度
   * @param {number} accuracy - 精度等级
   * @returns {number} 估计的磁场强度
   */
  estimateMagneticStrength(accuracy) {
    // 基于精度估计磁场强度（模拟真实环境）
    const baseStrength = 1.0; // 标准化强度
    
    switch (accuracy) {
      case 1: // 高精度
        return baseStrength * (0.9 + Math.random() * 0.1);
      case 2: // 中高精度
        return baseStrength * (0.8 + Math.random() * 0.2);
      case 3: // 中等精度
        return baseStrength * (0.7 + Math.random() * 0.3);
      case 4: // 低精度
        return baseStrength * (0.5 + Math.random() * 0.4);
      default:
        return baseStrength * 0.8;
    }
  }
  
  /**
   * 评估罗盘质量
   * @param {Object} data - 罗盘数据
   * @returns {string} 质量等级
   */
  assessCompassQuality(data) {
    const accuracy = data.accuracy || 3;
    
    // 检查是否有磁场干扰
    if (this.detectMagneticInterference(data)) {
      return 'poor';
    }
    
    // 基于精度评估质量
    if (accuracy <= 1) return 'excellent';
    if (accuracy <= 2) return 'good';
    if (accuracy <= 3) return 'fair';
    return 'poor';
  }
  
  /**
   * 检测磁场干扰
   * @param {Object} data - 罗盘数据
   * @returns {boolean} 是否有干扰
   */
  detectMagneticInterference(data) {
    // 检查方向角跳变 - 放宽干扰检测限制
    if (this.sensorStatus.magnetometer.lastData) {
      const lastDirection = this.sensorStatus.magnetometer.lastData.direction;
      const currentDirection = data.direction;
      
      // 计算角度差（考虑360度循环）
      let angleDiff = Math.abs(currentDirection - lastDirection);
      if (angleDiff > 180) {
        angleDiff = 360 - angleDiff;
      }
      
      // 放宽干扰检测：更大的角度变化才认为是干扰，更多次数才判定为持续干扰
      if (angleDiff > 90) { // 从45度放宽到90度
        this.interferenceCount = (this.interferenceCount || 0) + 1;
        console.log(`🧭 检测到方向角大幅变化: ${angleDiff.toFixed(1)}°, 干扰计数: ${this.interferenceCount}`);
        return this.interferenceCount > 10; // 从3次增加到10次
      } else {
        // 逐步减少干扰计数，而不是直接归零
        if (this.interferenceCount > 0) {
          this.interferenceCount = Math.max(0, this.interferenceCount - 1);
        }
      }
    }
    
    return false;
  }
  
  /**
   * 应用磁力计滤波
   * @param {Object} data - 校准后的数据
   * @returns {Object} 滤波后的数据
   */
  applyMagnetometerFiltering(data) {
    if (!this.config.enableFiltering) {
      return data;
    }
    
    // 应用移动平均滤波
    const filtered = this.filters.magnetometer.filter(data);
    
    // 保留原始方向和质量信息
    return {
      ...filtered,
      direction: this.filterDirection(data.direction),
      accuracy: data.accuracy,
      quality: data.quality,
      strength: data.strength
    };
  }
  
  /**
   * 方向角滤波（处理360度边界）
   * @param {number} newDirection - 新方向角
   * @returns {number} 滤波后的方向角
   */
  filterDirection(newDirection) {
    if (!this.lastFilteredDirection) {
      this.lastFilteredDirection = newDirection;
      return newDirection;
    }
    
    // 处理360度边界问题
    let diff = newDirection - this.lastFilteredDirection;
    if (diff > 180) {
      diff -= 360;
    } else if (diff < -180) {
      diff += 360;
    }
    
    // 应用一阶低通滤波
    const alpha = 0.3; // 滤波系数
    this.lastFilteredDirection += diff * alpha;
    
    // 规范化到0-360度
    this.lastFilteredDirection = (this.lastFilteredDirection + 360) % 360;
    
    return this.lastFilteredDirection;
  }

  /**
   * 传感器校准
   */
  async calibrate(calibrationData = {}) {
    console.log('🔧 开始传感器校准...');
    
    try {
      // 静态校准 - 收集静止状态下的传感器数据
      const staticData = await this.collectStaticCalibrationData();
      
      // 计算偏置
      this.calibration.accelerometerBias = this.calculateAccelerometerBias(staticData.accelerometer);
      this.calibration.gyroscopeBias = this.calculateGyroscopeBias(staticData.gyroscope);
      
      // 应用用户提供的校准数据
      if (calibrationData.stepLength) {
        this.calibration.stepLength = calibrationData.stepLength;
      }
      
      if (calibrationData.magneticDeclination !== undefined) {
        this.calibration.magneticDeclination = calibrationData.magneticDeclination;
      }
      
      console.log('✅ 传感器校准完成:', this.calibration);
      
      return {
        success: true,
        parameters: this.calibration
      };
      
    } catch (error) {
      console.error('❌ 传感器校准失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * 收集静态校准数据
   */
  async collectStaticCalibrationData() {
    return new Promise((resolve) => {
      const calibrationData = {
        accelerometer: [],
        gyroscope: []
      };
      
      let sampleCount = 0;
      const targetSamples = 100; // 2秒数据
      
      const collectData = () => {
        if (this.sensorStatus.accelerometer.lastData) {
          calibrationData.accelerometer.push(this.sensorStatus.accelerometer.lastData);
        }
        
        if (this.sensorStatus.gyroscope.lastData) {
          calibrationData.gyroscope.push(this.sensorStatus.gyroscope.lastData);
        }
        
        sampleCount++;
        
        if (sampleCount >= targetSamples) {
          resolve(calibrationData);
        } else {
          setTimeout(collectData, 20);
        }
      };
      
      collectData();
    });
  }
  
  /**
   * 计算加速计偏置
   */
  calculateAccelerometerBias(data) {
    if (data.length === 0) return [0, 0, 0];
    
    const sum = data.reduce((acc, curr) => ({
      x: acc.x + curr.x,
      y: acc.y + curr.y,
      z: acc.z + curr.z
    }), { x: 0, y: 0, z: 0 });
    
    const avg = {
      x: sum.x / data.length,
      y: sum.y / data.length,
      z: sum.z / data.length
    };
    
    // Z轴应该是重力加速度，减去理论值
    return [avg.x, avg.y, avg.z - 1.0]; // 1g = 1.0 in normalized units
  }
  
  /**
   * 计算陀螺仪偏置
   */
  calculateGyroscopeBias(data) {
    if (data.length === 0) return [0, 0, 0];
    
    const sum = data.reduce((acc, curr) => ({
      x: acc.x + curr.x,
      y: acc.y + curr.y,
      z: acc.z + curr.z
    }), { x: 0, y: 0, z: 0 });
    
    return [
      sum.x / data.length,
      sum.y / data.length,
      sum.z / data.length
    ];
  }
  
  /**
   * 清空数据缓存
   */
  clearBuffers() {
    this.dataBuffer.accelerometer = [];
    this.dataBuffer.gyroscope = [];
    this.dataBuffer.magnetometer = [];
    this.dataBuffer.barometer = [];
  }
  
  /**
   * 设置数据回调
   */
  setDataCallback(callback) {
    this.dataCallback = callback;
  }
  
  /**
   * 设置错误回调
   */
  setErrorCallback(callback) {
    this.errorCallback = callback;
  }
  
  /**
   * 更新配置
   */
  updateConfig(config) {
    this.config = { ...this.config, ...config };
    
    // 更新采样间隔
    if (config.sampleRate) {
      this.sampleInterval = 1000 / config.sampleRate;
    }
    
    // 更新校准参数
    if (config.calibration) {
      this.calibration = { ...this.calibration, ...config.calibration };
    }
    
    // 更新滤波窗口
    if (config.filterWindow) {
      this.filters.accelerometer.setWindowSize(config.filterWindow);
      this.filters.gyroscope.setWindowSize(config.filterWindow);
      this.filters.magnetometer.setWindowSize(config.filterWindow);
    }
  }
  
  /**
   * 获取传感器状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      isPaused: this.isPaused,
      sensors: { ...this.sensorStatus },
      calibration: { ...this.calibration },
      bufferSizes: {
        accelerometer: this.dataBuffer.accelerometer.length,
        gyroscope: this.dataBuffer.gyroscope.length,
        magnetometer: this.dataBuffer.magnetometer.length
      }
    };
  }
  
  /**
   * 错误处理
   */
  handleError(type, error) {
    const errorInfo = {
      type,
      message: error.message || error,
      timestamp: Date.now(),
      sensorStatus: this.sensorStatus
    };
    
    if (this.errorCallback) {
      this.errorCallback(errorInfo);
    }
    
    console.error('传感器管理器错误:', errorInfo);
  }
  
  /**
   * 强制重启加速计
   */
  async forceRestartAccelerometer() {
    console.log('🔧 强制重启加速计...');
    
    // 标记为非活跃状态
    this.sensorStatus.accelerometer.active = false;
    
    // 多次尝试停止
    for (let i = 0; i < 5; i++) {
      try {
        wx.stopAccelerometer({
          success: () => {
            console.log(`停止加速计成功 - 尝试 ${i + 1}`);
          },
          fail: (error) => {
            console.log(`停止加速计失败 - 尝试 ${i + 1}:`, error);
          }
        });
        await new Promise(resolve => setTimeout(resolve, 300));
      } catch (e) {
        console.log(`停止加速计异常 - 尝试 ${i + 1}:`, e);
      }
    }
    
    // 等待更长时间确保完全停止
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 重新启动
    try {
      console.log('🔄 尝试重新启动加速计...');
      await this.startAccelerometer();
    } catch (error) {
      console.error('❌ 强制重启加速计失败:', error);
      // 最后尝试：直接标记为已启动状态，跳过启动过程
      this.sensorStatus.accelerometer.active = true;
      console.log('⚠️ 假定加速计已启动，继续运行');
    }
  }
  
  /**
   * 强制重启陀螺仪
   */
  async forceRestartGyroscope() {
    console.log('🔧 强制重启陀螺仪...');
    
    // 多次尝试停止
    for (let i = 0; i < 3; i++) {
      try {
        wx.stopGyroscope();
        await new Promise(resolve => setTimeout(resolve, 200));
      } catch (e) {
        console.log(`停止陀螺仪尝试 ${i + 1} 失败:`, e);
      }
    }
    
    // 重置状态
    this.sensorStatus.gyroscope.active = false;
    
    // 等待更长时间
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 重新启动
    try {
      await this.startGyroscope();
    } catch (error) {
      console.error('❌ 强制重启陀螺仪失败:', error);
    }
  }
  
  /**
   * 强制重启罗盘
   */
  async forceRestartMagnetometer() {
    console.log('🔧 强制重启罗盘...');
    
    // 标记为非活跃状态
    this.sensorStatus.magnetometer.active = false;
    
    // 多次尝试停止
    for (let i = 0; i < 5; i++) {
      try {
        wx.stopCompass({
          success: () => {
            console.log(`停止罗盘成功 - 尝试 ${i + 1}`);
          },
          fail: (error) => {
            console.log(`停止罗盘失败 - 尝试 ${i + 1}:`, error);
          }
        });
        await new Promise(resolve => setTimeout(resolve, 300));
      } catch (e) {
        console.log(`停止罗盘异常 - 尝试 ${i + 1}:`, e);
      }
    }
    
    // 等待更长时间确保完全停止
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 重新启动
    try {
      console.log('🔄 尝试重新启动罗盘...');
      await this.startMagnetometer();
    } catch (error) {
      console.error('❌ 强制重启罗盘失败:', error);
      // 最后尝试：直接标记为已启动状态，跳过启动过程
      this.sensorStatus.magnetometer.active = true;
      console.log('⚠️ 假定罗盘已启动，继续运行');
    }
  }

  /**
   * 清理缓冲区（内存管理）
   */
  cleanupBuffers() {
    // 清理数据缓冲区，保留最近的数据
    const keepSize = 20; // 保留最近20条数据
    
    if (this.dataBuffer.accelerometer.length > keepSize) {
      this.dataBuffer.accelerometer = this.dataBuffer.accelerometer.slice(-keepSize);
    }
    
    if (this.dataBuffer.gyroscope.length > keepSize) {
      this.dataBuffer.gyroscope = this.dataBuffer.gyroscope.slice(-keepSize);
    }
    
    if (this.dataBuffer.magnetometer.length > keepSize) {
      this.dataBuffer.magnetometer = this.dataBuffer.magnetometer.slice(-keepSize);
    }
    
    if (this.dataBuffer.barometer.length > keepSize) {
      this.dataBuffer.barometer = this.dataBuffer.barometer.slice(-keepSize);
    }
    
    console.log('🧹 传感器缓冲区清理完成');
  }
  
  /**
   * 设置回退加速计传感器
   * 当加速计因"已启用"错误无法正常启动时使用
   */
  setupFallbackAccelerometer() {
    console.log('🔧 设置回退加速计传感器...');
    
    // 尝试直接监听加速计数据，不调用启动方法
    try {
      wx.onAccelerometerChange((data) => {
        if (this.isPaused) return;
        
        console.log('📱 回退加速计数据:', data);
        
        // 应用校准
        const calibratedData = this.calibrateAccelerometer(data);
        
        // 添加到缓存
        this.dataBuffer.accelerometer.push({
          ...calibratedData,
          timestamp: Date.now()
        });
        
        // 限制缓存大小
        if (this.dataBuffer.accelerometer.length > 100) {
          this.dataBuffer.accelerometer.shift();
        }
        
        this.sensorStatus.accelerometer.lastData = calibratedData;
        
        // 执行回调
        if (this.onDataCallback) {
          this.onDataCallback('accelerometer', calibratedData);
        }
      });
      
      console.log('✅ 回退加速计设置成功');
    } catch (error) {
      console.error('❌ 回退加速计设置失败:', error);
    }
  }
  
  /**
   * 设置回退磁力计传感器
   * 当磁力计因"已启用"错误无法正常启动时使用
   */
  setupFallbackMagnetometer() {
    console.log('🔧 设置回退磁力计传感器...');
    
    // 尝试直接监听磁力计数据，不调用启动方法
    try {
      wx.onCompassChange((data) => {
        if (this.isPaused) return;
        
        console.log('🧭 回退磁力计数据:', data);
        
        // 应用校准和滤波
        const calibratedData = this.calibrateMagnetometer(data);
        const filteredData = this.applyMagnetometerFiltering(calibratedData);
        
        // 检测磁场干扰（使用放宽的阈值）
        if (this.detectMagneticInterference(filteredData)) {
          console.warn('⚠️ 检测到磁场干扰，数据质量可能受影响');
          filteredData.quality = Math.max(0.1, filteredData.quality * 0.5);
        }
        
        // 添加到缓存
        this.dataBuffer.magnetometer.push({
          ...filteredData,
          timestamp: Date.now()
        });
        
        // 限制缓存大小
        if (this.dataBuffer.magnetometer.length > 100) {
          this.dataBuffer.magnetometer.shift();
        }
        
        this.sensorStatus.magnetometer.lastData = filteredData;
        
        // 执行回调
        if (this.onDataCallback) {
          this.onDataCallback('magnetometer', filteredData);
        }
      });
      
      console.log('✅ 回退磁力计设置成功');
    } catch (error) {
      console.error('❌ 回退磁力计设置失败:', error);
    }
  }
}

// CommonJS导出
module.exports = InertialSensorManager;

/**
 * 移动平均滤波器
 */
class MovingAverageFilter {
  constructor(windowSize = 5) {
    this.windowSize = windowSize;
    this.buffer = {
      x: [],
      y: [],
      z: []
    };
  }
  
  filter(data) {
    // 添加新数据
    this.buffer.x.push(data.x);
    this.buffer.y.push(data.y);
    this.buffer.z.push(data.z);
    
    // 限制缓存大小
    if (this.buffer.x.length > this.windowSize) {
      this.buffer.x.shift();
      this.buffer.y.shift();
      this.buffer.z.shift();
    }
    
    // 计算平均值
    const avg = {
      x: this.buffer.x.reduce((a, b) => a + b, 0) / this.buffer.x.length,
      y: this.buffer.y.reduce((a, b) => a + b, 0) / this.buffer.y.length,
      z: this.buffer.z.reduce((a, b) => a + b, 0) / this.buffer.z.length
    };
    
    // 保留其他属性
    return { ...data, ...avg };
  }
  
  setWindowSize(size) {
    this.windowSize = size;
    
    // 截断缓存
    if (this.buffer.x.length > size) {
      this.buffer.x = this.buffer.x.slice(-size);
      this.buffer.y = this.buffer.y.slice(-size);
      this.buffer.z = this.buffer.z.slice(-size);
    }
  }
  
  reset() {
    this.buffer.x = [];
    this.buffer.y = [];
    this.buffer.z = [];
  }
}

// CommonJS导出
module.exports = InertialSensorManager;