#!/usr/bin/env node

// 首先设置微信模拟环境
import { setupWxMock } from './setup-wx-mock.js';
setupWxMock();

// 然后导入库
import WXInertialNavigation from './WXInertialNavigation.js';

/**
 * 使用模拟环境测试高精度模式
 */
async function testWithMockEnvironment() {
    console.log('🎯 使用模拟环境测试高精度模式\n');
    
    try {
        // 创建高精度模式实例
        console.log('🚀 创建高精度模式实例...');
        const preciseNav = new WXInertialNavigation({
            initialPosition: { x: 0, y: 0, z: 0 },
            mode: 'precise',
            sampleRate: 100
        });
        
        console.log('✅ 实例创建成功');
        
        // 测试启动
        console.log('\n🚀 测试启动...');
        const startResult = await preciseNav.start();
        console.log('启动结果:', startResult);
        
        if (startResult) {
            console.log('✅ 高精度模式启动成功');
            
            // 等待一段时间让传感器数据处理
            console.log('\n⏳ 等待传感器数据处理...');
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 测试位置获取
            console.log('\n📍 测试位置获取...');
            const location = preciseNav.getCurrentLocation();
            console.log('当前位置:', JSON.stringify(location, null, 2));
            
            // 测试统计信息
            console.log('\n📊 测试统计信息...');
            const stats = preciseNav.getStatistics();
            console.log('统计信息:', JSON.stringify(stats, null, 2));
            
            // 测试轨迹
            console.log('\n🛤️ 测试轨迹获取...');
            const trajectory = preciseNav.getTrajectory();
            console.log('轨迹点数:', trajectory.length);
            
            // 停止
            console.log('\n⏹️ 停止系统...');
            preciseNav.stop();
            console.log('✅ 系统已停止');
            
        } else {
            console.log('❌ 启动失败');
        }
        
        return startResult;
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        return false;
    }
}

// 运行测试
testWithMockEnvironment().then(success => {
    console.log('\n' + '='.repeat(50));
    console.log(success ? '✅ 测试成功' : '❌ 测试失败');
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('❌ 测试运行失败:', error);
    process.exit(1);
});