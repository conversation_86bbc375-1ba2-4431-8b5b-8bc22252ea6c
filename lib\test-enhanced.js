#!/usr/bin/env node

// 使用增强的模拟环境
import { enhancedWxMock, stepSimulator } from './enhanced-mock.js';

// 设置全局wx对象
if (typeof wx === 'undefined') {
    global.wx = enhancedWxMock;
}

// 导入库
import WXInertialNavigation from './WXInertialNavigation.js';

/**
 * 使用增强模拟环境测试高精度模式
 */
async function testEnhancedPreciseMode() {
    console.log('🎯 使用增强模拟环境测试高精度模式\n');
    
    try {
        // 创建高精度模式实例
        console.log('🚀 创建高精度模式实例...');
        const preciseNav = new WXInertialNavigation({
            initialPosition: { x: 0, y: 0, z: 0 },
            mode: 'precise',
            sampleRate: 100,
            enableMLA: true
        });
        
        console.log('✅ 实例创建成功');
        
        // 设置MLA节点（模拟环境）
        console.log('📍 设置模拟MLA节点...');
        const mockMlaNodes = [
            { x: 5, y: 5, z: 0, confidence: 0.9, type: 'magnetic', id: 'node_1' },
            { x: 10, y: 10, z: 0, confidence: 0.8, type: 'magnetic', id: 'node_2' },
            { x: 15, y: 15, z: 0, confidence: 0.7, type: 'magnetic', id: 'node_3' }
        ];
        
        // 直接设置MLA节点（在真实环境中应该通过API设置）
        if (preciseNav.positionCalculator && preciseNav.positionCalculator.setMlaNodes) {
            preciseNav.positionCalculator.setMlaNodes(mockMlaNodes);
            console.log('✅ MLA节点设置完成');
        }
        
        // 测试启动
        console.log('\n🚀 测试启动...');
        const startResult = await preciseNav.start();
        console.log('启动结果:', startResult);
        
        if (startResult) {
            console.log('✅ 高精度模式启动成功');
            
            // 等待更长时间让传感器数据处理
            console.log('\n⏳ 等待传感器数据处理（5秒）...');
            for (let i = 1; i <= 5; i++) {
                await new Promise(resolve => setTimeout(resolve, 1000));
                console.log(`   ${i}秒...`);
                
                // 实时显示位置更新
                const location = preciseNav.getCurrentLocation();
                console.log(`   📍 位置: (${location.position.x.toFixed(2)}, ${location.position.y.toFixed(2)}, ${location.position.z.toFixed(2)})`);
                console.log(`   👣 步数: ${location.stepCount}`);
            }
            
            // 最终结果
            console.log('\n📋 最终结果:');
            console.log('-'.repeat(30));
            
            const location = preciseNav.getCurrentLocation();
            console.log('📍 最终位置:', JSON.stringify(location.position, null, 2));
            console.log('🧭 航向角:', location.heading);
            console.log('👣 步数:', location.stepCount);
            console.log('📏 距离:', location.velocity);
            console.log('🎯 置信度:', location.confidence);
            
            const stats = preciseNav.getStatistics();
            console.log('\n📊 统计信息:');
            console.log('-'.repeat(30));
            console.log('运行时间:', (stats.runtime / 1000).toFixed(1), '秒');
            console.log('总步数:', stats.totalSteps);
            console.log('总距离:', stats.totalDistance.toFixed(2), '米');
            console.log('平均置信度:', (stats.averageConfidence * 100).toFixed(1) + '%');
            
            const trajectory = preciseNav.getTrajectory();
            console.log('\n🛤️ 轨迹信息:');
            console.log('-'.repeat(30));
            console.log('轨迹点数:', trajectory.length);
            if (trajectory.length > 0) {
                console.log('最新轨迹点:', JSON.stringify(trajectory[trajectory.length - 1]));
            }
            
            // 停止
            console.log('\n⏹️ 停止系统...');
            preciseNav.stop();
            console.log('✅ 系统已停止');
            
        } else {
            console.log('❌ 启动失败');
        }
        
        return startResult;
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        return false;
    }
}

// 运行测试
testEnhancedPreciseMode().then(success => {
    console.log('\n' + '='.repeat(50));
    console.log(success ? '✅ 高精度模式测试成功' : '❌ 高精度模式测试失败');
    
    if (success) {
        console.log('\n🎉 高精度模式现在可以正常工作了！');
        console.log('📝 问题原因: 之前缺少微信环境和真实的传感器数据');
        console.log('✅ 解决方案: 提供了完整的模拟环境和步态数据');
    }
    
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('❌ 测试运行失败:', error);
    process.exit(1);
});