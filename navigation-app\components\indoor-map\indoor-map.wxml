<!--室内地图组件-->
<view class="map-container" style="width: {{width}}; height: {{height}}px;">
  
  <!-- 地图Canvas -->
  <canvas 
    id="{{canvasId}}" 
    type="2d"
    class="map-canvas"
    bindtouchstart="onTouchStart"
    bindtouchmove="onTouchMove"
    bindtouchend="onTouchEnd"
    bindtap="onMapTap">
  </canvas>
  
  <!-- 地图控制器 -->
  <view class="map-controls">
    
    <!-- 缩放控制 -->
    <view class="zoom-controls">
      <button class="zoom-button" bindtap="zoomMap" data-delta="0.2" size="mini">
        <text class="zoom-icon">+</text>
      </button>
      <button class="zoom-button" bindtap="zoomMap" data-delta="-0.2" size="mini">
        <text class="zoom-icon">-</text>
      </button>
    </view>
    
    <!-- 定位按钮 -->
    <view class="location-controls">
      <button class="location-button" bindtap="centerToUser" size="mini">
        <text class="location-icon">📍</text>
      </button>
    </view>
    
    <!-- 楼层选择器 -->
    <view class="floor-selector" wx:if="{{mode !== 'navigation'}}">
      <view class="floor-title">楼层</view>
      <view class="floor-buttons">
        <button 
          wx:for="{{[1,2,3,4]}}" 
          wx:key="*this"
          class="floor-button {{currentFloor === item ? 'active' : ''}}"
          bindtap="switchFloor"
          data-floor="{{item}}"
          size="mini">
          {{item}}F
        </button>
      </view>
    </view>
  </view>
  
  <!-- 地图信息面板 -->
  <view class="map-info" wx:if="{{mode === 'explore'}}">
    <view class="info-item">
      <text class="info-label">当前楼层:</text>
      <text class="info-value">{{currentFloor}}F</text>
    </view>
    <view class="info-item">
      <text class="info-label">缩放级别:</text>
      <text class="info-value">{{zoomLevel.toFixed(1)}}x</text>
    </view>
    <view class="info-item">
      <text class="info-label">视图中心:</text>
      <text class="info-value">{{viewCenter.x.toFixed(1)}}, {{viewCenter.y.toFixed(1)}}</text>
    </view>
  </view>
  
  <!-- 图例 -->
  <view class="map-legend" wx:if="{{mode === 'explore'}}">
    <view class="legend-title">图例</view>
    <view class="legend-items">
      <view class="legend-item">
        <view class="legend-symbol user-position"></view>
        <text class="legend-text">用户位置</text>
      </view>
      <view class="legend-item">
        <view class="legend-symbol navigation-route"></view>
        <text class="legend-text">导航路径</text>
      </view>
      <view class="legend-item">
        <view class="legend-symbol poi-entrance">🚪</view>
        <text class="legend-text">入口</text>
      </view>
      <view class="legend-item">
        <view class="legend-symbol poi-elevator">🛗</view>
        <text class="legend-text">电梯</text>
      </view>
      <view class="legend-item">
        <view class="legend-symbol poi-meeting">🏢</view>
        <text class="legend-text">会议室</text>
      </view>
      <view class="legend-item">
        <view class="legend-symbol poi-restroom">🚻</view>
        <text class="legend-text">洗手间</text>
      </view>
    </view>
  </view>
  
  <!-- 加载状态 -->
  <view wx:if="{{!canvasContext}}" class="map-loading">
    <view class="loading-spinner"></view>
    <text class="loading-text">地图加载中...</text>
  </view>
</view>