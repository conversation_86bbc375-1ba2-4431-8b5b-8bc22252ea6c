# 导航小程序测试指南

## 🎯 测试概述

本指南介绍如何在真实微信小程序环境中测试高精度定位功能。

## 📱 环境要求

### 硬件要求
- 支持陀螺仪、加速度计、磁力计的手机
- 建议使用iPhone或主流Android设备
- 充足的电量（定位功能耗电较多）

### 软件要求
- 微信最新版本
- 微信开发者工具（用于开发调试）
- 已开通小程序开发权限

## 🔧 部署步骤

### 1. 准备开发环境

```bash
# 1. 确保所有文件都在navigation-app目录
cd navigation-app

# 2. 检查关键文件
ls -la lib/              # 惯导库文件
ls -la pages/map/        # 地图页面
ls -la pages/navigation/ # 导航页面
```

### 2. 微信开发者工具配置

1. 打开微信开发者工具
2. 新建项目 -> 选择navigation-app目录
3. 填写AppID（测试可使用测试号）
4. 确保项目设置中启用了以下权限：
   - `scope.userLocation` - 位置权限
   - 传感器权限（陀螺仪、加速度计、磁力计）

### 3. 真机调试设置

1. 在开发者工具中点击"真机调试"
2. 使用微信扫码在手机上打开
3. 首次运行会请求权限，需要允许：
   - 位置权限
   - 传感器权限

## 🧪 测试流程

### 阶段1：基础功能测试

#### 1.1 库初始化测试
- 打开小程序，观察控制台日志
- 应该看到：`🧭 WX惯导定位库初始化完成`
- 检查App.js中的初始化是否成功

#### 1.2 权限获取测试
- 进入地图页面
- 检查是否正确请求位置权限
- 观察传感器是否能正常启动

#### 1.3 基础定位测试
- 点击地图页面底部的定位状态（红色🔴变绿色🟢）
- 观察是否显示"定位已启动"提示
- 查看底部状态栏位置坐标是否开始变化

### 阶段2：定位精度测试

#### 2.1 静态测试
```javascript
// 在真机上运行，保持手机静止
// 观察位置坐标的稳定性
// 期望：坐标变化应该很小（<0.1米）
```

#### 2.2 直线行走测试
```javascript
// 手持手机直线行走10米
// 观察轨迹是否接近直线
// 观察距离计算是否准确
```

#### 2.3 转向测试
```javascript
// 行走过程中改变方向
// 观察航向角是否正确更新
// 检查转向响应速度
```

### 阶段3：高精度模式测试

#### 3.1 模式切换测试
- 在设置页面切换到`precise`模式
- 重启定位，观察性能差异
- 对比标准模式和高精度模式的效果

#### 3.2 MLA校正测试（如果有磁场标定点）
```javascript
// 在有已知位置的磁场标定点附近测试
// 观察是否有校正提示
// 检查校正后的精度提升
```

## 📊 性能监控

### 实时监控指标

在地图页面可以监控：
- 位置坐标 (x, y, z)
- 定位状态（🟢定位中 / 🔴停止）
- 置信度（应该>70%）

### 详细统计查看

```javascript
// 在地图页面点击"更多" -> "导出轨迹"
// 可以获取详细的运行统计
// 包括：运行时间、步数、距离、置信度等
```

## 🐛 常见问题排查

### 问题1：定位无法启动
**症状**：点击定位按钮后显示"定位启动失败"

**排查步骤**：
1. 检查权限是否已授予
2. 查看控制台错误信息
3. 确认传感器是否可用

### 问题2：位置不更新
**症状**：定位已启动但坐标不变化

**排查步骤**：
1. 检查手机是否在移动
2. 确认传感器数据是否正常
3. 查看是否有数据质量警告

### 问题3：精度很差
**症状**：位置跳跃很大或轨迹不准确

**排查步骤**：
1. 检查手机传感器校准状态
2. 尝试重新校准磁力计
3. 更换测试环境（避免磁干扰）

### 问题4：耗电严重
**症状**：定位时手机发热明显

**解决方案**：
1. 切换到`lite`模式
2. 降低采样率
3. 定期暂停定位

## 🔍 调试技巧

### 1. 控制台日志监控
```javascript
// 在真机调试时观察关键日志：
// 🚀 启动惯导定位系统...
// ✅ 惯导定位系统启动成功  
// 📍 位置更新: {...}
// ⚠️ 如果有警告信息，注意排查
```

### 2. 数据导出分析
```javascript
// 测试完成后导出轨迹数据
// 可以在电脑上分析轨迹的准确性
// 对比GPS轨迹验证精度
```

### 3. 性能监控
```javascript
// 观察内存使用情况
// 监控CPU占用率
// 注意电池消耗情况
```

## ✅ 测试验收标准

### 基础功能
- [x] 小程序正常启动，无崩溃
- [x] 惯导库正确初始化
- [x] 权限正确获取
- [x] 定位可以启动和停止

### 定位精度
- [x] 静止时位置漂移 < 0.5米
- [x] 直线行走轨迹基本准确
- [x] 距离计算误差 < 10%
- [x] 航向角响应及时

### 性能表现
- [x] 定位延迟 < 2秒
- [x] 电池续航可接受
- [x] 内存占用合理
- [x] 无明显发热

## 🚀 下一步优化

基于测试结果，可以考虑以下优化：

1. **精度优化**
   - 传感器数据滤波优化
   - 步态检测算法调优
   - MLA校正策略改进

2. **性能优化**
   - 降低功耗
   - 优化算法效率
   - 减少内存占用

3. **用户体验优化**
   - 优化界面响应速度
   - 增加定位质量指示
   - 改进错误提示

## 📞 技术支持

如果在测试过程中遇到问题，可以：

1. 查看项目README.md
2. 检查lib/USAGE.md使用文档  
3. 分析lib/test/目录下的测试用例
4. 联系PDR团队技术支持

---

**注意**：真实环境测试时，建议在不同场景下进行（室内、室外、不同建筑），以全面评估定位效果。