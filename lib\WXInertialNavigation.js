/**
 * 微信小程序惯导定位库 - 核心API
 * WeChat MiniProgram Inertial Navigation Library
 * 
 * 提供简洁易用的PDR室内定位能力
 * 输入：初始化坐标
 * 输出：实时位置坐标
 * 
 * @version 1.0.0
 * <AUTHOR> Team
 */

import InertialSensorManager from './core/InertialSensorManager.js';
import PositionCalculator from './core/PositionCalculator.js';
import QualityController from './core/QualityController.js';

/**
 * 微信小程序惯导定位核心类
 */
export default class WXInertialNavigation {
  
  /**
   * 构造函数
   * @param {Object} config - 配置参数
   * @param {Object} config.initialPosition - 初始位置 {x, y, z}
   * @param {number} config.sampleRate - 传感器采样率 (Hz)，默认50
   * @param {string} config.mode - 运行模式：'standard'(标准) | 'lite'(轻量) | 'precise'(精确)
   * @param {boolean} config.enableMLA - 是否启用MLA校正，默认true
   * @param {Object} config.calibration - 校准参数
   */
  constructor(config = {}) {
    // 验证配置参数
    this.validateConfig(config);
    
    // 核心配置
    this.config = {
      // 初始位置（必需）
      initialPosition: config.initialPosition,
      
      // 传感器配置
      sampleRate: config.sampleRate || 50,
      
      // 运行模式
      mode: config.mode || 'standard',
      
      // 功能开关
      enableMLA: config.enableMLA !== false,
      enableStepDetection: config.enableStepDetection !== false,
      enableHeadingCorrection: config.enableHeadingCorrection !== false,
      
      // 校准参数
      calibration: {
        stepLength: config.calibration?.stepLength || 0.75,
        magneticDeclination: config.calibration?.magneticDeclination || 0,
        accelerometerBias: config.calibration?.accelerometerBias || [0, 0, 0],
        gyroscopeBias: config.calibration?.gyroscopeBias || [0, 0, 0],
        ...config.calibration
      },
      
      // 融合参数
      fusion: {
        pdrWeight: 0.7,
        mlaWeight: 0.3,
        adaptiveWeighting: true,
        confidenceThreshold: 0.4,
        smoothingFactor: 0.4,
        ...config.fusion
      },
      
      // 质量控制
      quality: {
        outlierThreshold: 3.0,
        minConfidence: 0.2,
        maxDeviation: 5.0,
        ...config.quality
      }
    };
    
    // 根据模式调整参数
    this.adjustConfigByMode();
    
    // 初始化核心组件
    this.sensorManager = new InertialSensorManager(this.config);
    this.positionCalculator = new PositionCalculator(this.config);
    this.qualityController = new QualityController(this.config);
    
    // 系统状态
    this.state = {
      isRunning: false,
      isPaused: false,
      isCalibrated: false,
      lastUpdate: 0,
      operatingMode: this.config.mode
    };
    
    // 当前定位结果
    this.currentLocation = {
      position: { ...this.config.initialPosition },
      heading: 0,
      velocity: 0,
      stepCount: 0,
      confidence: 0,
      timestamp: Date.now(),
      quality: 'unknown'
    };
    
    // 历史轨迹
    this.trajectory = [];
    this.maxTrajectoryLength = 1000;
    
    // 性能统计
    this.statistics = {
      totalSteps: 0,
      totalDistance: 0,
      averageConfidence: 0,
      processingTime: [],
      correctionCount: 0,
      startTime: null
    };
    
    // 回调函数
    this.callbacks = {
      onLocationUpdate: null,
      onStepDetected: null,
      onError: null,
      onCalibrationRequired: null
    };
    
    console.log('🧭 WX惯导定位库初始化完成', {
      mode: this.config.mode,
      initialPosition: this.config.initialPosition,
      enableMLA: this.config.enableMLA
    });
  }
  
  /**
   * 验证配置参数
   * @param {Object} config 
   */
  validateConfig(config) {
    // 检查配置对象是否存在
    if (!config || typeof config !== 'object') {
      throw new Error('配置参数必须是一个对象');
    }
    
    // 检查初始位置
    if (!config.initialPosition) {
      throw new Error('必须提供初始位置坐标 (initialPosition)');
    }
    
    const { x, y, z } = config.initialPosition;
    
    // 检查坐标是否为数字类型
    if (typeof x !== 'number' || typeof y !== 'number' || typeof z !== 'number') {
      throw new Error('初始位置坐标必须为数字类型');
    }
    
    // 检查坐标是否为有效数字（非NaN和有限数）
    if (isNaN(x) || isNaN(y) || isNaN(z) || 
        !isFinite(x) || !isFinite(y) || !isFinite(z)) {
      throw new Error('初始位置坐标必须是有效的数字');
    }
    
    // 检查采样率范围
    if (config.sampleRate && (config.sampleRate < 10 || config.sampleRate > 100)) {
      console.warn('⚠️ 建议采样率设置在10-100Hz之间');
    }
    
    // 检查运行模式是否有效
    if (config.mode && !['lite', 'standard', 'precise'].includes(config.mode)) {
      throw new Error('运行模式必须是 lite、standard 或 precise');
    }
  }
  
  /**
   * 根据运行模式调整配置参数
   */
  adjustConfigByMode() {
    const mode = this.config.mode;
    
    switch (mode) {
      case 'lite':
        // 轻量模式：低功耗，牺牲部分精度
        this.config.sampleRate = Math.min(this.config.sampleRate, 30);
        this.config.enableMLA = false;
        this.config.fusion.smoothingFactor = 0.6;
        this.config.quality.minConfidence = 0.1;
        break;
        
      case 'precise':
        // 精确模式：高精度，增加计算量
        this.config.sampleRate = Math.max(this.config.sampleRate, 50);
        this.config.enableMLA = true;
        this.config.fusion.smoothingFactor = 0.3;
        this.config.quality.minConfidence = 0.3;
        this.config.fusion.adaptiveWeighting = true;
        break;
        
      case 'standard':
      default:
        // 标准模式：平衡精度和性能
        // 使用默认配置
        break;
    }
  }
  
  /**
   * 启动惯导定位
   * @param {Object} options - 启动选项
   * @returns {Promise<boolean>} 启动是否成功
   */
  async start(options = {}) {
    if (this.state.isRunning) {
      console.warn('⚠️ 惯导定位已在运行中');
      return true;
    }
    
    try {
      console.log('🚀 启动惯导定位系统...');
      
      // 合并启动选项
      const startConfig = { ...this.config, ...options };
      
      // 启动传感器管理器
      await this.sensorManager.start(startConfig);
      
      // 初始化位置计算器
      this.positionCalculator.initialize(startConfig.initialPosition);
      
      // 初始化质量控制器
      this.qualityController.initialize();
      
      // 设置数据处理回调
      this.sensorManager.setDataCallback((sensorData) => {
        this.processSensorData(sensorData);
      });
      
      // 更新状态
      this.state.isRunning = true;
      this.state.isPaused = false;
      this.statistics.startTime = Date.now();
      
      console.log('✅ 惯导定位系统启动成功');
      return true;
      
    } catch (error) {
      console.error('❌ 惯导定位启动失败:', error);
      this.handleError('start_failed', error);
      return false;
    }
  }
  
  /**
   * 停止惯导定位
   */
  stop() {
    if (!this.state.isRunning) {
      return;
    }
    
    console.log('⏹️ 停止惯导定位系统');
    
    // 停止传感器采集
    this.sensorManager.stop();
    
    // 重置状态
    this.state.isRunning = false;
    this.state.isPaused = false;
    
    // 输出最终统计
    this.logFinalStatistics();
  }
  
  /**
   * 暂停/恢复定位
   * @param {boolean} pause - true暂停，false恢复
   */
  pause(pause = true) {
    if (!this.state.isRunning) {
      console.warn('⚠️ 系统未运行，无法暂停');
      return;
    }
    
    this.state.isPaused = pause;
    
    if (pause) {
      console.log('⏸️ 暂停惯导定位');
      this.sensorManager.pause();
    } else {
      console.log('▶️ 恢复惯导定位');
      this.sensorManager.resume();
    }
  }
  
  /**
   * 重置到指定位置
   * @param {Object} position - 新的位置坐标 {x, y, z}
   */
  reset(position = null) {
    console.log('🔄 重置惯导定位系统');
    
    // 重置位置
    const newPosition = position || this.config.initialPosition;
    this.currentLocation.position = { ...newPosition };
    this.currentLocation.heading = 0;
    this.currentLocation.velocity = 0;
    this.currentLocation.stepCount = 0;
    this.currentLocation.timestamp = Date.now();
    
    // 清空轨迹
    this.trajectory = [];
    
    // 重置统计
    this.statistics.totalSteps = 0;
    this.statistics.totalDistance = 0;
    this.statistics.correctionCount = 0;
    this.statistics.processingTime = [];
    
    // 重置计算器
    this.positionCalculator.reset(newPosition);
    
    console.log('✅ 重置完成，新位置:', newPosition);
  }
  
  /**
   * 处理传感器数据的核心方法
   * @param {Object} sensorData - 传感器数据
   */
  processSensorData(sensorData) {
    if (this.state.isPaused || !this.state.isRunning) {
      return;
    }
    
    const startTime = Date.now();
    
    try {
      // 数据质量检查
      const qualityResult = this.qualityController.checkDataQuality(sensorData);
      if (!qualityResult.isValid) {
        console.warn('⚠️ 传感器数据质量不佳:', qualityResult.reason);
        return;
      }
      
      // 位置计算
      const positionResult = this.positionCalculator.update(sensorData);
      
      // 更新当前位置
      this.updateCurrentLocation(positionResult);
      
      // 记录轨迹
      this.recordTrajectory();
      
      // 更新统计信息
      this.updateStatistics(positionResult, Date.now() - startTime);
      
      // 触发位置更新回调
      if (this.callbacks.onLocationUpdate) {
        this.callbacks.onLocationUpdate(this.getCurrentLocation());
      }
      
    } catch (error) {
      console.error('❌ 传感器数据处理失败:', error);
      this.handleError('processing_failed', error);
    }
  }
  
  /**
   * 更新当前位置信息
   * @param {Object} positionResult - 位置计算结果
   */
  updateCurrentLocation(positionResult) {
    this.currentLocation = {
      position: positionResult.position,
      heading: positionResult.heading,
      velocity: positionResult.velocity,
      stepCount: positionResult.stepCount,
      confidence: positionResult.confidence,
      timestamp: Date.now(),
      quality: this.qualityController.getQualityLevel()
    };
    
    this.state.lastUpdate = Date.now();
  }
  
  /**
   * 记录轨迹点
   */
  recordTrajectory() {
    const trajectoryPoint = {
      ...this.currentLocation.position,
      heading: this.currentLocation.heading,
      timestamp: this.currentLocation.timestamp,
      confidence: this.currentLocation.confidence
    };
    
    this.trajectory.push(trajectoryPoint);
    
    // 限制轨迹长度
    if (this.trajectory.length > this.maxTrajectoryLength) {
      this.trajectory.shift();
    }
  }
  
  /**
   * 更新性能统计
   * @param {Object} positionResult - 位置结果
   * @param {number} processingTime - 处理时间(ms)
   */
  updateStatistics(positionResult, processingTime) {
    this.statistics.processingTime.push(processingTime);
    
    // 保持处理时间数组大小
    if (this.statistics.processingTime.length > 100) {
      this.statistics.processingTime.shift();
    }
    
    // 更新步数
    if (positionResult.stepDetected) {
      this.statistics.totalSteps++;
      
      // 步数检测回调
      if (this.callbacks.onStepDetected) {
        this.callbacks.onStepDetected({
          stepCount: this.statistics.totalSteps,
          stepLength: positionResult.stepLength,
          timestamp: Date.now()
        });
      }
    }
    
    // 更新距离
    this.statistics.totalDistance = this.calculateTotalDistance();
    
    // 更新平均置信度
    this.statistics.averageConfidence = this.calculateAverageConfidence();
    
    // 更新校正次数
    if (positionResult.corrected) {
      this.statistics.correctionCount++;
    }
  }
  
  /**
   * 计算总距离
   * @returns {number} 总距离(米)
   */
  calculateTotalDistance() {
    if (this.trajectory.length < 2) return 0;
    
    let totalDistance = 0;
    for (let i = 1; i < this.trajectory.length; i++) {
      const prev = this.trajectory[i - 1];
      const curr = this.trajectory[i];
      const distance = Math.sqrt(
        Math.pow(curr.x - prev.x, 2) + 
        Math.pow(curr.y - prev.y, 2) + 
        Math.pow(curr.z - prev.z, 2)
      );
      totalDistance += distance;
    }
    return totalDistance;
  }
  
  /**
   * 计算平均置信度
   * @returns {number} 平均置信度
   */
  calculateAverageConfidence() {
    if (this.trajectory.length === 0) return 0;
    
    const recentTrajectory = this.trajectory.slice(-50); // 最近50个点
    const totalConfidence = recentTrajectory.reduce((sum, point) => sum + (point.confidence || 0), 0);
    return totalConfidence / recentTrajectory.length;
  }
  
  /**
   * 错误处理
   * @param {string} type - 错误类型
   * @param {Error} error - 错误对象
   */
  handleError(type, error) {
    const errorInfo = {
      type,
      message: error.message,
      timestamp: Date.now(),
      state: { ...this.state }
    };
    
    if (this.callbacks.onError) {
      this.callbacks.onError(errorInfo);
    }
    
    // 根据错误类型处理
    switch (type) {
      case 'sensor_error':
        // 传感器错误，尝试恢复
        this.recoverFromSensorError();
        break;
      case 'calibration_required':
        // 需要校准
        if (this.callbacks.onCalibrationRequired) {
          this.callbacks.onCalibrationRequired();
        }
        break;
      default:
        console.error('未处理的错误类型:', type, error);
    }
  }
  
  /**
   * 从传感器错误中恢复
   */
  recoverFromSensorError() {
    console.log('🔧 尝试从传感器错误中恢复...');
    
    // 重启传感器管理器
    setTimeout(() => {
      if (this.state.isRunning) {
        this.sensorManager.restart();
      }
    }, 1000);
  }
  
  /**
   * 输出最终统计信息
   */
  logFinalStatistics() {
    const runtime = this.statistics.startTime ? Date.now() - this.statistics.startTime : 0;
    const avgProcessingTime = this.statistics.processingTime.length > 0 
      ? this.statistics.processingTime.reduce((a, b) => a + b, 0) / this.statistics.processingTime.length 
      : 0;
    
    console.log('📊 惯导定位会话统计:', {
      运行时间: `${(runtime / 1000).toFixed(1)}秒`,
      总步数: this.statistics.totalSteps,
      总距离: `${this.statistics.totalDistance.toFixed(2)}米`,
      平均置信度: `${(this.statistics.averageConfidence * 100).toFixed(1)}%`,
      校正次数: this.statistics.correctionCount,
      平均处理时间: `${avgProcessingTime.toFixed(1)}ms`
    });
  }
  
  // ==================== 公共API方法 ====================
  
  /**
   * 获取当前位置
   * @returns {Object} 当前位置信息
   */
  getCurrentLocation() {
    return {
      ...this.currentLocation,
      // 添加额外的状态信息
      isMoving: this.currentLocation.velocity > 0.1,
      qualityLevel: this.qualityController.getQualityLevel(),
      systemStatus: this.getSystemStatus()
    };
  }
  
  /**
   * 获取历史轨迹
   * @param {number} limit - 返回的轨迹点数量限制
   * @returns {Array} 轨迹点数组
   */
  getTrajectory(limit = null) {
    if (limit && limit > 0) {
      return this.trajectory.slice(-limit);
    }
    return [...this.trajectory];
  }
  
  /**
   * 获取性能统计
   * @returns {Object} 性能统计信息
   */
  getStatistics() {
    const runtime = this.statistics.startTime ? Date.now() - this.statistics.startTime : 0;
    const avgProcessingTime = this.statistics.processingTime.length > 0 
      ? this.statistics.processingTime.reduce((a, b) => a + b, 0) / this.statistics.processingTime.length 
      : 0;
    
    return {
      runtime: runtime,
      totalSteps: this.statistics.totalSteps,
      totalDistance: this.statistics.totalDistance,
      averageConfidence: this.statistics.averageConfidence,
      correctionCount: this.statistics.correctionCount,
      averageProcessingTime: avgProcessingTime,
      trajectoryLength: this.trajectory.length,
      isRealtime: this.state.isRunning && !this.state.isPaused
    };
  }
  
  /**
   * 获取系统状态
   * @returns {Object} 系统状态信息
   */
  getSystemStatus() {
    return {
      isRunning: this.state.isRunning,
      isPaused: this.state.isPaused,
      isCalibrated: this.state.isCalibrated,
      operatingMode: this.state.operatingMode,
      lastUpdate: this.state.lastUpdate,
      sensorStatus: this.sensorManager.getStatus(),
      qualityLevel: this.qualityController.getQualityLevel()
    };
  }
  
  /**
   * 设置回调函数
   * @param {Object} callbacks - 回调函数对象
   * @param {Function} callbacks.onLocationUpdate - 位置更新回调
   * @param {Function} callbacks.onStepDetected - 步数检测回调
   * @param {Function} callbacks.onError - 错误回调
   * @param {Function} callbacks.onCalibrationRequired - 需要校准回调
   */
  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }
  
  /**
   * 更新配置参数
   * @param {Object} config - 新的配置参数
   */
  updateConfig(config) {
    // 合并配置
    this.config = { ...this.config, ...config };
    
    // 根据模式重新调整
    if (config.mode) {
      this.adjustConfigByMode();
    }
    
    // 更新子模块配置
    this.sensorManager.updateConfig(this.config);
    this.positionCalculator.updateConfig(this.config);
    this.qualityController.updateConfig(this.config);
    
    console.log('⚙️ 配置已更新:', config);
  }
  
  /**
   * 校准传感器
   * @param {Object} calibrationData - 校准数据
   * @returns {Promise<boolean>} 校准是否成功
   */
  async calibrate(calibrationData = {}) {
    console.log('🔧 开始传感器校准...');
    
    try {
      // 执行校准
      const result = await this.sensorManager.calibrate(calibrationData);
      
      if (result.success) {
        this.state.isCalibrated = true;
        this.config.calibration = { ...this.config.calibration, ...result.parameters };
        console.log('✅ 传感器校准成功');
        return true;
      } else {
        console.warn('⚠️ 传感器校准失败:', result.error);
        return false;
      }
      
    } catch (error) {
      console.error('❌ 传感器校准异常:', error);
      this.handleError('calibration_failed', error);
      return false;
    }
  }
  
  /**
   * 导出数据
   * @param {string} format - 导出格式: 'json' | 'csv'
   * @returns {string} 导出的数据
   */
  exportData(format = 'json') {
    const exportData = {
      metadata: {
        version: '1.0.0',
        exportTime: new Date().toISOString(),
        config: this.config,
        statistics: this.getStatistics()
      },
      trajectory: this.trajectory,
      currentLocation: this.currentLocation
    };
    
    switch (format.toLowerCase()) {
      case 'json':
        return JSON.stringify(exportData, null, 2);
      
      case 'csv':
        return this.convertToCSV(this.trajectory);
      
      default:
        throw new Error('不支持的导出格式: ' + format);
    }
  }
  
  /**
   * 将轨迹转换为CSV格式
   * @param {Array} trajectory - 轨迹数据
   * @returns {string} CSV格式字符串
   */
  convertToCSV(trajectory) {
    const headers = ['timestamp', 'x', 'y', 'z', 'heading', 'confidence'];
    const csvLines = [headers.join(',')];
    
    trajectory.forEach(point => {
      const row = [
        point.timestamp,
        point.x.toFixed(6),
        point.y.toFixed(6),
        point.z.toFixed(6),
        point.heading.toFixed(2),
        point.confidence.toFixed(3)
      ];
      csvLines.push(row.join(','));
    });
    
    return csvLines.join('\n');
  }
}

// 导出库版本信息
export const version = '1.0.0';
export const description = '微信小程序惯导定位库';