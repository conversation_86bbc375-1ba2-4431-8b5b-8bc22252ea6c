<!--位置指示器组件-->
<view class="position-indicator-container" wx:if="{{visible}}">
  
  <!-- 置信度圆圈 -->
  <view wx:if="{{showConfidenceCircle}}" 
        class="confidence-circle"
        style="width: {{getConfidenceRadius() * 2}}rpx; height: {{getConfidenceRadius() * 2}}rpx; border-color: {{getConfidenceColor()}};">
  </view>
  
  <!-- 轨迹线（如果启用） -->
  <view wx:if="{{showTrail && lastPosition}}" class="position-trail"></view>
  
  <!-- 主指示器 -->
  <view class="{{getIndicatorClass()}}"
        bindtap="onIndicatorTap"
        bindlongpress="onIndicatorLongPress"
        style="transform: rotate({{heading}}deg);">
    
    <!-- 指示器核心 -->
    <view class="indicator-core">
      <!-- 方向箭头 -->
      <view class="direction-arrow">
        <text class="arrow-symbol">▲</text>
      </view>
      
      <!-- 中心点 -->
      <view class="center-dot"></view>
    </view>
    
    <!-- 置信度指示环 -->
    <view class="confidence-ring" 
          style="border-color: {{getConfidenceColor()}}; opacity: {{confidence}};">
    </view>
  </view>
  
  <!-- 位置信息提示 -->
  <view class="position-info" wx:if="{{size === 'large'}}">
    <text class="info-text">{{position.x.toFixed(1)}}, {{position.y.toFixed(1)}}</text>
    <text class="confidence-text">{{(confidence * 100).toFixed(0)}}%</text>
  </view>
  
  <!-- 脉冲动画圆圈 -->
  <view class="pulse-circle" wx:if="{{isAnimating}}"></view>
</view>