/**
 * 运动分析器
 * 集成步态检测和航向估计功能
 */

class MotionAnalyzer {
  constructor(config = {}) {
    this.config = {
      // 步态检测配置
      stepDetection: {
        enabled: config.enableStepDetection !== false,
        accPeakMagnitudeLB: 1.2,
        accPeakPeriodLB: 300,
        slideWindowLen: 21,
        sampleFreq: 50,
        adaptiveThreshold: true,
        ...config.stepDetection
      },
      
      // 航向估计配置
      headingEstimation: {
        enabled: config.enableHeadingCorrection !== false,
        useCompass: true,
        useGyroscope: true,
        compassWeight: 0.8,
        gyroWeight: 0.2,
        headingSmoothing: 0.3,
        fastResponseMode: true,
        ...config.headingEstimation
      },
      
      // 步长估计配置
      stepLength: {
        defaultLength: config.calibration?.stepLength || 0.75,
        adaptiveEnabled: true,
        kValue: 0.55606, // Weinberg模型系数
        personalFactor: 1.0,
        ...config.stepLength
      }
    };
    
    // 步态检测器
    this.stepDetector = new StepDetector(this.config.stepDetection);
    
    // 航向估计器
    this.headingEstimator = new HeadingEstimator(this.config.headingEstimation);
    
    // 当前状态
    this.currentState = {
      // 步态相关
      stepCount: 0,
      stepLength: this.config.stepLength.defaultLength,
      stepFrequency: 0,
      walkingState: 'stationary', // stationary, walking, running
      
      // 航向相关
      heading: 0,
      headingConfidence: 0,
      headingQuality: 'unknown',
      
      // 速度相关
      velocity: 0,
      acceleration: 0,
      
      // 时间相关
      lastStepTime: 0,
      lastUpdateTime: 0
    };
    
    // 历史数据
    this.history = {
      steps: [],
      headings: [],
      velocities: []
    };
    
    // 性能统计
    this.statistics = {
      totalSteps: 0,
      averageStepLength: this.config.stepLength.defaultLength,
      averageStepFrequency: 0,
      headingAccuracy: 0,
      processingTime: []
    };
    
    // 回调函数
    this.callbacks = {
      onStepDetected: null,
      onHeadingUpdate: null,
      onStateChange: null
    };
    
    console.log('🚶 运动分析器初始化完成');
  }
  
  /**
   * 处理传感器数据
   * @param {Object} sensorData - 传感器数据
   * @returns {Object} 运动分析结果
   */
  analyze(sensorData) {
    const startTime = Date.now();
    
    try {
      const result = {
        stepDetected: false,
        stepData: null,
        headingUpdated: false,
        headingData: null,
        stateChanged: false,
        timestamp: startTime
      };
      
      // 步态检测
      if (this.config.stepDetection.enabled && sensorData.accelerometer) {
        const stepResult = this.stepDetector.process(sensorData.accelerometer, sensorData.timestamp);
        if (stepResult) {
          result.stepDetected = true;
          result.stepData = this.processStepDetection(stepResult);
        }
      }
      
      // 航向估计
      if (this.config.headingEstimation.enabled) {
        const headingResult = this.headingEstimator.update(
          sensorData.gyroscope,
          sensorData.magnetometer,
          sensorData.timestamp
        );
        if (headingResult) {
          result.headingUpdated = true;
          result.headingData = this.processHeadingUpdate(headingResult);
        }
      }
      
      // 更新运动状态
      const stateChange = this.updateMotionState(sensorData);
      if (stateChange) {
        result.stateChanged = true;
      }
      
      // 更新时间戳
      this.currentState.lastUpdateTime = startTime;
      
      // 记录处理时间
      const processingTime = Date.now() - startTime;
      this.statistics.processingTime.push(processingTime);
      if (this.statistics.processingTime.length > 100) {
        this.statistics.processingTime.shift();
      }
      
      return result;
      
    } catch (error) {
      console.error('❌ 运动分析失败:', error);
      return null;
    }
  }
  
  /**
   * 处理步态检测结果
   */
  processStepDetection(stepResult) {
    // 更新步数
    this.currentState.stepCount++;
    this.statistics.totalSteps++;
    
    // 计算步长
    const stepLength = this.calculateStepLength(stepResult);
    this.currentState.stepLength = stepLength;
    
    // 计算步频
    const stepFrequency = this.calculateStepFrequency(stepResult);
    this.currentState.stepFrequency = stepFrequency;
    
    // 更新步态时间
    this.currentState.lastStepTime = stepResult.timestamp;
    
    // 记录步态历史
    const stepRecord = {
      timestamp: stepResult.timestamp,
      stepCount: this.currentState.stepCount,
      stepLength: stepLength,
      magnitude: stepResult.magnitude,
      confidence: stepResult.confidence || 1.0
    };
    
    this.history.steps.push(stepRecord);
    if (this.history.steps.length > 100) {
      this.history.steps.shift();
    }
    
    // 更新统计信息
    this.updateStepStatistics(stepRecord);
    
    // 触发回调
    if (this.callbacks.onStepDetected) {
      this.callbacks.onStepDetected(stepRecord);
    }
    
    return stepRecord;
  }
  
  /**
   * 处理航向更新
   */
  processHeadingUpdate(headingResult) {
    // 更新航向角
    this.currentState.heading = headingResult.heading;
    this.currentState.headingConfidence = headingResult.confidence || 0;
    this.currentState.headingQuality = headingResult.quality || 'unknown';
    
    // 记录航向历史
    const headingRecord = {
      timestamp: headingResult.timestamp,
      heading: headingResult.heading,
      confidence: headingResult.confidence,
      quality: headingResult.quality,
      source: headingResult.source // 'gyro', 'compass', 'fused'
    };
    
    this.history.headings.push(headingRecord);
    if (this.history.headings.length > 200) {
      this.history.headings.shift();
    }
    
    // 更新统计信息
    this.updateHeadingStatistics(headingRecord);
    
    // 触发回调
    if (this.callbacks.onHeadingUpdate) {
      this.callbacks.onHeadingUpdate(headingRecord);
    }
    
    return headingRecord;
  }
  
  /**
   * 更新运动状态
   */
  updateMotionState(sensorData) {
    const currentTime = Date.now();
    const previousState = this.currentState.walkingState;
    
    // 基于步频判断运动状态
    const timeSinceLastStep = currentTime - this.currentState.lastStepTime;
    const recentSteps = this.history.steps.filter(step => 
      currentTime - step.timestamp < 3000 // 最近3秒
    );
    
    let newState = 'stationary';
    
    if (recentSteps.length >= 2) {
      const avgStepInterval = this.calculateAverageStepInterval(recentSteps);
      
      if (avgStepInterval < 400) { // 步频 > 2.5步/秒
        newState = 'running';
      } else if (avgStepInterval < 800) { // 步频 > 1.25步/秒
        newState = 'walking';
      } else if (timeSinceLastStep < 2000) { // 2秒内有步态
        newState = 'walking';
      }
    }
    
    // 使用加速度幅值辅助判断
    if (sensorData.accelerometer) {
      const accMagnitude = Math.sqrt(
        sensorData.accelerometer.x ** 2 + 
        sensorData.accelerometer.y ** 2 + 
        sensorData.accelerometer.z ** 2
      );
      
      // 如果加速度变化很小，更倾向于静止状态
      if (accMagnitude < 1.2 && newState !== 'stationary') {
        newState = 'walking'; // 轻微运动
      }
    }
    
    this.currentState.walkingState = newState;
    
    // 计算速度
    this.updateVelocity();
    
    // 检查状态是否发生变化
    const stateChanged = previousState !== newState;
    if (stateChanged && this.callbacks.onStateChange) {
      this.callbacks.onStateChange({
        previousState,
        currentState: newState,
        timestamp: currentTime,
        stepFrequency: this.currentState.stepFrequency,
        velocity: this.currentState.velocity
      });
    }
    
    return stateChanged;
  }
  
  /**
   * 计算步长 (Weinberg模型)
   */
  calculateStepLength(stepResult) {
    const magnitude = stepResult.magnitude || 1.0;
    const k = this.config.stepLength.kValue;
    const personalFactor = this.config.stepLength.personalFactor;
    
    // Weinberg模型: SL = K × (amax - amin)^0.25
    let estimatedLength = k * Math.pow(magnitude, 0.25) * personalFactor;
    
    // 应用自适应调整
    if (this.config.stepLength.adaptiveEnabled && this.history.steps.length > 5) {
      const recentLengths = this.history.steps.slice(-5).map(s => s.stepLength);
      const avgLength = recentLengths.reduce((a, b) => a + b, 0) / recentLengths.length;
      
      // 平滑处理
      estimatedLength = 0.7 * estimatedLength + 0.3 * avgLength;
    }
    
    // 限制步长范围
    estimatedLength = Math.max(0.3, Math.min(1.5, estimatedLength));
    
    return estimatedLength;
  }
  
  /**
   * 计算步频
   */
  calculateStepFrequency(stepResult) {
    if (this.history.steps.length < 2) {
      return 0;
    }
    
    const recentSteps = this.history.steps.slice(-5); // 最近5步
    if (recentSteps.length < 2) {
      return 0;
    }
    
    // 计算平均步态间隔
    const intervals = [];
    for (let i = 1; i < recentSteps.length; i++) {
      intervals.push(recentSteps[i].timestamp - recentSteps[i-1].timestamp);
    }
    
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    return avgInterval > 0 ? 1000 / avgInterval : 0; // 步/秒
  }
  
  /**
   * 计算平均步态间隔
   */
  calculateAverageStepInterval(steps) {
    if (steps.length < 2) return Infinity;
    
    const intervals = [];
    for (let i = 1; i < steps.length; i++) {
      intervals.push(steps[i].timestamp - steps[i-1].timestamp);
    }
    
    return intervals.reduce((a, b) => a + b, 0) / intervals.length;
  }
  
  /**
   * 更新速度
   */
  updateVelocity() {
    const state = this.currentState.walkingState;
    
    if (state === 'stationary') {
      this.currentState.velocity = 0;
      this.currentState.acceleration = 0;
    } else {
      // 基于步频和步长计算速度
      const velocity = this.currentState.stepFrequency * this.currentState.stepLength;
      
      // 平滑处理
      const smoothing = 0.3;
      this.currentState.velocity = smoothing * velocity + (1 - smoothing) * this.currentState.velocity;
      
      // 计算加速度（简单差分）
      const previousVelocity = this.history.velocities.length > 0 ? 
        this.history.velocities[this.history.velocities.length - 1].velocity : 0;
      this.currentState.acceleration = this.currentState.velocity - previousVelocity;
    }
    
    // 记录速度历史
    this.history.velocities.push({
      timestamp: Date.now(),
      velocity: this.currentState.velocity,
      acceleration: this.currentState.acceleration,
      state: state
    });
    
    if (this.history.velocities.length > 50) {
      this.history.velocities.shift();
    }
  }
  
  /**
   * 更新步态统计
   */
  updateStepStatistics(stepRecord) {
    // 更新平均步长
    const recentSteps = this.history.steps.slice(-20); // 最近20步
    if (recentSteps.length > 0) {
      this.statistics.averageStepLength = recentSteps.reduce((sum, step) => 
        sum + step.stepLength, 0) / recentSteps.length;
    }
    
    // 更新平均步频
    if (this.currentState.stepFrequency > 0) {
      const alpha = 0.1;
      this.statistics.averageStepFrequency = alpha * this.currentState.stepFrequency + 
        (1 - alpha) * this.statistics.averageStepFrequency;
    }
  }
  
  /**
   * 更新航向统计
   */
  updateHeadingStatistics(headingRecord) {
    // 计算航向精度（基于置信度）
    const recentHeadings = this.history.headings.slice(-10);
    if (recentHeadings.length > 0) {
      this.statistics.headingAccuracy = recentHeadings.reduce((sum, h) => 
        sum + (h.confidence || 0), 0) / recentHeadings.length;
    }
  }
  
  /**
   * 获取当前运动状态
   */
  getCurrentState() {
    return {
      ...this.currentState,
      statistics: { ...this.statistics },
      isMoving: this.currentState.walkingState !== 'stationary',
      movementType: this.currentState.walkingState,
      confidence: Math.min(
        this.currentState.headingConfidence,
        this.currentState.stepFrequency > 0 ? 1.0 : 0.5
      )
    };
  }
  
  /**
   * 获取运动历史
   */
  getMotionHistory(type = 'all', limit = null) {
    let history = {};
    
    if (type === 'all' || type === 'steps') {
      history.steps = limit ? this.history.steps.slice(-limit) : [...this.history.steps];
    }
    
    if (type === 'all' || type === 'headings') {
      history.headings = limit ? this.history.headings.slice(-limit) : [...this.history.headings];
    }
    
    if (type === 'all' || type === 'velocities') {
      history.velocities = limit ? this.history.velocities.slice(-limit) : [...this.history.velocities];
    }
    
    return history;
  }
  
  /**
   * 校准运动参数
   */
  calibrate(calibrationData) {
    console.log('🔧 校准运动分析器...');
    
    try {
      // 步长校准
      if (calibrationData.stepLength) {
        this.config.stepLength.defaultLength = calibrationData.stepLength;
        this.currentState.stepLength = calibrationData.stepLength;
      }
      
      // 个人化因子校准
      if (calibrationData.personalFactor) {
        this.config.stepLength.personalFactor = calibrationData.personalFactor;
      }
      
      // 航向校准
      if (calibrationData.magneticDeclination !== undefined) {
        this.headingEstimator.setMagneticDeclination(calibrationData.magneticDeclination);
      }
      
      // 传感器校准
      if (calibrationData.sensorCalibration) {
        this.stepDetector.calibrate(calibrationData.sensorCalibration);
        this.headingEstimator.calibrate(calibrationData.sensorCalibration);
      }
      
      console.log('✅ 运动分析器校准完成');
      return { success: true };
      
    } catch (error) {
      console.error('❌ 运动分析器校准失败:', error);
      return { success: false, error: error.message };
    }
  }
  
  /**
   * 重置运动状态
   */
  reset() {
    // 重置状态
    this.currentState = {
      stepCount: 0,
      stepLength: this.config.stepLength.defaultLength,
      stepFrequency: 0,
      walkingState: 'stationary',
      heading: 0,
      headingConfidence: 0,
      headingQuality: 'unknown',
      velocity: 0,
      acceleration: 0,
      lastStepTime: 0,
      lastUpdateTime: 0
    };
    
    // 清空历史
    this.history.steps = [];
    this.history.headings = [];
    this.history.velocities = [];
    
    // 重置统计
    this.statistics.totalSteps = 0;
    this.statistics.averageStepLength = this.config.stepLength.defaultLength;
    this.statistics.averageStepFrequency = 0;
    this.statistics.headingAccuracy = 0;
    this.statistics.processingTime = [];
    
    // 重置子模块
    this.stepDetector.reset();
    this.headingEstimator.reset();
    
    console.log('🔄 运动分析器已重置');
  }
  
  /**
   * 设置回调函数
   */
  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }
  
  /**
   * 更新配置
   */
  updateConfig(config) {
    this.config = { ...this.config, ...config };
    
    // 更新子模块配置
    if (config.stepDetection) {
      this.stepDetector.updateConfig(config.stepDetection);
    }
    
    if (config.headingEstimation) {
      this.headingEstimator.updateConfig(config.headingEstimation);
    }
    
    console.log('⚙️ 运动分析器配置已更新');
  }
  
  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    const avgProcessingTime = this.statistics.processingTime.length > 0 ?
      this.statistics.processingTime.reduce((a, b) => a + b, 0) / this.statistics.processingTime.length : 0;
    
    return {
      averageProcessingTime: avgProcessingTime,
      totalSteps: this.statistics.totalSteps,
      averageStepLength: this.statistics.averageStepLength,
      averageStepFrequency: this.statistics.averageStepFrequency,
      headingAccuracy: this.statistics.headingAccuracy,
      currentState: this.currentState.walkingState,
      dataPoints: {
        steps: this.history.steps.length,
        headings: this.history.headings.length,
        velocities: this.history.velocities.length
      }
    };
  }
}

/**
 * 简化的步态检测器
 */
class StepDetector {
  constructor(config) {
    this.config = config;
    this.slideWindow = [];
    this.stepState = {
      stepCount: 0,
      lastPeakTime: 0,
      threshold: config.accPeakMagnitudeLB
    };
  }
  
  process(accData, timestamp) {
    // 计算加速度幅值
    const magnitude = Math.sqrt(accData.x ** 2 + accData.y ** 2 + accData.z ** 2);
    
    // 添加到滑动窗口
    this.slideWindow.push({ magnitude, timestamp });
    
    // 维持窗口大小
    if (this.slideWindow.length > this.config.slideWindowLen) {
      this.slideWindow.shift();
    }
    
    // 检查是否有足够数据
    if (this.slideWindow.length < this.config.slideWindowLen) {
      return null;
    }
    
    // 简单峰值检测
    const current = this.slideWindow[this.slideWindow.length - 1];
    const previous = this.slideWindow[this.slideWindow.length - 2];
    
    // 检查峰值条件
    if (current.magnitude > this.stepState.threshold && 
        current.magnitude > previous.magnitude &&
        timestamp - this.stepState.lastPeakTime > this.config.accPeakPeriodLB) {
      
      this.stepState.stepCount++;
      this.stepState.lastPeakTime = timestamp;
      
      return {
        timestamp,
        magnitude: current.magnitude,
        stepCount: this.stepState.stepCount,
        confidence: Math.min(1.0, current.magnitude / 2.0)
      };
    }
    
    return null;
  }
  
  calibrate(calibrationData) {
    if (calibrationData.stepThreshold) {
      this.stepState.threshold = calibrationData.stepThreshold;
    }
  }
  
  reset() {
    this.slideWindow = [];
    this.stepState.stepCount = 0;
    this.stepState.lastPeakTime = 0;
  }
  
  updateConfig(config) {
    this.config = { ...this.config, ...config };
  }
}

/**
 * 简化的航向估计器
 */
class HeadingEstimator {
  constructor(config) {
    this.config = config;
    this.state = {
      heading: 0,
      lastUpdate: 0,
      initialized: false
    };
    this.magneticDeclination = 0;
  }
  
  update(gyroData, compassData, timestamp) {
    const dt = this.state.lastUpdate > 0 ? (timestamp - this.state.lastUpdate) / 1000 : 0;
    
    let newHeading = this.state.heading;
    let confidence = 0;
    let quality = 'unknown';
    let source = 'none';
    
    // 使用罗盘数据
    if (this.config.useCompass && compassData && compassData.direction !== undefined) {
      const compassHeading = (compassData.direction + this.magneticDeclination + 360) % 360;
      
      if (!this.state.initialized) {
        newHeading = compassHeading;
        this.state.initialized = true;
      } else {
        // 融合罗盘和当前航向
        const alpha = this.config.compassWeight;
        newHeading = this.blendAngles(this.state.heading, compassHeading, alpha);
      }
      
      confidence = Math.max(0.5, Math.min(1.0, (compassData.accuracy || 3) / 3));
      quality = confidence > 0.8 ? 'good' : confidence > 0.5 ? 'fair' : 'poor';
      source = 'compass';
    }
    
    // 使用陀螺仪数据进行预测
    if (this.config.useGyroscope && gyroData && dt > 0 && dt < 0.1) {
      const gyroHeading = gyroData.z * dt * 180 / Math.PI; // 转换为度
      newHeading = (newHeading + gyroHeading + 360) % 360;
      
      if (source === 'none') {
        source = 'gyro';
        confidence = 0.7;
        quality = 'fair';
      } else {
        source = 'fused';
      }
    }
    
    // 应用平滑
    if (this.state.initialized && this.config.headingSmoothing > 0) {
      const smoothing = this.config.headingSmoothing;
      newHeading = this.blendAngles(this.state.heading, newHeading, 1 - smoothing);
    }
    
    this.state.heading = newHeading;
    this.state.lastUpdate = timestamp;
    
    return {
      heading: newHeading,
      confidence,
      quality,
      source,
      timestamp
    };
  }
  
  blendAngles(angle1, angle2, weight) {
    const diff = ((angle2 - angle1 + 540) % 360) - 180;
    return (angle1 + diff * weight + 360) % 360;
  }
  
  setMagneticDeclination(declination) {
    this.magneticDeclination = declination;
  }
  
  calibrate(calibrationData) {
    if (calibrationData.magneticDeclination !== undefined) {
      this.setMagneticDeclination(calibrationData.magneticDeclination);
    }
  }
  
  reset() {
    this.state.heading = 0;
    this.state.lastUpdate = 0;
    this.state.initialized = false;
  }
  
  updateConfig(config) {
    this.config = { ...this.config, ...config };
  }
}

// CommonJS导出
module.exports = MotionAnalyzer;