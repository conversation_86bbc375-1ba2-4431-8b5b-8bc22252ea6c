<!--历史页面-->
<view class="history-page">
  
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">历史记录</text>
    <text class="page-subtitle">导航历史和统计信息</text>
  </view>

  <!-- 标签页切换 -->
  <view class="tab-bar">
    <view class="tab-item {{currentTab === 'history' ? 'active' : ''}}" 
          bindtap="switchTab" 
          data-tab="history">
      <text class="tab-icon">📋</text>
      <text class="tab-text">历史记录</text>
    </view>
    <view class="tab-item {{currentTab === 'statistics' ? 'active' : ''}}" 
          bindtap="switchTab" 
          data-tab="statistics">
      <text class="tab-icon">📊</text>
      <text class="tab-text">统计信息</text>
    </view>
  </view>

  <!-- 历史记录标签页 -->
  <view wx:if="{{currentTab === 'history'}}" class="tab-content">
    
    <!-- 操作栏 -->
    <view class="action-bar" wx:if="{{historyList.length > 0}}">
      <button class="action-button" bindtap="exportHistoryData" size="mini">
        <text class="button-icon">📤</text>
        <text>导出</text>
      </button>
      <button class="action-button danger" bindtap="clearAllHistory" size="mini">
        <text class="button-icon">🗑️</text>
        <text>清空</text>
      </button>
    </view>

    <!-- 历史记录列表 -->
    <scroll-view class="history-list" scroll-y="{{true}}" wx:if="{{historyList.length > 0}}">
      <view wx:for="{{historyList}}" wx:key="id" class="history-item">
        
        <!-- 记录头部 -->
        <view class="history-header" bindtap="viewHistoryDetail" data-index="{{index}}">
          <view class="history-icon">
            <text>{{getNavigationIcon(item.type)}}</text>
          </view>
          <view class="history-info">
            <text class="history-title">{{item.title || '导航记录'}}</text>
            <text class="history-time">{{formatDate(item.endTime)}}</text>
          </view>
          <view class="history-summary">
            <text class="summary-distance">{{formatDistance(item.distance)}}</text>
            <text class="summary-duration">{{formatDuration(item.duration)}}</text>
          </view>
        </view>

        <!-- 记录详情 -->
        <view class="history-details">
          <view class="detail-item">
            <text class="detail-label">步数:</text>
            <text class="detail-value">{{item.steps || 0}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">精度:</text>
            <text class="detail-value">{{item.formattedAccuracy || '0.0%'}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">模式:</text>
            <text class="detail-value">{{item.mode || 'standard'}}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="history-actions">
          <button class="history-action-btn" bindtap="viewTrajectory" data-index="{{index}}" size="mini">
            <text class="btn-icon">🛤️</text>
            <text>轨迹</text>
          </button>
          <button class="history-action-btn" bindtap="shareHistory" data-index="{{index}}" size="mini">
            <text class="btn-icon">📤</text>
            <text>分享</text>
          </button>
          <button class="history-action-btn" bindtap="reNavigate" data-index="{{index}}" size="mini">
            <text class="btn-icon">🧭</text>
            <text>重导</text>
          </button>
          <button class="history-action-btn danger" bindtap="deleteHistory" data-index="{{index}}" size="mini">
            <text class="btn-icon">🗑️</text>
            <text>删除</text>
          </button>
        </view>
      </view>
    </scroll-view>

    <!-- 空状态 -->
    <view wx:if="{{historyList.length === 0}}" class="empty-state">
      <text class="empty-icon">📋</text>
      <text class="empty-title">暂无导航记录</text>
      <text class="empty-desc">开始导航后，记录将显示在这里</text>
      <button class="empty-action" bindtap="goToNavigation">
        <text>开始导航</text>
      </button>
    </view>
  </view>

  <!-- 统计信息标签页 -->
  <view wx:if="{{currentTab === 'statistics'}}" class="tab-content">
    <scroll-view class="statistics-content" scroll-y="{{true}}">
      
      <!-- 总体统计 -->
      <view class="stats-section">
        <view class="section-title">
          <text class="title-icon">📊</text>
          <text class="title-text">总体统计</text>
        </view>
        
        <view class="stats-grid">
          <view class="stats-card">
            <text class="stats-number">{{totalNavigations}}</text>
            <text class="stats-label">总导航次数</text>
            <text class="stats-icon">🧭</text>
          </view>
          <view class="stats-card">
            <text class="stats-number">{{formatDistance(totalDistance)}}</text>
            <text class="stats-label">总行走距离</text>
            <text class="stats-icon">📏</text>
          </view>
          <view class="stats-card">
            <text class="stats-number">{{totalSteps}}</text>
            <text class="stats-label">总步数</text>
            <text class="stats-icon">👣</text>
          </view>
          <view class="stats-card">
            <text class="stats-number">{{formatDuration(totalTime)}}</text>
            <text class="stats-label">总用时</text>
            <text class="stats-icon">⏱️</text>
          </view>
        </view>
      </view>

      <!-- 精度统计 -->
      <view class="stats-section">
        <view class="section-title">
          <text class="title-icon">🎯</text>
          <text class="title-text">精度统计</text>
        </view>
        
        <view class="accuracy-stats">
          <view class="accuracy-item">
            <text class="accuracy-label">平均精度</text>
            <view class="accuracy-bar">
              <view class="accuracy-fill" style="width: {{formattedAverageAccuracy}}%"></view>
              <text class="accuracy-text">{{formattedAverageAccuracy}}%</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 使用模式统计 -->
      <view class="stats-section" wx:if="{{historyList.length > 0}}">
        <view class="section-title">
          <text class="title-icon">⚙️</text>
          <text class="title-text">使用习惯</text>
        </view>
        
        <view class="usage-stats">
          <view class="usage-item">
            <text class="usage-label">最常用模式</text>
            <text class="usage-value">标准模式</text>
          </view>
          <view class="usage-item">
            <text class="usage-label">平均步长</text>
            <text class="usage-value">0.75米</text>
          </view>
          <view class="usage-item">
            <text class="usage-label">最长导航</text>
            <text class="usage-value">{{formatDistance(maxDistance)}}</text>
          </view>
        </view>
      </view>

      <!-- 底部间距 -->
      <view class="bottom-spacing"></view>
    </scroll-view>
  </view>

  <!-- 历史详情弹窗 -->
  <view wx:if="{{showHistoryDetail && selectedHistory}}" class="detail-overlay" bindtap="closeHistoryDetail">
    <view class="detail-popup" catchtap="">
      <view class="popup-header">
        <text class="popup-title">导航详情</text>
        <button class="popup-close" bindtap="closeHistoryDetail" size="mini">×</button>
      </view>
      
      <view class="detail-content">
        <view class="detail-section">
          <text class="section-label">基本信息</text>
          <view class="detail-row">
            <text class="row-label">开始时间:</text>
            <text class="row-value">{{formatDate(selectedHistory.startTime)}}</text>
          </view>
          <view class="detail-row">
            <text class="row-label">结束时间:</text>
            <text class="row-value">{{formatDate(selectedHistory.endTime)}}</text>
          </view>
          <view class="detail-row">
            <text class="row-label">导航模式:</text>
            <text class="row-value">{{selectedHistory.mode}}</text>
          </view>
        </view>
        
        <view class="detail-section">
          <text class="section-label">统计数据</text>
          <view class="detail-row">
            <text class="row-label">行走距离:</text>
            <text class="row-value">{{formatDistance(selectedHistory.distance)}}</text>
          </view>
          <view class="detail-row">
            <text class="row-label">总步数:</text>
            <text class="row-value">{{selectedHistory.steps}}</text>
          </view>
          <view class="detail-row">
            <text class="row-label">平均精度:</text>
            <text class="row-value">{{selectedHistory.formattedAccuracy || '0.0%'}}</text>
          </view>
          <view class="detail-row">
            <text class="row-label">用时:</text>
            <text class="row-value">{{formatDuration(selectedHistory.duration)}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 轨迹查看弹窗 -->
  <view wx:if="{{showTrajectoryView}}" class="trajectory-overlay" bindtap="closeTrajectoryView">
    <view class="trajectory-popup" catchtap="">
      <view class="popup-header">
        <text class="popup-title">运动轨迹</text>
        <button class="popup-close" bindtap="closeTrajectoryView" size="mini">×</button>
      </view>
      
      <view class="trajectory-content">
        <view class="trajectory-info">
          <text class="info-text">轨迹点数: {{trajectoryData.length}}</text>
        </view>
        <!-- 这里可以添加轨迹可视化组件 -->
        <view class="trajectory-placeholder">
          <text class="placeholder-text">轨迹可视化</text>
          <text class="placeholder-desc">显示用户运动轨迹路径</text>
        </view>
      </view>
    </view>
  </view>
</view>