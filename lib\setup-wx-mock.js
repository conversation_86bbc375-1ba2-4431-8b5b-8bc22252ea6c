#!/usr/bin/env node

/**
 * 设置微信小程序模拟环境
 */

// 模拟微信小程序环境
const mockWxEnv = {
    // 模拟传感器API
    onAccelerometerChange: (callback) => {
        console.log('📱 监听加速计变化');
        // 模拟传感器数据
        setInterval(() => {
            callback({
                x: Math.random() * 2 - 1,
                y: Math.random() * 2 - 1,
                z: Math.random() * 2 - 1
            });
        }, 100);
    },
    
    startAccelerometer: (options) => {
        console.log('🚀 启动加速计');
        if (options && options.success) options.success();
    },
    
    stopAccelerometer: () => {
        console.log('⏹️ 停止加速计');
    },
    
    onGyroscopeChange: (callback) => {
        console.log('📱 监听陀螺仪变化');
    },
    
    startGyroscope: (options) => {
        console.log('🚀 启动陀螺仪');
        if (options && options.success) options.success();
    },
    
    stopGyroscope: () => {
        console.log('⏹️ 停止陀螺仪');
    },
    
    onCompassChange: (callback) => {
        console.log('📱 监听罗盘变化');
    },
    
    startCompass: (options) => {
        console.log('🚀 启动罗盘');
        if (options && options.success) options.success();
    },
    
    stopCompass: () => {
        console.log('⏹️ 停止罗盘');
    },
    
    getSystemInfo: (options) => {
        if (options && options.success) {
            options.success({
                system: 'iOS 15.0',
                platform: 'ios',
                model: 'iPhone 13',
                benchmarkLevel: 50
            });
        }
    },
    
    getSetting: (options) => {
        if (options && options.success) {
            options.success({
                authSetting: {
                    'scope.userLocation': true
                }
            });
        }
    },
    
    getStorageSync: (key) => {
        return null;
    },
    
    setStorageSync: (key, data) => {
        console.log(`💾 存储数据: ${key}`);
    },
    
    showToast: (options) => {
        console.log(`🔔 提示: ${options.title}`);
    },
    
    showModal: (options) => {
        console.log(`📢 弹窗: ${options.title} - ${options.content}`);
        if (options && options.success) {
            options.success({ confirm: true });
        }
    },
    
    vibrateShort: () => {
        console.log('📳 短震动');
    }
};

// 设置全局wx对象
if (typeof wx === 'undefined') {
    global.wx = mockWxEnv;
    console.log('✅ 微信模拟环境已设置');
} else {
    console.log('ℹ️  微信环境已存在');
}

console.log('🎯 现在可以运行高精度模式测试了');

// 导出设置函数
export function setupWxMock() {
    if (typeof wx === 'undefined') {
        global.wx = mockWxEnv;
    }
    return global.wx;
}