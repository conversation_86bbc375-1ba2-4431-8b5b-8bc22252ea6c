/* 导航面板组件样式 */

.navigation-panel {
  position: fixed;
  top: 200rpx;
  left: 20rpx;
  right: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  z-index: 100;
  backdrop-filter: blur(10rpx);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 主导航信息 */
.nav-main-info {
  display: flex;
  align-items: center;
  padding: 30rpx;
}

/* 方向箭头容器 */
.direction-arrow-container {
  margin-right: 30rpx;
}

.direction-arrow {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.5s ease;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.arrow-icon {
  font-size: 48rpx;
  color: white;
  font-weight: bold;
}

/* 导航文字信息 */
.nav-text-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.nav-instruction {
  margin-bottom: 15rpx;
}

.instruction-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.nav-distance {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.distance-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 15rpx;
}

.distance-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #667eea;
}

.nav-time {
  display: flex;
  align-items: baseline;
}

.time-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 15rpx;
}

.time-value {
  font-size: 28rpx;
  color: #666;
}

/* 导航控制按钮 */
.nav-controls {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 30rpx;
  border-top: 1px solid #f0f0f0;
  background: rgba(248, 249, 250, 0.8);
}

.control-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.control-btn:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

.btn-icon {
  font-size: 28rpx;
}

.toggle-btn .btn-icon {
  color: #666;
}

.voice-btn .btn-icon {
  color: #28a745;
}

.replan-btn .btn-icon {
  color: #ffc107;
}

.stop-btn .btn-icon {
  color: #dc3545;
}

/* 进度指示器 */
.nav-progress {
  padding: 20rpx 30rpx;
  border-top: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background: #e9ecef;
  border-radius: 4rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  min-width: 80rpx;
  text-align: right;
}

/* 简化状态 */
.navigation-panel.simple {
  position: static;
  margin: 20rpx;
  padding: 30rpx;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.navigation-panel.simple:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.simple-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.simple-text {
  font-size: 32rpx;
  color: #667eea;
  font-weight: 600;
}

.simple-distance {
  font-size: 28rpx;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .navigation-panel {
    left: 15rpx;
    right: 15rpx;
  }
  
  .nav-main-info {
    padding: 25rpx;
  }
  
  .direction-arrow {
    width: 80rpx;
    height: 80rpx;
  }
  
  .arrow-icon {
    font-size: 40rpx;
  }
  
  .instruction-text {
    font-size: 32rpx;
  }
}

/* 动画效果 */
.navigation-panel {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 方向箭头旋转动画 */
.direction-arrow {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
  }
  50% {
    box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.5);
  }
}