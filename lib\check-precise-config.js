#!/usr/bin/env node

import { LibraryTester } from './test/simple-test.js';

/**
 * 检查高精度模式配置
 */
async function checkPreciseModeConfig() {
    console.log('🔍 检查高精度模式配置...\n');
    
    const tester = new LibraryTester();
    
    try {
        // 导入真实库（如果可用）
        let WXInertialNavigation;
        try {
            // 尝试导入真实库
            const module = await import('./WXInertialNavigation.js');
            WXInertialNavigation = module.default;
            console.log('✅ 成功导入真实惯导库');
        } catch (error) {
            // 使用模拟库
            console.log('ℹ️  使用模拟库进行配置检查');
            await tester.testLibraryImport();
            WXInertialNavigation = tester.WXInertialNavigation;
        }
        
        // 创建不同模式的实例并检查配置
        const modes = ['lite', 'standard', 'precise'];
        
        for (const mode of modes) {
            console.log(`\n📊 检查${mode}模式配置:`);
            console.log('-'.repeat(30));
            
            try {
                const nav = new WXInertialNavigation({
                    initialPosition: { x: 0, y: 0, z: 0 },
                    mode: mode,
                    sampleRate: 50
                });
                
                console.log(`✅ ${mode}模式初始化成功`);
                
                // 检查配置参数
                const config = nav.config;
                console.log(`📋 采样率: ${config.sampleRate}Hz`);
                console.log(`🔧 MLA启用: ${config.enableMLA}`);
                console.log(`🎯 模式: ${config.mode}`);
                
                if (mode === 'precise') {
                    console.log('🔍 高精度模式特殊检查:');
                    console.log(`   - 采样率 >= 50Hz: ${config.sampleRate >= 50}`);
                    console.log(`   - MLA校正启用: ${config.enableMLA === true}`);
                    
                    // 检查融合配置
                    if (config.fusion) {
                        console.log(`   - 自适应权重: ${config.fusion.adaptiveWeighting}`);
                        console.log(`   - 平滑因子: ${config.fusion.smoothingFactor}`);
                    }
                    
                    // 检查质量配置
                    if (config.quality) {
                        console.log(`   - 最小置信度: ${config.quality.minConfidence}`);
                    }
                }
                
            } catch (error) {
                console.error(`❌ ${mode}模式初始化失败:`, error.message);
            }
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ 配置检查失败:', error.message);
        return false;
    }
}

// 运行检查
checkPreciseModeConfig().then(success => {
    console.log('\n' + '='.repeat(50));
    if (success) {
        console.log('🎉 配置检查完成');
    } else {
        console.log('❌ 配置检查失败');
    }
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('❌ 检查运行失败:', error);
    process.exit(1);
});