# 🚀 快速开始指南

## 5分钟集成惯导定位

### 第1步：复制库文件
将 `lib` 文件夹复制到你的小程序项目中：

```
your-miniprogram/
├── lib/                          # 惯导定位库
│   ├── WXInertialNavigation.js   # 主入口文件
│   └── core/                     # 核心模块
└── pages/
    └── index/
        ├── index.js              # 在这里使用
        ├── index.wxml
        └── index.wxss
```

### 第2步：导入库
在页面文件中导入：

```javascript
// pages/index/index.js
import WXInertialNavigation from '../../lib/WXInertialNavigation.js';
```

### 第3步：初始化
在 `onLoad` 中初始化：

```javascript
Page({
  data: {
    position: { x: 0, y: 0, z: 0 },
    isTracking: false
  },

  onLoad() {
    // 创建惯导实例
    this.inertialNav = new WXInertialNavigation({
      initialPosition: { x: 0, y: 0, z: 0 }  // 起始位置
    });
    
    // 设置位置更新回调
    this.inertialNav.setCallbacks({
      onLocationUpdate: (location) => {
        this.setData({
          position: location.position
        });
      }
    });
  }
});
```

### 第4步：启动定位
添加按钮控制：

```javascript
// 启动定位
async startNavigation() {
  const success = await this.inertialNav.start();
  if (success) {
    this.setData({ isTracking: true });
    wx.showToast({ title: '定位已启动', icon: 'success' });
  }
},

// 停止定位
stopNavigation() {
  this.inertialNav.stop();
  this.setData({ isTracking: false });
  wx.showToast({ title: '定位已停止', icon: 'success' });
}
```

### 第5步：添加UI界面
在 WXML 中显示位置：

```xml
<!-- pages/index/index.wxml -->
<view class="container">
  <view class="position-info">
    <text>X: {{position.x}}</text>
    <text>Y: {{position.y}}</text>
    <text>Z: {{position.z}}</text>
  </view>
  
  <button wx:if="{{!isTracking}}" bindtap="startNavigation">
    开始定位
  </button>
  <button wx:else bindtap="stopNavigation" type="warn">
    停止定位
  </button>
</view>
```

## 🎉 完成！

现在你已经成功集成了惯导定位功能。位置信息会实时更新显示在界面上。

## 📋 完整示例代码

```javascript
// pages/index/index.js
import WXInertialNavigation from '../../lib/WXInertialNavigation.js';

Page({
  data: {
    position: { x: 0, y: 0, z: 0 },
    heading: 0,
    stepCount: 0,
    isTracking: false
  },

  onLoad() {
    this.initInertialNav();
  },

  initInertialNav() {
    this.inertialNav = new WXInertialNavigation({
      initialPosition: { x: 0, y: 0, z: 0 },
      mode: 'standard',
      sampleRate: 50
    });

    this.inertialNav.setCallbacks({
      onLocationUpdate: (location) => {
        this.setData({
          position: location.position,
          heading: Math.round(location.heading),
          stepCount: location.stepCount
        });
      },
      onError: (error) => {
        console.error('定位错误:', error);
        wx.showToast({
          title: '定位异常',
          icon: 'none'
        });
      }
    });
  },

  async startNavigation() {
    try {
      const success = await this.inertialNav.start();
      if (success) {
        this.setData({ isTracking: true });
        wx.showToast({
          title: '定位已启动',
          icon: 'success'
        });
      }
    } catch (error) {
      wx.showToast({
        title: '启动失败',
        icon: 'none'
      });
    }
  },

  stopNavigation() {
    this.inertialNav.stop();
    this.setData({ isTracking: false });
    wx.showToast({
      title: '定位已停止',
      icon: 'success'
    });
  },

  resetPosition() {
    this.inertialNav.reset();
    this.setData({
      position: { x: 0, y: 0, z: 0 },
      stepCount: 0
    });
  }
});
```

## 🔧 常用配置

### 轻量模式（省电）
```javascript
new WXInertialNavigation({
  initialPosition: { x: 0, y: 0, z: 0 },
  mode: 'lite',
  sampleRate: 25
});
```

### 高精度模式
```javascript
new WXInertialNavigation({
  initialPosition: { x: 0, y: 0, z: 0 },
  mode: 'precise',
  sampleRate: 100,
  calibration: {
    stepLength: 0.8  // 根据用户身高调整
  }
});
```

## 📱 权限配置

在 `app.json` 中添加权限：

```json
{
  "permission": {
    "scope.userLocation": {
      "desc": "获取位置信息用于室内定位"
    }
  },
  "requiredBackgroundModes": ["location"]
}
```

## 🎯 下一步

- 查看 [完整文档](README.md) 了解更多功能
- 查看 [示例代码](example/demo.js) 学习高级用法
- 运行 [测试用例](test/simple-test.js) 验证功能

需要帮助？查看 [常见问题](README.md#故障排除) 或提交 Issue。