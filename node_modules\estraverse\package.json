{"name": "estraverse", "description": "ECMAScript JS AST traversal functions", "homepage": "https://github.com/Constellation/estraverse", "main": "estraverse.js", "version": "1.5.1", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://github.com/Constellation"}], "repository": {"type": "git", "url": "http://github.com/Constellation/estraverse.git"}, "devDependencies": {"mocha": "~1.12.0", "chai": "~1.7.2", "jshint": "2.1.5", "coffee-script": "~1.6.3", "xyz": "^0.4.0"}, "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/estraverse/raw/master/LICENSE.BSD"}], "scripts": {"test": "npm run-script lint && npm run-script unit-test", "lint": "jshint estraverse.js", "unit-test": "mocha --compilers coffee:coffee-script", "release-major": "xyz --increment major", "release-minor": "xyz --increment minor", "release-patch": "xyz --increment patch"}}