# array-min

  [![NPM version][npm-image]][npm-url]
  [![npm download][download-image]][download-url]

Get the minimum value in an array.

## Installation

`$ npm install --save ml-array-min`

## Usage

```js
import min from 'ml-array-min';

const result = min([1, 5, 3, 2, 4]);
// 1
```

## License

  [MIT](./LICENSE)

[npm-image]: https://img.shields.io/npm/v/ml-array-min.svg?style=flat-square
[npm-url]: https://npmjs.org/package/ml-array-min
[download-image]: https://img.shields.io/npm/dm/ml-array-min.svg?style=flat-square
[download-url]: https://npmjs.org/package/ml-array-min
