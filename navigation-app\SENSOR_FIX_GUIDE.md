# 传感器重复启动问题修复指南

## 问题描述

之前遇到的错误：
```
❌ 加速计启动失败: {errMsg: "startAccelerometer:fail, has enable, should stop pre operation"}
❌ 罗盘启动失败: {errMsg: "startCompass:fail, has enable, should stop pre operation"}
```

## 修复方案

### 1. 智能状态检查
- 启动传感器前检查是否已在运行
- 避免重复启动同一个传感器

### 2. 强化停止逻辑
- 使用带回调的停止API
- 确保传感器完全停止后再启动

### 3. 错误恢复机制
- 检测到重复启动错误时自动强制重启
- 多次尝试停止操作确保彻底关闭

### 4. 容错启动
- 允许部分传感器启动失败
- 加速计必需，陀螺仪和罗盘可选

## 修复的关键代码

### 启动前状态检查
```javascript
if (this.sensorStatus.accelerometer.active) {
  console.log('ℹ️ 加速计已在运行中');
  return;
}
```

### 错误恢复处理
```javascript
fail: (error) => {
  if (error.errMsg && error.errMsg.includes('has enable')) {
    console.log('🔄 检测到传感器已启用，尝试强制重启...');
    setTimeout(async () => {
      await this.forceRestartAccelerometer();
    }, 500);
  }
}
```

### 强制重启机制
```javascript
async forceRestartAccelerometer() {
  // 多次尝试停止
  for (let i = 0; i < 3; i++) {
    try {
      wx.stopAccelerometer();
      await new Promise(resolve => setTimeout(resolve, 200));
    } catch (e) {
      console.log(`停止加速计尝试 ${i + 1} 失败:`, e);
    }
  }
  
  // 等待更长时间确保完全停止
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 重新启动
  await this.startAccelerometer();
}
```

## 测试步骤

1. **清理旧状态**
   ```javascript
   // 在小程序启动时
   await app.stopNavigation(); // 确保清理旧状态
   await app.startNavigation(); // 重新启动
   ```

2. **观察启动日志**
   - 应该看到 "传感器采集启动完成，活跃传感器: X/3"
   - 不应该出现 "has enable, should stop pre operation" 错误

3. **验证位置更新**
   - 检查控制台是否有 "📊 传感器数据采集成功"
   - 观察位置数据是否正常更新

## 预期效果

✅ 传感器启动不再报错  
✅ 位置信息正常更新  
✅ 支持部分传感器失败的情况  
✅ 具备自动恢复能力  

## 如果仍有问题

1. **检查权限**
   ```javascript
   wx.getSetting({
     success: (res) => {
       console.log('权限状态:', res.authSetting);
     }
   });
   ```

2. **查看传感器状态**
   ```javascript
   const status = app.globalData.inertialNav.sensorManager.getStatus();
   console.log('传感器状态:', status);
   ```

3. **手动重启**
   ```javascript
   await app.globalData.inertialNav.sensorManager.restart();
   ```

## 注意事项

- 修复后首次启动可能需要更长时间
- 建议在真机环境测试，模拟器可能行为不一致
- 传感器权限必须正确授予