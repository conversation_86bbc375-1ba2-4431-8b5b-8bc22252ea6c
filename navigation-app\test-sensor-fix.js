/**
 * 传感器修复验证测试
 * 用于验证传感器重复启动问题的修复
 */

// 模拟微信小程序API
global.wx = {
  onAccelerometerChange: () => console.log('📱 加速计监听设置成功'),
  onGyroscopeChange: () => console.log('📱 陀螺仪监听设置成功'),
  onCompassChange: () => console.log('📱 罗盘监听设置成功'),
  
  startAccelerometer: ({ success, fail }) => {
    console.log('🚀 尝试启动加速计');
    // 模拟已启用的情况
    if (Math.random() > 0.5) {
      success && success();
    } else {
      fail && fail({ errMsg: "startAccelerometer:fail, has enable, should stop pre operation" });
    }
  },
  
  startGyroscope: ({ success, fail }) => {
    console.log('🚀 尝试启动陀螺仪');
    success && success();
  },
  
  startCompass: ({ success, fail }) => {
    console.log('🚀 尝试启动罗盘');
    // 模拟已启用的情况
    if (Math.random() > 0.5) {
      success && success();
    } else {
      fail && fail({ errMsg: "startCompass:fail, has enable, should stop pre operation" });
    }
  },
  
  stopAccelerometer: ({ success, fail }) => {
    console.log('⏹️ 停止加速计');
    success && success();
  },
  
  stopGyroscope: ({ success, fail }) => {
    console.log('⏹️ 停止陀螺仪'); 
    success && success();
  },
  
  stopCompass: ({ success, fail }) => {
    console.log('⏹️ 停止罗盘');
    success && success();
  },
  
  getSystemInfo: ({ success }) => {
    success && success({
      platform: 'ios',
      model: 'iPhone 13'
    });
  }
};

async function testSensorFix() {
  console.log('🧪 开始传感器修复验证测试\n');
  
  try {
    // 使用import导入传感器管理器
    const { default: InertialSensorManager } = await import('./lib/core/InertialSensorManager.js');
    
    // 创建传感器管理器实例
    const sensorManager = new InertialSensorManager({
      sampleRate: 50,
      enableFiltering: true
    });
    
    console.log('✅ 传感器管理器创建成功\n');
    
    // 测试启动传感器
    console.log('📡 测试传感器启动...');
    await sensorManager.start();
    
    console.log('\n📊 传感器状态:');
    const status = sensorManager.getStatus();
    console.log('  运行中:', status.isRunning);
    console.log('  传感器状态:', {
      accelerometer: status.sensors.accelerometer.active,
      gyroscope: status.sensors.gyroscope.active,
      magnetometer: status.sensors.magnetometer.active
    });
    
    // 等待一段时间
    console.log('\n⏳ 等待传感器数据...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 测试重复启动（这应该不会产生错误）
    console.log('\n🔄 测试重复启动（应该被优雅处理）...');
    await sensorManager.start();
    
    // 测试停止
    console.log('\n⏹️ 测试停止传感器...');
    sensorManager.stop();
    
    const finalStatus = sensorManager.getStatus();
    console.log('停止后状态:', finalStatus.isRunning);
    
    console.log('\n🎉 传感器修复验证测试完成！');
    console.log('✅ 所有测试通过，传感器重复启动问题已修复');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
testSensorFix().catch(console.error);