/**
 * 自动模式管理器
 * Auto Mode Manager
 * 
 * 实现lite、standard、precise模式的智能自动切换
 * 基于用户行为、设备性能、定位精度需求进行动态调整
 */

class AutoModeManager {
  constructor(options = {}) {
    this.config = {
      // 模式切换策略
      strategy: options.strategy || 'adaptive', // 'adaptive', 'performance', 'accuracy'
      
      // 切换条件阈值
      thresholds: {
        // 步数阈值
        liteToStandard: 5,      // 5步后可升级到standard
        standardToPrecise: 12,   // 12步后可升级到precise
        
        // 性能阈值
        maxProcessingTime: 50,   // 最大处理时间(ms)
        memoryThreshold: 0.8,    // 内存使用阈值
        batteryThreshold: 0.2,   // 电池电量阈值
        
        // 精度阈值
        minAccuracy: 0.6,        // 最小精度要求
        stabilityWindow: 10,     // 稳定性检测窗口
        
        // 时间阈值
        minModeTime: 3000,       // 最小模式持续时间(ms)
        evaluationInterval: 1000 // 评估间隔(ms)
      },
      
      // 模式配置
      modes: {
        lite: {
          sampleRate: 25,
          enableMLA: false,
          smoothingFactor: 0.7,
          minConfidence: 0.1,
          processingLevel: 'LITE'
        },
        standard: {
          sampleRate: 50,
          enableMLA: true,
          smoothingFactor: 0.5,
          minConfidence: 0.2,
          processingLevel: 'STANDARD'
        },
        precise: {
          sampleRate: 60,
          enableMLA: true,
          smoothingFactor: 0.3,
          minConfidence: 0.3,
          processingLevel: 'ADVANCED'
        }
      },
      
      ...options
    };
    
    // 当前状态
    this.currentMode = 'lite';
    this.modeStartTime = Date.now();
    this.lastEvaluation = Date.now();
    
    // 统计数据
    this.statistics = {
      stepCount: 0,
      accuracyHistory: [],
      performanceHistory: [],
      modeHistory: [],
      switchCount: 0
    };
    
    // 设备信息
    this.deviceInfo = {
      battery: 1.0,
      memory: 0.5,
      cpu: 'unknown',
      isLowEnd: false
    };
    
    // 回调函数
    this.onModeChange = null;
    this.onEvaluation = null;
    
    console.log('🔄 自动模式管理器初始化完成');
    this.initializeDeviceMonitoring();
  }
  
  /**
   * 初始化设备监控
   */
  initializeDeviceMonitoring() {
    // 检测设备性能
    this.detectDeviceCapability();
    
    // 启动定期评估
    this.startPeriodicEvaluation();
    
    // 监听电池状态变化
    if ('getBattery' in navigator) {
      navigator.getBattery().then(battery => {
        this.deviceInfo.battery = battery.level;
        battery.addEventListener('levelchange', () => {
          this.deviceInfo.battery = battery.level;
          this.evaluateMode();
        });
      });
    }
  }
  
  /**
   * 检测设备性能能力
   */
  detectDeviceCapability() {
    // 检测内存
    if ('memory' in performance) {
      const memInfo = performance.memory;
      this.deviceInfo.memory = memInfo.usedJSHeapSize / memInfo.jsHeapSizeLimit;
    }
    
    // 检测CPU性能（简单基准测试）
    const startTime = performance.now();
    let iterations = 0;
    while (performance.now() - startTime < 10) {
      Math.random() * Math.random();
      iterations++;
    }
    
    // 根据迭代次数判断设备性能
    this.deviceInfo.isLowEnd = iterations < 100000;
    this.deviceInfo.cpu = iterations > 500000 ? 'high' : iterations > 200000 ? 'medium' : 'low';
    
    console.log('📱 设备性能检测:', this.deviceInfo);
  }
  
  /**
   * 启动定期评估
   */
  startPeriodicEvaluation() {
    setInterval(() => {
      this.evaluateMode();
    }, this.config.thresholds.evaluationInterval);
  }
  
  /**
   * 更新统计数据
   * @param {Object} data - 统计数据
   */
  updateStatistics(data) {
    if (data.stepCount !== undefined) {
      this.statistics.stepCount = data.stepCount;
    }
    
    if (data.accuracy !== undefined) {
      this.statistics.accuracyHistory.push(data.accuracy);
      if (this.statistics.accuracyHistory.length > this.config.thresholds.stabilityWindow) {
        this.statistics.accuracyHistory.shift();
      }
    }
    
    if (data.processingTime !== undefined) {
      this.statistics.performanceHistory.push(data.processingTime);
      if (this.statistics.performanceHistory.length > 20) {
        this.statistics.performanceHistory.shift();
      }
    }
  }
  
  /**
   * 评估并决定是否切换模式
   */
  evaluateMode() {
    const now = Date.now();
    
    // 检查最小模式持续时间
    if (now - this.modeStartTime < this.config.thresholds.minModeTime) {
      return;
    }
    
    const recommendation = this.calculateModeRecommendation();
    
    if (recommendation.mode !== this.currentMode) {
      this.switchToMode(recommendation.mode, recommendation.reason);
    }
    
    this.lastEvaluation = now;
    
    // 触发评估回调
    if (this.onEvaluation) {
      this.onEvaluation({
        currentMode: this.currentMode,
        recommendation: recommendation,
        statistics: this.getStatistics()
      });
    }
  }
  
  /**
   * 计算模式推荐
   * @returns {Object} 推荐结果
   */
  calculateModeRecommendation() {
    const factors = this.analyzeFactors();
    
    switch (this.config.strategy) {
      case 'performance':
        return this.calculatePerformanceBasedMode(factors);
      case 'accuracy':
        return this.calculateAccuracyBasedMode(factors);
      case 'adaptive':
      default:
        return this.calculateAdaptiveMode(factors);
    }
  }
  
  /**
   * 分析影响因素
   * @returns {Object} 因素分析结果
   */
  analyzeFactors() {
    const avgAccuracy = this.statistics.accuracyHistory.length > 0 ?
      this.statistics.accuracyHistory.reduce((a, b) => a + b) / this.statistics.accuracyHistory.length : 0.5;
    
    const avgProcessingTime = this.statistics.performanceHistory.length > 0 ?
      this.statistics.performanceHistory.reduce((a, b) => a + b) / this.statistics.performanceHistory.length : 20;
    
    return {
      stepCount: this.statistics.stepCount,
      accuracy: avgAccuracy,
      processingTime: avgProcessingTime,
      battery: this.deviceInfo.battery,
      memory: this.deviceInfo.memory,
      isLowEnd: this.deviceInfo.isLowEnd,
      isStable: this.isPerformanceStable()
    };
  }
  
  /**
   * 计算自适应模式
   * @param {Object} factors - 影响因素
   * @returns {Object} 推荐结果
   */
  calculateAdaptiveMode(factors) {
    const { stepCount, accuracy, processingTime, battery, memory, isLowEnd, isStable } = factors;
    
    // 电池电量过低，强制lite模式
    if (battery < this.config.thresholds.batteryThreshold) {
      return { mode: 'lite', reason: '电池电量过低，切换到省电模式' };
    }
    
    // 设备性能不足，限制最高模式
    if (isLowEnd || memory > this.config.thresholds.memoryThreshold) {
      if (this.currentMode === 'precise') {
        return { mode: 'standard', reason: '设备性能不足，降级到标准模式' };
      }
      if (processingTime > this.config.thresholds.maxProcessingTime) {
        return { mode: 'lite', reason: '处理时间过长，降级到轻量模式' };
      }
    }
    
    // 基于步数和精度的升级逻辑
    if (stepCount >= this.config.thresholds.standardToPrecise && 
        accuracy >= this.config.thresholds.minAccuracy && 
        isStable && 
        !isLowEnd) {
      return { mode: 'precise', reason: '步数充足且精度稳定，升级到精确模式' };
    }
    
    if (stepCount >= this.config.thresholds.liteToStandard && 
        processingTime < this.config.thresholds.maxProcessingTime) {
      return { mode: 'standard', reason: '步数达标且性能良好，升级到标准模式' };
    }
    
    // 保持当前模式
    return { mode: this.currentMode, reason: '当前模式适合，保持不变' };
  }
  
  /**
   * 检查性能是否稳定
   * @returns {boolean} 是否稳定
   */
  isPerformanceStable() {
    if (this.statistics.performanceHistory.length < 5) return false;
    
    const recent = this.statistics.performanceHistory.slice(-5);
    const avg = recent.reduce((a, b) => a + b) / recent.length;
    const variance = recent.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / recent.length;
    
    return variance < 100; // 方差小于100ms²认为稳定
  }
  
  /**
   * 切换到指定模式
   * @param {string} mode - 目标模式
   * @param {string} reason - 切换原因
   */
  switchToMode(mode, reason) {
    if (mode === this.currentMode) return;
    
    const oldMode = this.currentMode;
    this.currentMode = mode;
    this.modeStartTime = Date.now();
    this.statistics.switchCount++;
    
    // 记录模式历史
    this.statistics.modeHistory.push({
      mode: mode,
      timestamp: Date.now(),
      reason: reason
    });
    
    console.log(`🔄 模式切换: ${oldMode} → ${mode}`, { 原因: reason });
    
    // 触发模式变更回调
    if (this.onModeChange) {
      this.onModeChange({
        oldMode: oldMode,
        newMode: mode,
        reason: reason,
        config: this.config.modes[mode]
      });
    }
  }
  
  /**
   * 获取当前统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    return {
      currentMode: this.currentMode,
      modeUptime: Date.now() - this.modeStartTime,
      switchCount: this.statistics.switchCount,
      stepCount: this.statistics.stepCount,
      averageAccuracy: this.statistics.accuracyHistory.length > 0 ?
        this.statistics.accuracyHistory.reduce((a, b) => a + b) / this.statistics.accuracyHistory.length : 0,
      deviceInfo: this.deviceInfo
    };
  }
  
  /**
   * 手动设置模式
   * @param {string} mode - 目标模式
   */
  setMode(mode) {
    if (this.config.modes[mode]) {
      this.switchToMode(mode, '用户手动设置');
    }
  }
  
  /**
   * 获取当前模式配置
   * @returns {Object} 模式配置
   */
  getCurrentModeConfig() {
    return this.config.modes[this.currentMode];
  }
}

export default AutoModeManager;
