<!--PDR演示页面 - 简化版本-->
<view class="pdr-demo-container">
  
  <!-- 顶部标题 -->
  <view class="header">
    <text class="app-title">PDR演示</text>
    <button class="menu-button" bindtap="showMenu" size="mini">⋯</button>
  </view>

  <!-- 传感器数据显示区域 -->
  <view class="sensor-data">
    
    <!-- 左侧传感器数据 -->
    <view class="data-left">
      <view class="data-row">
        <text class="data-label">方向角[度]:</text>
        <text class="data-value">{{heading.toFixed(1)}}</text>
      </view>
      <view class="data-row">
        <text class="data-label">步长[米]:</text>
        <text class="data-value">{{stepLength.toFixed(2)}}</text>
      </view>
      <view class="data-row">
        <text class="data-label">X坐标[米]:</text>
        <text class="data-value">{{position.x.toFixed(2)}}</text>
      </view>
      <view class="data-row">
        <text class="data-label">Y坐标[米]:</text>
        <text class="data-value">{{position.y.toFixed(2)}}</text>
      </view>
      <view class="data-row">
        <text class="data-label">Z坐标[米]:</text>
        <text class="data-value">{{position.z.toFixed(2)}}</text>
      </view>
    </view>

    <!-- 右侧控制按钮 -->
    <view class="controls-right">
      <button class="control-button init-btn" bindtap="initPDR" disabled="{{isRunning || isInitializing}}">
        <text wx:if="{{isInitializing}}">{{initProgress}}%</text>
        <text wx:else>INIT</text>
      </button>
      <button class="control-button start-btn" bindtap="startPDR" disabled="{{isRunning}}">
        START
      </button>
      <button class="control-button stop-btn" bindtap="stopPDR" disabled="{{!isRunning}}">
        STOP
      </button>
      <button class="control-button destroy-btn" bindtap="destroyPDR" disabled="{{isRunning}}">
        DESTROY
      </button>
    </view>
  </view>

  <!-- 统计信息区域 -->
  <view class="stats-area">
    <view class="stats-left">
      <view class="stats-row">
        <text class="stats-label">步数:</text>
        <text class="stats-value">{{stepCount}}</text>
      </view>
      <view class="stats-row">
        <text class="stats-label">楼层:</text>
        <text class="stats-value">{{floor}}</text>
      </view>
    </view>
    
    <!-- 功能按钮 -->
    <view class="function-buttons">
      <button class="func-button save-btn" bindtap="saveData" disabled="{{!hasData}}">
        保存数据
      </button>
      <button class="func-button export-btn" bindtap="exportData" disabled="{{!hasData}}">
        导出
      </button>
    </view>
  </view>

  <!-- 轨迹显示区域 -->
  <view class="trajectory-area">
    <view class="trajectory-canvas" id="trajectory-canvas" 
          bindtouchstart="onTouchStart"
          bindtouchmove="onTouchMove" 
          bindtouchend="onTouchEnd"
          bindtap="onCanvasTap">
      <!-- 坐标系背景 -->
      <view class="coordinate-grid">
        <!-- 垂直线 -->
        <view class="grid-line vertical" style="left: 50%"></view>
        <!-- 水平线 -->
        <view class="grid-line horizontal" style="top: 50%"></view>
        
        <!-- 刻度标记 -->
        <view class="axis-labels">
          <!-- X轴刻度 -->
          <text class="axis-label x-positive" style="right: 10px; top: calc(50% + 5px)">+X</text>
          <text class="axis-label x-negative" style="left: 10px; top: calc(50% + 5px)">-X</text>
          <!-- Y轴刻度 -->
          <text class="axis-label y-positive" style="top: 10px; left: calc(50% + 5px)">+Y</text>
          <text class="axis-label y-negative" style="bottom: 10px; left: calc(50% + 5px)">-Y</text>
        </view>
      </view>
      
      <!-- 轨迹点 -->
      <view wx:for="{{trajectoryPoints}}" wx:key="index" 
            class="trajectory-point"
            style="left: {{50 + (item.x * trajectoryScale * zoomLevel) + offsetX}}%; top: {{50 - (item.y * trajectoryScale * zoomLevel) + offsetY}}%;">
      </view>
      
      <!-- 当前位置指示器 -->
      <view class="current-position" 
            style="left: {{50 + (position.x * trajectoryScale * zoomLevel) + offsetX}}%; top: {{50 - (position.y * trajectoryScale * zoomLevel) + offsetY}}%; transform: rotate({{heading}}deg);">
        <text class="position-arrow">▲</text>
      </view>
      
      <!-- 起始点 -->
      <view wx:if="{{trajectoryPoints.length > 0}}" 
            class="start-point"
            style="left: {{50 + (trajectoryPoints[0].x * trajectoryScale * zoomLevel) + offsetX}}%; top: {{50 - (trajectoryPoints[0].y * trajectoryScale * zoomLevel) + offsetY}}%;">
        <text class="start-marker">🔵</text>
      </view>
    </view>
    
    <!-- 轨迹信息 -->
    <view class="trajectory-info">
      <text class="info-text">总距离: {{totalDistance.toFixed(2)}}米</text>
      <text class="info-text">轨迹点: {{trajectoryPoints.length}}个</text>
      <text class="info-text">缩放: {{zoomLevel.toFixed(1)}}x</text>
      <text class="info-text" wx:if="{{isRunning}}">状态: 运行中</text>
      <text class="info-text" wx:else>状态: 已停止</text>
    </view>
    
    <!-- 轨迹操作提示 -->
    <view class="trajectory-controls">
      <text class="control-hint">双指缩放 | 单指拖拽 | 双击重置</text>
      <button class="reset-view-btn" bindtap="resetTrajectoryView" size="mini">
        重置视图
      </button>
    </view>
  </view>

  <!-- 状态信息面板 -->
  <view wx:if="{{showStatus}}" class="status-panel">
    <view class="status-header">
      <text class="status-title">传感器状态</text>
      <button class="status-close" bindtap="hideStatus" size="mini">×</button>
    </view>
    <view class="status-content">
      <view class="status-item">
        <text class="status-label">加速计:</text>
        <text class="status-value {{accelerometerSupported ? (accelerometerStatus ? 'active' : 'inactive') : 'unsupported'}}">
          {{accelerometerSupported ? (accelerometerStatus ? '运行中' : '已停止') : '不支持'}}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">陀螺仪:</text>
        <text class="status-value {{gyroscopeSupported ? (gyroscopeStatus ? 'active' : 'inactive') : 'unsupported'}}">
          {{gyroscopeSupported ? (gyroscopeStatus ? '运行中' : '已停止') : '不支持'}}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">磁力计:</text>
        <text class="status-value {{magnetometerSupported ? (magnetometerStatus ? 'active' : 'inactive') : 'unsupported'}}">
          {{magnetometerSupported ? (magnetometerStatus ? '运行中' : '已停止') : '不支持'}}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">更新频率:</text>
        <text class="status-value">{{updateRate}}Hz</text>
      </view>
    </view>
  </view>

</view>