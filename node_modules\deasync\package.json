{"name": "deas<PERSON>", "version": "0.1.30", "description": "Turns async function into sync via JavaScript wrapper of Node event loop", "main": "index.js", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/abbr)"], "license": "MIT", "scripts": {"install": "node ./build.js", "test": "node spec"}, "dependencies": {"bindings": "^1.5.0", "node-addon-api": "^1.7.1"}, "repository": {"type": "git", "url": "https://github.com/abbr/deasync.git"}, "homepage": "https://github.com/abbr/deasync", "keywords": ["async", "sync", "sleep", "async wrapper"], "engines": {"node": ">=0.11.0"}, "devDependencies": {"require-directory": "^2.1.1"}}