/* 设置页面样式 */

.settings-page {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx 40rpx;
  color: white;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

/* 设置内容 */
.settings-content {
  flex: 1;
  padding: 20rpx;
}

/* 设置分组 */
.settings-section {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx 20rpx 40rpx;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.section-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 设置项 */
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background-color: #f8f9fa;
}

.setting-item.danger .label-text {
  color: #dc3545;
}

.setting-label {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.label-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.label-desc {
  font-size: 24rpx;
  color: #666;
}

.setting-value {
  display: flex;
  align-items: center;
}

.value-text {
  font-size: 28rpx;
  color: #667eea;
  margin-right: 10rpx;
}

.arrow {
  font-size: 28rpx;
  color: #ccc;
}

.setting-control {
  min-width: 200rpx;
  display: flex;
  justify-content: flex-end;
}

/* 状态指示器 */
.status-indicator {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.status-indicator.running {
  background: #28a745;
  animation: pulse 1.5s infinite;
}

.status-indicator.stopped {
  background: #dc3545;
}

.status-indicator.unknown {
  background: #ffc107;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 模式选择弹窗 */
.mode-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.mode-selector-popup {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.popup-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666;
}

.mode-list {
  padding: 20rpx 0;
}

.mode-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  transition: background-color 0.2s ease;
}

.mode-item:active {
  background-color: #f8f9fa;
}

.mode-item.selected {
  background-color: #667eea10;
}

.mode-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.mode-name {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.mode-desc {
  font-size: 24rpx;
  color: #666;
}

.mode-check {
  font-size: 32rpx;
  color: #667eea;
  font-weight: bold;
}

/* 校准面板 */
.calibration-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.calibration-popup {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 500rpx;
  animation: slideUp 0.3s ease-out;
}

.calibration-content {
  padding: 40rpx;
}

.calibration-steps {
  margin-bottom: 40rpx;
}

.step-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.step-item {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  padding-left: 20rpx;
}

.calibration-actions {
  text-align: center;
}

.calibration-button {
  background: #667eea;
  color: white;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 32rpx;
  border: none;
}

.calibration-button:active {
  opacity: 0.8;
}

/* 底部间距 */
.bottom-spacing {
  height: 100rpx;
}

/* 动画效果 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滑块自定义样式 */
slider {
  width: 100%;
}

/* 开关自定义样式 */
switch {
  transform: scale(0.8);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .setting-item {
    padding: 25rpx 30rpx;
  }
  
  .section-header {
    padding: 25rpx 30rpx 15rpx 30rpx;
  }
  
  .page-header {
    padding: 50rpx 30rpx 30rpx 30rpx;
  }
}