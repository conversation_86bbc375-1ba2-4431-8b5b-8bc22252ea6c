// PDR演示页面
const WXInertialNavigation = require('../../lib/WXInertialNavigation.js');

Page({
  data: {
    // 传感器数据
    heading: 0.0,
    stepLength: 0.0,
    position: {
      x: 0.0,
      y: 0.0,
      z: 0.0
    },
    
    // 统计数据
    stepCount: 0,
    floor: 1,
    totalDistance: 0.0,
    
    // 控制状态
    isRunning: false,
    hasData: false,
    
    // 轨迹数据
    trajectoryPoints: [],
    trajectoryScale: 10, // 缩放比例，1米 = 10%
    zoomLevel: 1.0, // 缩放级别
    offsetX: 0, // X轴偏移
    offsetY: 0, // Y轴偏移
    
    // 触摸控制
    lastTouchDistance: 0, // 双指距离
    lastTouchX: 0, // 上次触摸X坐标
    lastTouchY: 0, // 上次触摸Y坐标
    isDragging: false, // 是否正在拖拽
    
    // 传感器状态
    showStatus: false,
    accelerometerStatus: false,
    gyroscopeStatus: false,
    magnetometerStatus: false,
    accelerometerSupported: false,
    gyroscopeSupported: false,
    magnetometerSupported: false,
    updateRate: 0,
    
    // 初始化状态
    isInitializing: false,
    initProgress: 0,
    
    // 更新计数器
    updateCount: 0,
    lastUpdateTime: 0
  },

  onLoad() {
    console.log('📱 PDR演示页面加载');
    this.initializeNavigation();
  },

  onUnload() {
    console.log('📱 PDR演示页面卸载');
    if (this.navigationInstance) {
      this.navigationInstance.stop();
    }
  },

  /**
   * 初始化导航系统
   */
  async initializeNavigation() {
    try {
      // 创建导航实例
      this.navigationInstance = new WXInertialNavigation({
        // 必需参数：初始位置
        initialPosition: { x: 0, y: 0, z: 0 },
        
        // 运行模式
        mode: 'standard',
        sampleRate: 50,
        
        // 传感器配置
        sensorConfig: {
          accelerometerInterval: 'game',
          gyroscopeInterval: 'game',
          compassInterval: 'game'
        },
        
        // 数据质量控制
        quality: {
          minConfidence: 0.1,
          maxSpeed: 10.0,
          maxJumpDistance: 100.0
        },
        
        // 调试选项
        enableDebug: true
      });

      // 设置回调函数
      this.navigationInstance.setCallbacks({
        onLocationUpdate: (location) => {
          this.handleLocationUpdate(location);
        },
        onStepDetected: (stepInfo) => {
          console.log('👣 检测到步态:', stepInfo);
        },
        onError: (error) => {
          console.error('❌ PDR系统错误:', error);
        }
      });

      console.log('✅ PDR系统初始化完成');
    } catch (error) {
      console.error('❌ PDR系统初始化失败:', error);
      wx.showToast({
        title: '初始化失败',
        icon: 'none'
      });
    }
  },

  /**
   * 处理位置更新
   */
  handleLocationUpdate(location) {
    console.log('📍 位置更新:', location);
    
    const now = Date.now();
    const position = location.position || {};
    
    this.setData({
      position: {
        x: position.x || 0,
        y: position.y || 0,
        z: position.z || 0
      },
      heading: location.heading || 0,
      stepLength: location.stepLength || 0.65, // 默认步长65cm
      stepCount: location.stepCount || this.data.stepCount,
      floor: Math.floor(position.z || 0) + 1, // 楼层从1开始
      hasData: true
    });

    // 添加到轨迹
    if (position.x !== undefined && position.y !== undefined) {
      const trajectoryPoints = [...this.data.trajectoryPoints];
      trajectoryPoints.push({
        x: position.x,
        y: position.y,
        timestamp: now
      });

      // 计算总距离
      let totalDistance = 0;
      for (let i = 1; i < trajectoryPoints.length; i++) {
        const dx = trajectoryPoints[i].x - trajectoryPoints[i-1].x;
        const dy = trajectoryPoints[i].y - trajectoryPoints[i-1].y;
        totalDistance += Math.sqrt(dx*dx + dy*dy);
      }

      this.setData({
        trajectoryPoints: trajectoryPoints,
        totalDistance: totalDistance
      });
    }

    // 更新频率计算
    this.data.updateCount++;
    if (now - this.data.lastUpdateTime >= 1000) {
      this.setData({
        updateRate: this.data.updateCount
      });
      this.data.updateCount = 0;
      this.data.lastUpdateTime = now;
    }
  },

  /**
   * 初始化PDR系统
   */
  async initPDR() {
    console.log('🔧 初始化PDR系统');
    
    this.setData({ 
      isInitializing: true, 
      initProgress: 0 
    });
    
    try {
      // 第1步：重置数据状态
      this.setData({
        position: { x: 0, y: 0, z: 0 },
        heading: 0,
        stepCount: 0,
        totalDistance: 0,
        trajectoryPoints: [],
        hasData: false,
        accelerometerStatus: false,
        gyroscopeStatus: false,
        magnetometerStatus: false,
        zoomLevel: 1.0,
        offsetX: 0,
        offsetY: 0,
        isRunning: false,
        initProgress: 20
      });
      
      // 第2步：检查传感器可用性
      console.log('📊 检查传感器可用性...');
      await this.checkSensorAvailability();
      this.setData({ initProgress: 50 });
      
      // 第3步：预热传感器
      console.log('🔥 预热传感器...');
      await this.warmupSensors();
      this.setData({ initProgress: 80 });
      
      // 第4步：完成初始化
      this.setData({ 
        initProgress: 100,
        isInitializing: false 
      });
      
      wx.showToast({
        title: '初始化成功',
        icon: 'success'
      });
      
      console.log('✅ PDR系统初始化完成', {
        accelerometer: this.data.accelerometerSupported,
        gyroscope: this.data.gyroscopeSupported,
        magnetometer: this.data.magnetometerSupported
      });
      
    } catch (error) {
      console.error('❌ PDR初始化失败:', error);
      this.setData({ 
        isInitializing: false,
        initProgress: 0
      });
      wx.showToast({
        title: '初始化失败',
        icon: 'none'
      });
    }
  },

  /**
   * 启动PDR系统
   */
  async startPDR() {
    console.log('▶️ 启动PDR系统');
    
    try {
      if (this.navigationInstance) {
        // 传递启动配置
        const startConfig = {
          initialPosition: { x: 0, y: 0, z: 0 },
          mode: 'standard',
          sampleRate: 50
        };
        
        await this.navigationInstance.start(startConfig);
        
        this.setData({
          isRunning: true,
          accelerometerStatus: true,
          gyroscopeStatus: true,
          magnetometerStatus: true
        });
        
        wx.showToast({
          title: '已启动',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('❌ PDR启动失败:', error);
      wx.showToast({
        title: '启动失败',
        icon: 'none'
      });
    }
  },

  /**
   * 停止PDR系统
   */
  stopPDR() {
    console.log('⏹️ 停止PDR系统, 当前状态:', this.data.isRunning);
    
    try {
      if (this.navigationInstance && this.data.isRunning) {
        this.navigationInstance.stop();
        
        this.setData({
          isRunning: false,
          updateRate: 0,
          accelerometerStatus: false,
          gyroscopeStatus: false,
          magnetometerStatus: false
        });
        
        console.log('✅ PDR系统已停止');
        wx.showToast({
          title: '已停止',
          icon: 'success'
        });
      } else {
        console.warn('⚠️ PDR系统未运行或实例不存在');
        wx.showToast({
          title: '系统未运行',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('❌ PDR停止失败:', error);
      wx.showToast({
        title: '停止失败',
        icon: 'none'
      });
    }
  },

  /**
   * 销毁PDR系统
   */
  async destroyPDR() {
    console.log('🗑️ 销毁PDR系统');
    
    try {
      if (this.navigationInstance) {
        this.navigationInstance.stop();
        
        // 重置所有数据
        this.setData({
          position: { x: 0, y: 0, z: 0 },
          heading: 0,
          stepCount: 0,
          totalDistance: 0,
          trajectoryPoints: [],
          isRunning: false,
          hasData: false,
          updateRate: 0,
          accelerometerStatus: false,
          gyroscopeStatus: false,
          magnetometerStatus: false
        });
        
        wx.showToast({
          title: '已销毁',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('❌ PDR销毁失败:', error);
      wx.showToast({
        title: '销毁失败',
        icon: 'none'
      });
    }
  },

  /**
   * 保存数据
   */
  saveData() {
    console.log('💾 保存轨迹数据');
    
    if (this.data.trajectoryPoints.length === 0) {
      wx.showToast({
        title: '没有数据可保存',
        icon: 'none'
      });
      return;
    }

    try {
      const data = {
        timestamp: new Date().toISOString(),
        totalDistance: this.data.totalDistance,
        stepCount: this.data.stepCount,
        trajectoryPoints: this.data.trajectoryPoints,
        summary: {
          startTime: this.data.trajectoryPoints[0]?.timestamp,
          endTime: this.data.trajectoryPoints[this.data.trajectoryPoints.length - 1]?.timestamp,
          pointCount: this.data.trajectoryPoints.length
        }
      };

      // 保存到本地存储
      const key = `pdr_data_${Date.now()}`;
      wx.setStorageSync(key, data);
      
      wx.showToast({
        title: '数据已保存',
        icon: 'success'
      });
    } catch (error) {
      console.error('❌ 保存数据失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 导出数据
   */
  exportData() {
    console.log('📤 导出轨迹数据');
    
    if (this.data.trajectoryPoints.length === 0) {
      wx.showToast({
        title: '没有数据可导出',
        icon: 'none'
      });
      return;
    }

    try {
      const data = {
        version: '1.0',
        timestamp: new Date().toISOString(),
        totalDistance: this.data.totalDistance,
        stepCount: this.data.stepCount,
        trajectoryPoints: this.data.trajectoryPoints
      };

      // 转换为CSV格式
      const csvHeader = 'timestamp,x,y,distance\n';
      let csvContent = csvHeader;
      
      this.data.trajectoryPoints.forEach((point, index) => {
        let distance = 0;
        if (index > 0) {
          const prev = this.data.trajectoryPoints[index - 1];
          const dx = point.x - prev.x;
          const dy = point.y - prev.y;
          distance = Math.sqrt(dx*dx + dy*dy);
        }
        
        csvContent += `${point.timestamp},${point.x.toFixed(3)},${point.y.toFixed(3)},${distance.toFixed(3)}\n`;
      });

      // 显示导出信息
      wx.showModal({
        title: '导出数据',
        content: `轨迹点: ${this.data.trajectoryPoints.length}\n总距离: ${this.data.totalDistance.toFixed(2)}米\n步数: ${this.data.stepCount}`,
        showCancel: false,
        confirmText: '确定'
      });
      
    } catch (error) {
      console.error('❌ 导出数据失败:', error);
      wx.showToast({
        title: '导出失败',
        icon: 'none'
      });
    }
  },

  /**
   * 显示菜单
   */
  showMenu() {
    wx.showActionSheet({
      itemList: ['传感器状态', '清空轨迹', '系统信息'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.setData({ showStatus: true });
            break;
          case 1:
            this.clearTrajectory();
            break;
          case 2:
            this.showSystemInfo();
            break;
        }
      }
    });
  },

  /**
   * 清空轨迹
   */
  clearTrajectory() {
    wx.showModal({
      title: '确认清空',
      content: '是否清空当前轨迹数据？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            trajectoryPoints: [],
            totalDistance: 0,
            stepCount: 0,
            hasData: false
          });
          wx.showToast({
            title: '轨迹已清空',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 显示系统信息
   */
  showSystemInfo() {
    const info = `PDR演示版本: 1.0\n轨迹点数: ${this.data.trajectoryPoints.length}\n更新频率: ${this.data.updateRate}Hz\n当前状态: ${this.data.isRunning ? '运行中' : '已停止'}`;
    
    wx.showModal({
      title: '系统信息',
      content: info,
      showCancel: false,
      confirmText: '确定'
    });
  },

  /**
   * 隐藏状态面板
   */
  hideStatus() {
    this.setData({
      showStatus: false
    });
  },

  /**
   * 检查传感器可用性
   */
  async checkSensorAvailability() {
    return new Promise((resolve) => {
      let checkedCount = 0;
      const totalSensors = 3;
      
      // 检查加速计
      wx.onAccelerometerChange(() => {});
      wx.startAccelerometer({
        interval: 'normal',
        success: () => {
          console.log('✅ 加速计可用');
          this.setData({ accelerometerSupported: true });
          wx.stopAccelerometer();
        },
        fail: () => {
          console.log('❌ 加速计不可用');
          this.setData({ accelerometerSupported: false });
        },
        complete: () => {
          checkedCount++;
          if (checkedCount === totalSensors) resolve();
        }
      });
      
      // 检查陀螺仪
      wx.onGyroscopeChange(() => {});
      wx.startGyroscope({
        interval: 'normal',
        success: () => {
          console.log('✅ 陀螺仪可用');
          this.setData({ gyroscopeSupported: true });
          wx.stopGyroscope();
        },
        fail: () => {
          console.log('❌ 陀螺仪不可用');
          this.setData({ gyroscopeSupported: false });
        },
        complete: () => {
          checkedCount++;
          if (checkedCount === totalSensors) resolve();
        }
      });
      
      // 检查磁力计/指南针
      wx.onCompassChange(() => {});
      wx.startCompass({
        success: () => {
          console.log('✅ 指南针可用');
          this.setData({ magnetometerSupported: true });
          wx.stopCompass();
        },
        fail: () => {
          console.log('❌ 指南针不可用');
          this.setData({ magnetometerSupported: false });
        },
        complete: () => {
          checkedCount++;
          if (checkedCount === totalSensors) resolve();
        }
      });
    });
  },

  /**
   * 预热传感器
   */
  async warmupSensors() {
    return new Promise((resolve) => {
      let warmupData = {
        accelerometer: false,
        gyroscope: false,
        magnetometer: false
      };
      
      const checkComplete = () => {
        if (warmupData.accelerometer && warmupData.gyroscope && warmupData.magnetometer) {
          // 停止所有传感器
          wx.stopAccelerometer();
          wx.stopGyroscope(); 
          wx.stopCompass();
          resolve();
        }
      };
      
      // 预热加速计
      if (this.data.accelerometerSupported) {
        wx.onAccelerometerChange((data) => {
          if (!warmupData.accelerometer && data && typeof data.x === 'number') {
            console.log('🔥 加速计预热完成', data);
            warmupData.accelerometer = true;
            checkComplete();
          }
        });
        wx.startAccelerometer({ interval: 'game' });
      } else {
        warmupData.accelerometer = true;
      }
      
      // 预热陀螺仪
      if (this.data.gyroscopeSupported) {
        wx.onGyroscopeChange((data) => {
          if (!warmupData.gyroscope && data && typeof data.x === 'number') {
            console.log('🔥 陀螺仪预热完成', data);
            warmupData.gyroscope = true;
            checkComplete();
          }
        });
        wx.startGyroscope({ interval: 'game' });
      } else {
        warmupData.gyroscope = true;
      }
      
      // 预热磁力计
      if (this.data.magnetometerSupported) {
        wx.onCompassChange((data) => {
          if (!warmupData.magnetometer && data && typeof data.direction === 'number') {
            console.log('🔥 指南针预热完成', data);
            warmupData.magnetometer = true;
            checkComplete();
          }
        });
        wx.startCompass();
      } else {
        warmupData.magnetometer = true;
      }
      
      // 设置超时，避免无限等待
      setTimeout(() => {
        console.warn('⚠️ 传感器预热超时');
        resolve();
      }, 3000);
    });
  },

  /**
   * 触摸开始事件
   */
  onTouchStart(event) {
    const touches = event.touches;
    
    if (touches.length === 1) {
      // 单指拖拽
      this.setData({
        isDragging: true,
        lastTouchX: touches[0].clientX,
        lastTouchY: touches[0].clientY
      });
    } else if (touches.length === 2) {
      // 双指缩放
      const distance = this.calculateDistance(touches[0], touches[1]);
      this.setData({
        isDragging: false,
        lastTouchDistance: distance
      });
    }
  },

  /**
   * 触摸移动事件
   */
  onTouchMove(event) {
    const touches = event.touches;
    
    if (touches.length === 1 && this.data.isDragging) {
      // 单指拖拽
      const deltaX = touches[0].clientX - this.data.lastTouchX;
      const deltaY = touches[0].clientY - this.data.lastTouchY;
      
      this.setData({
        offsetX: this.data.offsetX + deltaX * 0.1, // 缩放拖拽灵敏度
        offsetY: this.data.offsetY + deltaY * 0.1,
        lastTouchX: touches[0].clientX,
        lastTouchY: touches[0].clientY
      });
    } else if (touches.length === 2 && this.data.lastTouchDistance > 0) {
      // 双指缩放
      const distance = this.calculateDistance(touches[0], touches[1]);
      const scale = distance / this.data.lastTouchDistance;
      const newZoomLevel = Math.max(0.5, Math.min(20, this.data.zoomLevel * scale));
      
      this.setData({
        zoomLevel: newZoomLevel,
        lastTouchDistance: distance
      });
    }
  },

  /**
   * 触摸结束事件
   */
  onTouchEnd(event) {
    this.setData({
      isDragging: false,
      lastTouchDistance: 0
    });
  },

  /**
   * 计算两点间距离
   */
  calculateDistance(touch1, touch2) {
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    return Math.sqrt(dx * dx + dy * dy);
  },

  /**
   * 重置轨迹视图
   */
  resetTrajectoryView() {
    console.log('🔄 重置轨迹视图');
    this.setData({
      zoomLevel: 1.0,
      offsetX: 0,
      offsetY: 0
    });
    wx.showToast({
      title: '视图已重置',
      icon: 'success',
      duration: 1000
    });
  },

  /**
   * 画布点击事件（保留用于双击检测）
   */
  onCanvasTap(event) {
    console.log('🖱️ 轨迹区域点击:', event.detail);
  }
});