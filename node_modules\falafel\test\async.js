var falafel = require('../');
var test = require('tape');

test('async', function (t) {
    t.plan(5);
    
    var src = '(function () {'
        + 'var xs = [ 1, 2, [ 3, 4 ] ];'
        + 'var ys = [ 5, 6 ];'
        + 'g([ xs, ys ]);'
    + '})()';
    
    var pending = 0;
    var output = falafel(src, function (node) {
        if (node.type === 'ArrayExpression') {
            pending ++;
            setTimeout(function () {
                node.update('fn(' + node.source() + ')');
                if (--pending === 0) check();
            }, 50 * pending * 2);
        }
    });
    
    var arrays = [
        [ 3, 4 ],
        [ 1, 2, [ 3, 4 ] ],
        [ 5, 6 ],
        [ [ 1, 2, [ 3, 4 ] ], [ 5, 6 ] ],
    ];
    
    function check () {
        Function([ 'fn', 'g' ], output)(
            function (xs) {
                t.same(arrays.shift(), xs);
                return xs;
            },
            function (xs) {
                t.same(xs, [ [ 1, 2, [ 3, 4 ] ], [ 5, 6 ] ]);
            }
        );
    }
});
