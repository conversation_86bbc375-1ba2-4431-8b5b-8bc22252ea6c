#!/usr/bin/env node

import { LibraryTester } from './test/simple-test.js';

/**
 * 高精度模式专项测试
 */
async function testPreciseMode() {
    console.log('🎯 开始高精度模式专项测试...\n');
    
    const tester = new LibraryTester();
    
    // 测试高精度模式初始化
    console.log('🚀 测试高精度模式初始化...');
    try {
        // 创建高精度模式实例
        const preciseNav = new tester.WXInertialNavigation({
            initialPosition: { x: 0, y: 0, z: 0 },
            mode: 'precise',
            sampleRate: 100,
            enableMLA: true
        });
        
        console.log('✅ 高精度模式初始化成功');
        
        // 设置模拟的MLA节点（在真实环境中应该从服务器获取）
        const mockMlaNodes = [
            { x: 5, y: 5, z: 0, confidence: 0.9, type: 'magnetic' },
            { x: 10, y: 10, z: 0, confidence: 0.8, type: 'magnetic' },
            { x: 15, y: 15, z: 0, confidence: 0.7, type: 'magnetic' }
        ];
        
        // 在模拟环境中，MLA节点功能可能不可用
        console.log('ℹ️  模拟环境中的MLA节点功能需要真实传感器数据');
        
        // 测试启动
        console.log('🚀 启动高精度模式...');
        const startResult = await preciseNav.start();
        console.log('✅ 高精度模式启动:', startResult ? '成功' : '失败');
        
        // 在模拟环境中，需要手动触发一些数据处理来模拟传感器数据
        console.log('📡 模拟传感器数据处理...');
        
        // 测试位置获取
        console.log('📍 获取当前位置...');
        const location = preciseNav.getCurrentLocation();
        console.log('✅ 当前位置:', location);
        
        // 测试统计信息
        console.log('📊 获取统计信息...');
        const stats = preciseNav.getStatistics();
        console.log('✅ 统计信息:', stats);
        
        // 测试轨迹
        console.log('🛤️ 获取轨迹数据...');
        const trajectory = preciseNav.getTrajectory();
        console.log('✅ 轨迹点数:', trajectory.length);
        
        // 停止
        console.log('⏹️ 停止高精度模式...');
        preciseNav.stop();
        console.log('✅ 高精度模式已停止');
        
        return true;
        
    } catch (error) {
        console.error('❌ 高精度模式测试失败:', error.message);
        return false;
    }
}

/**
 * 运行所有高精度模式测试
 */
async function runPreciseModeTests() {
    console.log('='.repeat(60));
    console.log('          高精度模式专项测试');
    console.log('='.repeat(60));
    
    const results = [];
    
    // 测试1: 基本功能
    results.push(await testPreciseMode());
    
    // 汇总结果
    console.log('\n📋 高精度模式测试汇总:');
    console.log('='.repeat(40));
    
    const passed = results.filter(r => r).length;
    const total = results.length;
    
    console.log(`总测试数: ${total}`);
    console.log(`通过: ${passed} ✅`);
    console.log(`失败: ${total - passed} ❌`);
    console.log(`通过率: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (passed === total) {
        console.log('\n🎉 高精度模式测试全部通过!');
    } else {
        console.log('\n❌ 高精度模式测试有失败项');
    }
    
    return passed === total;
}

// 如果是直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
    runPreciseModeTests().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('❌ 测试运行失败:', error);
        process.exit(1);
    });
}

export { runPreciseModeTests };