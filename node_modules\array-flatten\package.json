{"name": "array-flatten", "version": "3.0.0", "description": "Flatten nested arrays", "main": "dist/index.js", "typings": "dist/index.d.ts", "module": "dist.es2015/index.js", "sideEffects": false, "jsnext:main": "dist.es2015/index.js", "files": ["dist/", "dist.es2015/", "LICENSE"], "scripts": {"prettier": "prettier --write", "lint": "tslint \"src/**/*\" --project tsconfig.json", "format": "npm run prettier -- \"{.,src/**,benchmark/**}/*.{js,ts}\"", "build": "rimraf dist/ dist.es2015/ && tsc && tsc -P tsconfig.es2015.json", "benchmark": "node benchmark", "specs": "jest --coverage", "test": "npm run build && npm run lint && npm run specs && npm run size", "size": "size-limit", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/blakeembrey/array-flatten.git"}, "keywords": ["array", "flatten", "arguments", "depth", "fast", "for"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/blakeembrey/array-flatten/issues"}, "homepage": "https://github.com/blakeembrey/array-flatten", "size-limit": [{"path": "dist/index.js", "limit": "100 B"}], "jest": {"roots": ["<rootDir>/src/"], "transform": {"\\.tsx?$": "ts-jest"}, "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(tsx?|jsx?)$", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,json,css,md}": ["npm run prettier", "git add"]}, "publishConfig": {"access": "public"}, "devDependencies": {"@size-limit/preset-small-lib": "^2.2.1", "@types/jest": "^24.0.23", "@types/node": "^12.12.11", "benchmarked": "^2.0.0", "husky": "^3.1.0", "jest": "^24.9.0", "lint-staged": "^9.4.3", "prettier": "^1.19.1", "ts-expect": "^1.1.0", "ts-jest": "^24.1.0", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "tslint-config-standard": "^9.0.0", "typescript": "^3.7.2"}}