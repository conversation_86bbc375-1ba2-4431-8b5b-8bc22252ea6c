{"name": "falafel", "description": "transform the ast on a recursive walk", "version": "2.2.5", "repository": {"type": "git", "url": "git://github.com/substack/node-falafel.git"}, "main": "index.js", "keywords": ["ast", "burrito", "source", "syntax", "traversal", "tree"], "directories": {"example": "example", "test": "test"}, "scripts": {"coverage": "covert test/*.js", "test": "node --harmony test/bin/run.js test/*.js"}, "dependencies": {"acorn": "^7.1.1", "isarray": "^2.0.1"}, "devDependencies": {"acorn-jsx": "^5.2.0", "covert": "^1.1.0", "glob": "^6.0.4", "safe-buffer": "^5.2.0", "semver": "^6.0.0", "tape": "^4.0.0"}, "engines": {"node": ">=0.4.0"}, "license": "MIT", "author": {"email": "<EMAIL>", "name": "<PERSON>", "url": "http://substack.net"}, "testling": {"browsers": {"chrome": ["20.0"], "firefox": ["10.0", "15.0"], "iexplore": ["6.0", "7.0", "8.0", "9.0"], "opera": ["12.0"], "safari": ["5.1"]}, "files": "test/*.js"}}