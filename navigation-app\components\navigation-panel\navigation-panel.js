/**
 * 导航面板组件
 * 提供导航指引、方向箭头、距离信息等功能
 */

Component({
  properties: {
    // 当前位置
    currentPosition: {
      type: Object,
      value: { x: 0, y: 0, z: 0 }
    },
    
    // 目标位置
    targetPosition: {
      type: Object,
      value: null
    },
    
    // 当前航向角
    heading: {
      type: Number,
      value: 0
    },
    
    // 导航状态
    isNavigating: {
      type: Boolean,
      value: false
    },
    
    // 当前指令
    currentInstruction: {
      type: String,
      value: ''
    },
    
    // 剩余距离
    remainingDistance: {
      type: Number,
      value: 0
    },
    
    // 预计时间
    estimatedTime: {
      type: Number,
      value: 0
    }
  },

  data: {
    // 方向箭头角度
    arrowAngle: 0,
    
    // 显示状态
    showPanel: true,
    
    // 导航步骤
    currentStep: 0,
    totalSteps: 0
  },

  observers: {
    'currentPosition, targetPosition, heading': function(current, target, heading) {
      if (current && target) {
        this.updateNavigationInfo(current, target, heading);
      }
    }
  },

  methods: {
    /**
     * 更新导航信息
     */
    updateNavigationInfo(current, target, heading) {
      // 计算目标方向角
      const dx = target.x - current.x;
      const dy = target.y - current.y;
      const targetAngle = Math.atan2(dy, dx) * 180 / Math.PI;
      
      // 计算箭头指向角度（相对于当前朝向）
      let arrowAngle = targetAngle - heading;
      
      // 标准化角度到 -180 到 180 度
      while (arrowAngle > 180) arrowAngle -= 360;
      while (arrowAngle < -180) arrowAngle += 360;
      
      this.setData({
        arrowAngle: arrowAngle
      });
    },

    /**
     * 获取方向指示文字
     */
    getDirectionText() {
      const angle = this.data.arrowAngle;
      
      if (angle >= -22.5 && angle < 22.5) {
        return '直行';
      } else if (angle >= 22.5 && angle < 67.5) {
        return '右前方';
      } else if (angle >= 67.5 && angle < 112.5) {
        return '右转';
      } else if (angle >= 112.5 && angle < 157.5) {
        return '右后方';
      } else if (angle >= 157.5 || angle < -157.5) {
        return '掉头';
      } else if (angle >= -157.5 && angle < -112.5) {
        return '左后方';
      } else if (angle >= -112.5 && angle < -67.5) {
        return '左转';
      } else if (angle >= -67.5 && angle < -22.5) {
        return '左前方';
      }
      
      return '直行';
    },

    /**
     * 格式化距离
     */
    formatDistance(meters) {
      if (meters < 10) {
        return `${meters.toFixed(1)}米`;
      } else if (meters < 1000) {
        return `${Math.round(meters)}米`;
      } else {
        return `${(meters / 1000).toFixed(1)}公里`;
      }
    },

    /**
     * 格式化时间
     */
    formatTime(seconds) {
      if (seconds < 60) {
        return `${Math.round(seconds)}秒`;
      } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        return `${minutes}分钟`;
      } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}小时${minutes}分钟`;
      }
    },

    /**
     * 切换面板显示
     */
    togglePanel() {
      this.setData({
        showPanel: !this.data.showPanel
      });
    },

    /**
     * 停止导航
     */
    stopNavigation() {
      this.triggerEvent('stopNavigation');
    },

    /**
     * 重新规划路线
     */
    replanRoute() {
      this.triggerEvent('replanRoute');
    },

    /**
     * 语音播报
     */
    speakInstruction() {
      const instruction = this.properties.currentInstruction || this.getDirectionText();
      this.triggerEvent('speakInstruction', { text: instruction });
    }
  },

  lifetimes: {
    attached() {
      console.log('🧭 导航面板组件已挂载');
    },
    
    detached() {
      console.log('🧭 导航面板组件已卸载');
    }
  }
});