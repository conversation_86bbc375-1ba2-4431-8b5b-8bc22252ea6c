/**
 * 微信小程序惯导定位库 - 核心API
 * WeChat MiniProgram Inertial Navigation Library
 * 
 * 提供简洁易用的PDR室内定位能力
 * 输入：初始化坐标
 * 输出：实时位置坐标
 * 
 * @version 1.0.0
 * <AUTHOR> Team
 */

const InertialSensorManager = require('./core/InertialSensorManager.js');
const PositionCalculator = require('./core/PositionCalculator.js');
const QualityController = require('./core/QualityController.js');

/**
 * 微信小程序惯导定位核心类
 */
export default class WXInertialNavigation {
  
  /**
   * 构造函数
   * @param {Object} config - 配置参数
   * @param {Object} config.initialPosition - 初始位置 {x, y, z}
   * @param {number} config.sampleRate - 传感器采样率 (Hz)，默认50
   * @param {string} config.mode - 运行模式：'standard'(标准) | 'lite'(轻量) | 'precise'(精确)
   * @param {boolean} config.enableMLA - 是否启用MLA校正，默认true
   * @param {Object} config.calibration - 校准参数
   */
  constructor(config = {}) {
    // 验证配置参数
    this.validateConfig(config);
    
    // 核心配置
    this.config = {
      // 初始位置（必需）
      initialPosition: config.initialPosition,
      
      // 传感器配置
      sampleRate: config.sampleRate || 50,
      
      // 运行模式
      mode: config.mode || 'standard',
      
      // 功能开关
      enableMLA: config.enableMLA !== false,
      enableStepDetection: config.enableStepDetection !== false,
      enableHeadingCorrection: config.enableHeadingCorrection !== false,
      
      // 校准参数
      calibration: {
        stepLength: config.calibration?.stepLength || 0.75,
        magneticDeclination: config.calibration?.magneticDeclination || 0,
        accelerometerBias: config.calibration?.accelerometerBias || [0, 0, 0],
        gyroscopeBias: config.calibration?.gyroscopeBias || [0, 0, 0],
        ...config.calibration
      },
      
      // 融合参数
      fusion: {
        pdrWeight: 0.7,
        mlaWeight: 0.3,
        adaptiveWeighting: true,
        confidenceThreshold: 0.4,
        smoothingFactor: 0.4,
        ...config.fusion
      },
      
      // 质量控制
      quality: {
        outlierThreshold: 3.0,
        minConfidence: 0.2,
        maxDeviation: 5.0,
        ...config.quality
      }
    };
    
    // 根据模式调整参数
    this.adjustConfigByMode();
    
    // 初始化核心组件
    this.sensorManager = new InertialSensorManager(this.config);
    this.positionCalculator = new PositionCalculator(this.config);
    this.qualityController = new QualityController(this.config);
    
    // 系统状态
    this.state = {
      isRunning: false,
      isPaused: false,
      isCalibrated: false,
      lastUpdate: 0,
      operatingMode: this.config.mode
    };
    
    // 当前定位结果
    this.currentLocation = {
      position: { ...this.config.initialPosition },
      heading: 0,
      velocity: 0,
      stepCount: 0,
      confidence: 0,
      timestamp: Date.now(),
      quality: 'unknown'
    };
    
    // 历史轨迹 - 使用循环缓冲区防止内存泄漏
    this.trajectory = [];
    this.maxTrajectoryLength = 1000;
    this.trajectoryBuffer = new TrajectoryBuffer(this.maxTrajectoryLength);
    
    // 性能统计
    this.statistics = {
      totalSteps: 0,
      totalDistance: 0,
      averageConfidence: 0,
      processingTime: [],
      correctionCount: 0,
      startTime: null
    };
    
    // 回调函数
    this.callbacks = {
      onLocationUpdate: null,
      onStepDetected: null,
      onError: null,
      onCalibrationRequired: null
    };
    
    console.log('🧭 WX惯导定位库初始化完成', {
      mode: this.config.mode,
      initialPosition: this.config.initialPosition,
      enableMLA: this.config.enableMLA
    });
  }
  
  /**
   * 验证配置参数
   * @param {Object} config 
   */
  validateConfig(config) {
    // 检查配置对象是否存在
    if (!config || typeof config !== 'object') {
      throw new Error('配置参数必须是一个对象');
    }
    
    // 检查初始位置
    if (!config.initialPosition) {
      throw new Error('必须提供初始位置坐标 (initialPosition)');
    }
    
    const { x, y, z } = config.initialPosition;
    
    // 检查坐标是否为数字类型
    if (typeof x !== 'number' || typeof y !== 'number' || typeof z !== 'number') {
      throw new Error('初始位置坐标必须为数字类型');
    }
    
    // 检查坐标是否为有效数字（非NaN和有限数）
    if (isNaN(x) || isNaN(y) || isNaN(z) || 
        !isFinite(x) || !isFinite(y) || !isFinite(z)) {
      throw new Error('初始位置坐标必须是有效的数字');
    }
    
    // 检查采样率范围
    if (config.sampleRate && (config.sampleRate < 10 || config.sampleRate > 100)) {
      console.warn('⚠️ 建议采样率设置在10-100Hz之间');
    }
    
    // 检查运行模式是否有效
    if (config.mode && !['lite', 'standard', 'precise'].includes(config.mode)) {
      throw new Error('运行模式必须是 lite、standard 或 precise');
    }
  }
  
  /**
   * 根据运行模式调整配置参数
   */
  adjustConfigByMode() {
    const mode = this.config.mode;
    
    switch (mode) {
      case 'lite':
        // 轻量模式：低功耗，牺牲部分精度
        this.config.sampleRate = Math.min(this.config.sampleRate, 30);
        this.config.enableMLA = false;
        this.config.fusion.smoothingFactor = 0.6;
        this.config.quality.minConfidence = 0.1;
        break;
        
      case 'precise':
        // 精确模式：高精度，增加计算量
        this.config.sampleRate = Math.max(this.config.sampleRate, 50);
        this.config.enableMLA = true;
        this.config.fusion.smoothingFactor = 0.3;
        this.config.quality.minConfidence = 0.3;
        this.config.fusion.adaptiveWeighting = true;
        break;
        
      case 'standard':
      default:
        // 标准模式：平衡精度和性能
        // 使用默认配置
        break;
    }
  }
  
  /**
   * 启动惯导定位
   * @param {Object} options - 启动选项
   * @returns {Promise<boolean>} 启动是否成功
   */
  async start(options = {}) {
    if (this.state.isRunning) {
      console.warn('⚠️ 惯导定位已在运行中');
      return true;
    }
    
    try {
      console.log('🚀 启动惯导定位系统...');
      
      // 合并启动选项
      const startConfig = { ...this.config, ...options };
      
      // 启动传感器管理器
      await this.sensorManager.start(startConfig);
      
      // 初始化位置计算器
      this.positionCalculator.initialize(startConfig.initialPosition);
      
      // 初始化质量控制器
      this.qualityController.initialize();
      
      // 设置数据处理回调
      this.sensorManager.setDataCallback((sensorData) => {
        this.processSensorData(sensorData);
      });
      
      // 更新状态
      this.state.isRunning = true;
      this.state.isPaused = false;
      this.statistics.startTime = Date.now();
      
      console.log('✅ 惯导定位系统启动成功');
      return true;
      
    } catch (error) {
      console.error('❌ 惯导定位启动失败:', error);
      this.handleError('start_failed', error);
      return false;
    }
  }
  
  /**
   * 停止惯导定位
   */
  stop() {
    if (!this.state.isRunning) {
      return;
    }
    
    console.log('⏹️ 停止惯导定位系统');
    
    // 停止传感器采集
    this.sensorManager.stop();
    
    // 重置状态
    this.state.isRunning = false;
    this.state.isPaused = false;
    
    // 输出最终统计
    this.logFinalStatistics();
  }
  
  /**
   * 暂停/恢复定位
   * @param {boolean} pause - true暂停，false恢复
   */
  pause(pause = true) {
    if (!this.state.isRunning) {
      console.warn('⚠️ 系统未运行，无法暂停');
      return;
    }
    
    this.state.isPaused = pause;
    
    if (pause) {
      console.log('⏸️ 暂停惯导定位');
      this.sensorManager.pause();
    } else {
      console.log('▶️ 恢复惯导定位');
      this.sensorManager.resume();
    }
  }
  
  /**
   * 重置到指定位置
   * @param {Object} position - 新的位置坐标 {x, y, z}
   */
  reset(position = null) {
    console.log('🔄 重置惯导定位系统');
    
    // 重置位置
    const newPosition = position || this.config.initialPosition;
    this.currentLocation.position = { ...newPosition };
    this.currentLocation.heading = 0;
    this.currentLocation.velocity = 0;
    this.currentLocation.stepCount = 0;
    this.currentLocation.timestamp = Date.now();
    
    // 清空轨迹
    this.trajectory = [];
    
    // 重置统计
    this.statistics.totalSteps = 0;
    this.statistics.totalDistance = 0;
    this.statistics.correctionCount = 0;
    this.statistics.processingTime = [];
    
    // 重置计算器
    this.positionCalculator.reset(newPosition);
    
    console.log('✅ 重置完成，新位置:', newPosition);
  }
  
  /**
   * 处理传感器数据的核心方法
   * @param {Object} sensorData - 传感器数据
   */
  processSensorData(sensorData) {
    if (this.state.isPaused || !this.state.isRunning) {
      return;
    }
    
    const startTime = Date.now();
    console.log('🔄 处理传感器数据:', {
      timestamp: sensorData.timestamp,
      hasAccelerometer: !!sensorData.accelerometer,
      hasGyroscope: !!sensorData.gyroscope,
      hasMagnetometer: !!sensorData.magnetometer
    });
    
    try {
      // 数据质量检查
      const qualityResult = this.qualityController.checkDataQuality(sensorData);
      console.log('🔍 数据质量检查结果:', qualityResult);
      
      if (!qualityResult.isValid) {
        console.warn('⚠️ 传感器数据质量不佳:', qualityResult.reason);
        return;
      }
      
      // 位置计算
      console.log('📍 开始位置计算...');
      const positionResult = this.positionCalculator.update(sensorData);
      console.log('📍 位置计算结果:', positionResult);
      
      if (!positionResult) {
        console.warn('⚠️ 位置计算器返回空结果');
        return;
      }
      
      // 更新当前位置
      this.updateCurrentLocation(positionResult);
      
      // 记录轨迹
      this.recordTrajectory();
      
      // 更新统计信息
      this.updateStatistics(positionResult, Date.now() - startTime);
      
      // 触发位置更新回调
      if (this.callbacks.onLocationUpdate) {
        const currentLocation = this.getCurrentLocation();
        console.log('📤 准备发送位置更新回调:', currentLocation);
        
        // 确保位置数据有效再调用回调
        if (currentLocation && currentLocation.position && 
            currentLocation.position.x !== null && currentLocation.position.x !== undefined) {
          this.callbacks.onLocationUpdate(currentLocation);
        } else {
          console.warn('⚠️ 位置数据无效，跳过回调:', currentLocation);
        }
      } else {
        console.warn('⚠️ 位置更新回调未设置');
      }
      
    } catch (error) {
      console.error('❌ 传感器数据处理失败:', error);
      this.handleError('processing_failed', error);
    }
  }
  
  /**
   * 更新当前位置信息
   * @param {Object} positionResult - 位置计算结果
   */
  updateCurrentLocation(positionResult) {
    this.currentLocation = {
      position: positionResult.position,
      heading: positionResult.heading,
      velocity: positionResult.velocity,
      stepCount: positionResult.stepCount,
      confidence: positionResult.confidence,
      timestamp: Date.now(),
      quality: this.qualityController.getQualityLevel()
    };
    
    this.state.lastUpdate = Date.now();
  }
  
  /**
   * 记录轨迹点
   */
  recordTrajectory() {
    const trajectoryPoint = {
      ...this.currentLocation.position,
      heading: this.currentLocation.heading,
      timestamp: this.currentLocation.timestamp,
      confidence: this.currentLocation.confidence
    };
    
    // 使用循环缓冲区记录轨迹
    this.trajectoryBuffer.add(trajectoryPoint);
    
    // 保持向后兼容性
    this.trajectory.push(trajectoryPoint);
    
    // 限制轨迹长度 - 使用高效的内存管理
    if (this.trajectory.length > this.maxTrajectoryLength) {
      this.trajectory.shift();
    }
    
    // 定期进行内存清理
    this.performMemoryMaintenance();
  }
  
  /**
   * 更新性能统计
   * @param {Object} positionResult - 位置结果
   * @param {number} processingTime - 处理时间(ms)
   */
  updateStatistics(positionResult, processingTime) {
    this.statistics.processingTime.push(processingTime);
    
    // 保持处理时间数组大小
    if (this.statistics.processingTime.length > 100) {
      this.statistics.processingTime.shift();
    }
    
    // 更新步数
    if (positionResult.stepDetected) {
      this.statistics.totalSteps++;
      
      // 步数检测回调
      if (this.callbacks.onStepDetected) {
        this.callbacks.onStepDetected({
          stepCount: this.statistics.totalSteps,
          stepLength: positionResult.stepLength,
          timestamp: Date.now()
        });
      }
    }
    
    // 更新距离
    this.statistics.totalDistance = this.calculateTotalDistance();
    
    // 更新平均置信度
    this.statistics.averageConfidence = this.calculateAverageConfidence();
    
    // 更新校正次数
    if (positionResult.corrected) {
      this.statistics.correctionCount++;
    }
  }
  
  /**
   * 计算总距离
   * @returns {number} 总距离(米)
   */
  calculateTotalDistance() {
    if (this.trajectory.length < 2) return 0;
    
    let totalDistance = 0;
    for (let i = 1; i < this.trajectory.length; i++) {
      const prev = this.trajectory[i - 1];
      const curr = this.trajectory[i];
      const distance = Math.sqrt(
        Math.pow(curr.x - prev.x, 2) + 
        Math.pow(curr.y - prev.y, 2) + 
        Math.pow(curr.z - prev.z, 2)
      );
      totalDistance += distance;
    }
    return totalDistance;
  }
  
  /**
   * 计算平均置信度
   * @returns {number} 平均置信度
   */
  calculateAverageConfidence() {
    if (this.trajectory.length === 0) return 0;
    
    const recentTrajectory = this.trajectory.slice(-50); // 最近50个点
    const totalConfidence = recentTrajectory.reduce((sum, point) => sum + (point.confidence || 0), 0);
    return totalConfidence / recentTrajectory.length;
  }
  
  /**
   * 错误处理
   * @param {string} type - 错误类型
   * @param {Error} error - 错误对象
   */
  handleError(type, error) {
    const errorInfo = {
      type,
      message: error.message,
      timestamp: Date.now(),
      state: { ...this.state }
    };
    
    if (this.callbacks.onError) {
      this.callbacks.onError(errorInfo);
    }
    
    // 根据错误类型处理
    switch (type) {
      case 'sensor_error':
        // 传感器错误，尝试恢复
        this.recoverFromSensorError();
        break;
      case 'calibration_required':
        // 需要校准
        if (this.callbacks.onCalibrationRequired) {
          this.callbacks.onCalibrationRequired();
        }
        break;
      default:
        console.error('未处理的错误类型:', type, error);
    }
  }
  
  /**
   * 从传感器错误中恢复
   */
  recoverFromSensorError() {
    console.log('🔧 尝试从传感器错误中恢复...');
    
    // 重启传感器管理器
    setTimeout(() => {
      if (this.state.isRunning) {
        this.sensorManager.restart();
      }
    }, 1000);
  }
  
  /**
   * 输出最终统计信息
   */
  logFinalStatistics() {
    const runtime = this.statistics.startTime ? Date.now() - this.statistics.startTime : 0;
    const avgProcessingTime = this.statistics.processingTime.length > 0 
      ? this.statistics.processingTime.reduce((a, b) => a + b, 0) / this.statistics.processingTime.length 
      : 0;
    
    console.log('📊 惯导定位会话统计:', {
      运行时间: `${(runtime / 1000).toFixed(1)}秒`,
      总步数: this.statistics.totalSteps,
      总距离: `${this.statistics.totalDistance.toFixed(2)}米`,
      平均置信度: `${(this.statistics.averageConfidence * 100).toFixed(1)}%`,
      校正次数: this.statistics.correctionCount,
      平均处理时间: `${avgProcessingTime.toFixed(1)}ms`
    });
  }
  
  // ==================== 内存管理方法 ====================
  
  /**
   * 执行内存维护
   */
  performMemoryMaintenance() {
    // 每100次轨迹记录执行一次内存清理
    if (this.trajectory.length % 100 === 0) {
      this.cleanupMemory();
    }
  }
  
  /**
   * 清理内存
   */
  cleanupMemory() {
    // 清理过长的处理时间数组
    if (this.statistics.processingTime.length > 50) {
      this.statistics.processingTime = this.statistics.processingTime.slice(-50);
    }
    
    // 清理传感器缓存
    if (this.sensorManager) {
      this.sensorManager.cleanupBuffers();
    }
    
    // 清理位置历史
    if (this.positionCalculator) {
      this.positionCalculator.cleanupHistory();
    }
    
    console.log('🧹 内存清理完成');
  }
  
  /**
   * 获取内存使用情况
   * @returns {Object} 内存统计信息
   */
  getMemoryUsage() {
    const trajectorySize = this.trajectory.length;
    const trajectoryMemory = trajectorySize * 80; // 估算每个轨迹点80字节
    
    const processingTimeSize = this.statistics.processingTime.length;
    const processingTimeMemory = processingTimeSize * 8; // 每个时间值8字节
    
    // 获取传感器缓存大小
    let sensorBufferMemory = 0;
    if (this.sensorManager) {
      const bufferSizes = this.sensorManager.getStatus().bufferSizes;
      sensorBufferMemory = (bufferSizes.accelerometer + bufferSizes.gyroscope + bufferSizes.magnetometer) * 32;
    }
    
    const totalEstimatedMemory = trajectoryMemory + processingTimeMemory + sensorBufferMemory;
    
    return {
      trajectoryPoints: trajectorySize,
      trajectoryMemory: trajectoryMemory,
      processingTimeEntries: processingTimeSize,
      processingTimeMemory: processingTimeMemory,
      sensorBufferMemory: sensorBufferMemory,
      totalEstimatedMemory: totalEstimatedMemory,
      memoryStatus: totalEstimatedMemory > 1024 * 1024 ? 'high' : totalEstimatedMemory > 512 * 1024 ? 'medium' : 'low'
    };
  }
  
  /**
   * 强制内存压缩
   */
  compressMemory() {
    console.log('🗜️ 开始内存压缩...');
    
    // 压缩轨迹数据 - 保留关键点
    this.compressTrajectory();
    
    // 清空处理时间统计
    this.statistics.processingTime = [];
    
    // 重置传感器缓存
    if (this.sensorManager) {
      this.sensorManager.clearBuffers();
    }
    
    // 触发垃圾回收（如果支持）
    if (global.gc) {
      global.gc();
    }
    
    console.log('✅ 内存压缩完成');
  }
  
  /**
   * 压缩轨迹数据
   */
  compressTrajectory() {
    if (this.trajectory.length <= this.maxTrajectoryLength / 2) {
      return; // 不需要压缩
    }
    
    // 使用Douglas-Peucker简化算法压缩轨迹
    const compressed = this.simplifyTrajectory(this.trajectory, 0.5);
    
    console.log(`📉 轨迹压缩: ${this.trajectory.length} -> ${compressed.length}`);
    
    this.trajectory = compressed;
  }
  
  /**
   * 轨迹简化算法（Douglas-Peucker）
   * @param {Array} points - 轨迹点数组
   * @param {number} tolerance - 简化容差
   * @returns {Array} 简化后的轨迹
   */
  simplifyTrajectory(points, tolerance = 0.5) {
    if (points.length <= 2) {
      return points;
    }
    
    // 找到距离起点和终点连线最远的点
    let maxDistance = 0;
    let maxIndex = 0;
    const start = points[0];
    const end = points[points.length - 1];
    
    for (let i = 1; i < points.length - 1; i++) {
      const distance = this.pointToLineDistance(points[i], start, end);
      if (distance > maxDistance) {
        maxDistance = distance;
        maxIndex = i;
      }
    }
    
    // 如果最大距离小于容差，则直接连接起点和终点
    if (maxDistance < tolerance) {
      return [start, end];
    }
    
    // 递归简化两段
    const leftSegment = this.simplifyTrajectory(points.slice(0, maxIndex + 1), tolerance);
    const rightSegment = this.simplifyTrajectory(points.slice(maxIndex), tolerance);
    
    // 合并结果（避免重复中间点）
    return leftSegment.slice(0, -1).concat(rightSegment);
  }
  
  /**
   * 计算点到直线的距离
   * @param {Object} point - 点
   * @param {Object} lineStart - 直线起点
   * @param {Object} lineEnd - 直线终点
   * @returns {number} 距离
   */
  pointToLineDistance(point, lineStart, lineEnd) {
    const A = point.x - lineStart.x;
    const B = point.y - lineStart.y;
    const C = lineEnd.x - lineStart.x;
    const D = lineEnd.y - lineStart.y;
    
    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    
    if (lenSq === 0) {
      return Math.sqrt(A * A + B * B);
    }
    
    const param = dot / lenSq;
    
    let xx, yy;
    if (param < 0) {
      xx = lineStart.x;
      yy = lineStart.y;
    } else if (param > 1) {
      xx = lineEnd.x;
      yy = lineEnd.y;
    } else {
      xx = lineStart.x + param * C;
      yy = lineStart.y + param * D;
    }
    
    const dx = point.x - xx;
    const dy = point.y - yy;
    return Math.sqrt(dx * dx + dy * dy);
  }

  // ==================== 公共API方法 ====================
  
  /**
   * 获取当前位置
   * @returns {Object} 当前位置信息
   */
  getCurrentLocation() {
    return {
      ...this.currentLocation,
      // 添加额外的状态信息
      isMoving: this.currentLocation.velocity > 0.1,
      qualityLevel: this.qualityController.getQualityLevel(),
      systemStatus: this.getSystemStatus()
    };
  }
  
  /**
   * 获取历史轨迹
   * @param {number} limit - 返回的轨迹点数量限制
   * @returns {Array} 轨迹点数组
   */
  getTrajectory(limit = null) {
    if (limit && limit > 0) {
      return this.trajectory.slice(-limit);
    }
    return [...this.trajectory];
  }
  
  /**
   * 获取性能统计
   * @returns {Object} 性能统计信息
   */
  getStatistics() {
    const runtime = this.statistics.startTime ? Date.now() - this.statistics.startTime : 0;
    const avgProcessingTime = this.statistics.processingTime.length > 0 
      ? this.statistics.processingTime.reduce((a, b) => a + b, 0) / this.statistics.processingTime.length 
      : 0;
    
    return {
      runtime: runtime,
      totalSteps: this.statistics.totalSteps,
      totalDistance: this.statistics.totalDistance,
      averageConfidence: this.statistics.averageConfidence,
      correctionCount: this.statistics.correctionCount,
      averageProcessingTime: avgProcessingTime,
      trajectoryLength: this.trajectory.length,
      isRealtime: this.state.isRunning && !this.state.isPaused
    };
  }
  
  /**
   * 获取系统状态
   * @returns {Object} 系统状态信息
   */
  getSystemStatus() {
    return {
      isRunning: this.state.isRunning,
      isPaused: this.state.isPaused,
      isCalibrated: this.state.isCalibrated,
      operatingMode: this.state.operatingMode,
      lastUpdate: this.state.lastUpdate,
      sensorStatus: this.sensorManager.getStatus(),
      qualityLevel: this.qualityController.getQualityLevel()
    };
  }
  
  /**
   * 设置回调函数
   * @param {Object} callbacks - 回调函数对象
   * @param {Function} callbacks.onLocationUpdate - 位置更新回调
   * @param {Function} callbacks.onStepDetected - 步数检测回调
   * @param {Function} callbacks.onError - 错误回调
   * @param {Function} callbacks.onCalibrationRequired - 需要校准回调
   */
  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }
  
  /**
   * 更新配置参数
   * @param {Object} config - 新的配置参数
   */
  updateConfig(config) {
    // 合并配置
    this.config = { ...this.config, ...config };
    
    // 根据模式重新调整
    if (config.mode) {
      this.adjustConfigByMode();
    }
    
    // 更新子模块配置
    this.sensorManager.updateConfig(this.config);
    this.positionCalculator.updateConfig(this.config);
    this.qualityController.updateConfig(this.config);
    
    console.log('⚙️ 配置已更新:', config);
  }
  
  /**
   * 校准传感器
   * @param {Object} calibrationData - 校准数据
   * @returns {Promise<boolean>} 校准是否成功
   */
  async calibrate(calibrationData = {}) {
    console.log('🔧 开始传感器校准...');
    
    try {
      // 执行校准
      const result = await this.sensorManager.calibrate(calibrationData);
      
      if (result.success) {
        this.state.isCalibrated = true;
        this.config.calibration = { ...this.config.calibration, ...result.parameters };
        console.log('✅ 传感器校准成功');
        return true;
      } else {
        console.warn('⚠️ 传感器校准失败:', result.error);
        return false;
      }
      
    } catch (error) {
      console.error('❌ 传感器校准异常:', error);
      this.handleError('calibration_failed', error);
      return false;
    }
  }
  
  /**
   * 导出数据
   * @param {string} format - 导出格式: 'json' | 'csv'
   * @returns {string} 导出的数据
   */
  exportData(format = 'json') {
    const exportData = {
      metadata: {
        version: '1.0.0',
        exportTime: new Date().toISOString(),
        config: this.config,
        statistics: this.getStatistics()
      },
      trajectory: this.trajectory,
      currentLocation: this.currentLocation
    };
    
    switch (format.toLowerCase()) {
      case 'json':
        return JSON.stringify(exportData, null, 2);
      
      case 'csv':
        return this.convertToCSV(this.trajectory);
      
      default:
        throw new Error('不支持的导出格式: ' + format);
    }
  }
  
  /**
   * 将轨迹转换为CSV格式
   * @param {Array} trajectory - 轨迹数据
   * @returns {string} CSV格式字符串
   */
  convertToCSV(trajectory) {
    const headers = ['timestamp', 'x', 'y', 'z', 'heading', 'confidence'];
    const csvLines = [headers.join(',')];
    
    trajectory.forEach(point => {
      const row = [
        point.timestamp,
        point.x.toFixed(6),
        point.y.toFixed(6),
        point.z.toFixed(6),
        point.heading.toFixed(2),
        point.confidence.toFixed(3)
      ];
      csvLines.push(row.join(','));
    });
    
    return csvLines.join('\n');
  }
}

/**
 * 高效的轨迹缓冲区实现（循环缓冲区）
 */
class TrajectoryBuffer {
  constructor(maxSize) {
    this.maxSize = maxSize;
    this.buffer = new Array(maxSize);
    this.head = 0;
    this.tail = 0;
    this.count = 0;
  }
  
  /**
   * 添加轨迹点
   * @param {Object} point - 轨迹点
   */
  add(point) {
    this.buffer[this.tail] = point;
    this.tail = (this.tail + 1) % this.maxSize;
    
    if (this.count < this.maxSize) {
      this.count++;
    } else {
      // 缓冲区满时，移动头指针
      this.head = (this.head + 1) % this.maxSize;
    }
  }
  
  /**
   * 获取所有轨迹点
   * @returns {Array} 轨迹点数组
   */
  getAll() {
    const result = [];
    let current = this.head;
    
    for (let i = 0; i < this.count; i++) {
      result.push(this.buffer[current]);
      current = (current + 1) % this.maxSize;
    }
    
    return result;
  }
  
  /**
   * 获取最近的N个轨迹点
   * @param {number} n - 数量
   * @returns {Array} 轨迹点数组
   */
  getLast(n) {
    const count = Math.min(n, this.count);
    const result = [];
    let current = (this.tail - count + this.maxSize) % this.maxSize;
    
    for (let i = 0; i < count; i++) {
      result.push(this.buffer[current]);
      current = (current + 1) % this.maxSize;
    }
    
    return result;
  }
  
  /**
   * 清空缓冲区
   */
  clear() {
    this.head = 0;
    this.tail = 0;
    this.count = 0;
  }
  
  /**
   * 获取当前大小
   * @returns {number} 当前元素数量
   */
  size() {
    return this.count;
  }
  
  /**
   * 检查是否已满
   * @returns {boolean} 是否已满
   */
  isFull() {
    return this.count === this.maxSize;
  }
}

// 导出库版本信息
const version = '1.0.0';
const description = '微信小程序惯导定位库';

// CommonJS导出
module.exports = WXInertialNavigation;
module.exports.version = version;
module.exports.description = description;