/**
 * 室内地图可视化组件
 * 用于显示用户位置、路径规划和导航指引
 */

const app = getApp();

Component({
  properties: {
    // 地图尺寸
    width: {
      type: String,
      value: '100%'
    },
    height: {
      type: String,
      value: '400'
    },
    
    // 当前楼层
    currentFloor: {
      type: Number,
      value: 1
    },
    
    // 是否显示用户位置
    showUserPosition: {
      type: Boolean,
      value: true
    },
    
    // 是否显示导航路径
    showNavigationRoute: {
      type: Boolean,
      value: false
    },
    
    // 地图模式
    mode: {
      type: String,
      value: 'navigation' // navigation, explore, edit
    },
    
    // 缩放级别
    zoomLevel: {
      type: Number,
      value: 1.0
    }
  },

  data: {
    // Canvas上下文
    canvasContext: null,
    canvasId: 'indoor-map-canvas',
    
    // 地图数据
    mapData: {
      bounds: { x: 0, y: 0, width: 100, height: 80 },
      walls: [],
      rooms: [],
      pois: [],
      exits: []
    },
    
    // 用户状态
    userPosition: { x: 0, y: 0, z: 1 },
    userHeading: 0,
    
    // 导航数据
    navigationRoute: [],
    currentTarget: null,
    
    // 视图状态
    viewCenter: { x: 50, y: 40 },
    scale: 8, // 像素/米比例
    canvasWidth: 300,
    canvasHeight: 200,
    
    // 交互状态
    isDragging: false,
    lastTouchPos: { x: 0, y: 0 },
    
    // 动画状态
    animationFrame: null,
    needsRedraw: true
  },

  lifetimes: {
    attached() {
      console.log('🗺️ 室内地图组件加载');
      this.initMap();
    },
    
    detached() {
      this.cleanup();
    }
  },

  observers: {
    'currentFloor': function(newFloor) {
      this.loadFloorData(newFloor);
    },
    
    'zoomLevel': function(newZoom) {
      this.updateScale(newZoom);
    }
  },

  methods: {
    /**
     * 初始化地图
     */
    initMap() {
      // 创建Canvas上下文
      this.createCanvasContext();
      
      // 加载地图数据
      this.loadMapData();
      
      // 启动渲染循环
      this.startRenderLoop();
      
      // 设置canvas尺寸
      this.updateCanvasSize();
    },

    /**
     * 创建Canvas上下文
     */
    createCanvasContext() {
      const canvasId = `indoor-map-${Date.now()}`;
      this.setData({ canvasId });
      
      wx.createSelectorQuery()
        .in(this)
        .select(`#${canvasId}`)
        .node((res) => {
          if (res && res.node) {
            const canvas = res.node;
            const ctx = canvas.getContext('2d');
            
            // 设置canvas尺寸
            const dpr = wx.getSystemInfoSync().pixelRatio;
            canvas.width = this.data.canvasWidth * dpr;
            canvas.height = this.data.canvasHeight * dpr;
            ctx.scale(dpr, dpr);
            
            this.setData({ 
              canvasContext: ctx,
              canvas: canvas
            });
            
            // 首次绘制
            this.redraw();
          }
        })
        .exec();
    },

    /**
     * 加载地图数据
     */
    loadMapData() {
      // 从app获取地图数据
      const appMapData = app.globalData.mapData;
      
      // 构建地图数据
      const mapData = {
        bounds: { x: 0, y: 0, width: 100, height: 80 },
        
        // 墙体数据（简化的矩形建筑）
        walls: [
          { x1: 0, y1: 0, x2: 100, y2: 0 },     // 上墙
          { x1: 100, y1: 0, x2: 100, y2: 80 },  // 右墙
          { x1: 100, y1: 80, x2: 0, y2: 80 },   // 下墙
          { x1: 0, y1: 80, x2: 0, y2: 0 },      // 左墙
          
          // 内部分隔
          { x1: 30, y1: 20, x2: 70, y2: 20 },   // 横向分隔
          { x1: 50, y1: 20, x2: 50, y2: 60 },   // 纵向分隔
        ],
        
        // 房间区域
        rooms: [
          { name: '大厅', x: 5, y: 5, width: 90, height: 15, color: '#f0f8ff' },
          { name: '会议室A', x: 5, y: 25, width: 40, height: 30, color: '#fff8dc' },
          { name: '会议室B', x: 55, y: 25, width: 40, height: 30, color: '#f0fff0' },
          { name: '走廊', x: 5, y: 65, width: 90, height: 10, color: '#f5f5f5' }
        ],
        
        // 兴趣点
        pois: appMapData.pois.filter(poi => poi.floor === this.data.currentFloor),
        
        // 出入口
        exits: [
          { x: 50, y: 0, width: 6, type: 'entrance', name: '主入口' },
          { x: 0, y: 40, width: 6, type: 'exit', name: '安全出口' }
        ]
      };
      
      this.setData({ mapData });
      this.setData({ needsRedraw: true });
    },

    /**
     * 加载楼层数据
     */
    loadFloorData(floor) {
      console.log('📍 切换到楼层:', floor);
      this.loadMapData();
    },

    /**
     * 更新缩放比例
     */
    updateScale(zoomLevel) {
      const baseScale = 8;
      const newScale = baseScale * zoomLevel;
      this.setData({ 
        scale: newScale,
        needsRedraw: true 
      });
    },

    /**
     * 更新canvas尺寸
     */
    updateCanvasSize() {
      const query = wx.createSelectorQuery().in(this);
      query.select('.map-container').boundingClientRect((rect) => {
        if (rect) {
          this.setData({
            canvasWidth: rect.width,
            canvasHeight: rect.height,
            needsRedraw: true
          });
        }
      }).exec();
    },

    /**
     * 启动渲染循环
     */
    startRenderLoop() {
      const render = () => {
        if (this.data.needsRedraw) {
          this.redraw();
          this.setData({ needsRedraw: false });
        }
        
        this.data.animationFrame = requestAnimationFrame(render);
      };
      
      render();
    },

    /**
     * 主绘制方法
     */
    redraw() {
      const ctx = this.data.canvasContext;
      if (!ctx) return;
      
      // 清空画布
      ctx.clearRect(0, 0, this.data.canvasWidth, this.data.canvasHeight);
      
      // 绘制地图元素
      this.drawBackground(ctx);
      this.drawRooms(ctx);
      this.drawWalls(ctx);
      this.drawPOIs(ctx);
      this.drawExits(ctx);
      
      // 绘制导航相关
      if (this.data.showNavigationRoute && this.data.navigationRoute.length > 0) {
        this.drawNavigationRoute(ctx);
      }
      
      // 绘制用户位置
      if (this.data.showUserPosition) {
        this.drawUserPosition(ctx);
      }
      
      // 绘制UI元素
      this.drawCompass(ctx);
      this.drawScale(ctx);
    },

    /**
     * 绘制背景
     */
    drawBackground(ctx) {
      ctx.fillStyle = '#f8f9fa';
      ctx.fillRect(0, 0, this.data.canvasWidth, this.data.canvasHeight);
      
      // 绘制网格
      this.drawGrid(ctx);
    },

    /**
     * 绘制网格
     */
    drawGrid(ctx) {
      const { scale, viewCenter, canvasWidth, canvasHeight } = this.data;
      const gridSize = 5; // 5米网格
      
      ctx.strokeStyle = '#e0e0e0';
      ctx.lineWidth = 0.5;
      ctx.setLineDash([2, 2]);
      
      // 计算网格起始点
      const startX = -viewCenter.x * scale + canvasWidth / 2;
      const startY = -viewCenter.y * scale + canvasHeight / 2;
      
      // 绘制垂直线
      for (let i = 0; i <= this.data.mapData.bounds.width / gridSize; i++) {
        const x = startX + i * gridSize * scale;
        if (x >= 0 && x <= canvasWidth) {
          ctx.beginPath();
          ctx.moveTo(x, 0);
          ctx.lineTo(x, canvasHeight);
          ctx.stroke();
        }
      }
      
      // 绘制水平线
      for (let i = 0; i <= this.data.mapData.bounds.height / gridSize; i++) {
        const y = startY + i * gridSize * scale;
        if (y >= 0 && y <= canvasHeight) {
          ctx.beginPath();
          ctx.moveTo(0, y);
          ctx.lineTo(canvasWidth, y);
          ctx.stroke();
        }
      }
      
      ctx.setLineDash([]);
    },

    /**
     * 绘制房间
     */
    drawRooms(ctx) {
      const { mapData } = this.data;
      
      mapData.rooms.forEach(room => {
        const screenPos = this.worldToScreen(room.x, room.y);
        const width = room.width * this.data.scale;
        const height = room.height * this.data.scale;
        
        // 绘制房间背景
        ctx.fillStyle = room.color || '#ffffff';
        ctx.fillRect(screenPos.x, screenPos.y, width, height);
        
        // 绘制房间边框
        ctx.strokeStyle = '#cccccc';
        ctx.lineWidth = 1;
        ctx.strokeRect(screenPos.x, screenPos.y, width, height);
        
        // 绘制房间名称
        if (width > 50 && height > 30) {
          ctx.fillStyle = '#666666';
          ctx.font = '12px sans-serif';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(
            room.name,
            screenPos.x + width / 2,
            screenPos.y + height / 2
          );
        }
      });
    },

    /**
     * 绘制墙体
     */
    drawWalls(ctx) {
      const { mapData } = this.data;
      
      ctx.strokeStyle = '#333333';
      ctx.lineWidth = 3;
      
      mapData.walls.forEach(wall => {
        const start = this.worldToScreen(wall.x1, wall.y1);
        const end = this.worldToScreen(wall.x2, wall.y2);
        
        ctx.beginPath();
        ctx.moveTo(start.x, start.y);
        ctx.lineTo(end.x, end.y);
        ctx.stroke();
      });
    },

    /**
     * 绘制兴趣点
     */
    drawPOIs(ctx) {
      const { mapData } = this.data;
      
      mapData.pois.forEach(poi => {
        const screenPos = this.worldToScreen(poi.x, poi.y);
        
        // 绘制POI图标
        ctx.save();
        ctx.translate(screenPos.x, screenPos.y);
        
        // 根据类型选择颜色和图标
        const config = this.getPOIConfig(poi.type);
        
        // 绘制圆形背景
        ctx.fillStyle = config.backgroundColor;
        ctx.beginPath();
        ctx.arc(0, 0, 12, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制边框
        ctx.strokeStyle = config.borderColor;
        ctx.lineWidth = 2;
        ctx.stroke();
        
        // 绘制图标文字
        ctx.fillStyle = config.textColor;
        ctx.font = '16px sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(config.icon, 0, 0);
        
        ctx.restore();
        
        // 绘制标签
        if (this.data.scale > 6) {
          ctx.fillStyle = '#333333';
          ctx.font = '10px sans-serif';
          ctx.textAlign = 'center';
          ctx.fillText(poi.name, screenPos.x, screenPos.y + 25);
        }
      });
    },

    /**
     * 获取POI配置
     */
    getPOIConfig(type) {
      const configs = {
        entrance: { icon: '🚪', backgroundColor: '#4CAF50', borderColor: '#388E3C', textColor: 'white' },
        elevator: { icon: '🛗', backgroundColor: '#2196F3', borderColor: '#1976D2', textColor: 'white' },
        meeting: { icon: '🏢', backgroundColor: '#FF9800', borderColor: '#F57C00', textColor: 'white' },
        restroom: { icon: '🚻', backgroundColor: '#9C27B0', borderColor: '#7B1FA2', textColor: 'white' },
        default: { icon: '📍', backgroundColor: '#607D8B', borderColor: '#455A64', textColor: 'white' }
      };
      
      return configs[type] || configs.default;
    },

    /**
     * 绘制出入口
     */
    drawExits(ctx) {
      const { mapData } = this.data;
      
      mapData.exits.forEach(exit => {
        const screenPos = this.worldToScreen(exit.x, exit.y);
        const width = exit.width * this.data.scale;
        
        // 绘制出入口
        ctx.fillStyle = exit.type === 'entrance' ? '#4CAF50' : '#FF5722';
        ctx.fillRect(screenPos.x - width/2, screenPos.y - 3, width, 6);
        
        // 绘制标签
        if (this.data.scale > 5) {
          ctx.fillStyle = '#333333';
          ctx.font = '9px sans-serif';
          ctx.textAlign = 'center';
          ctx.fillText(exit.name, screenPos.x, screenPos.y + 15);
        }
      });
    },

    /**
     * 绘制导航路径
     */
    drawNavigationRoute(ctx) {
      const route = this.data.navigationRoute;
      if (route.length < 2) return;
      
      // 绘制路径线
      ctx.strokeStyle = '#667eea';
      ctx.lineWidth = 4;
      ctx.setLineDash([]);
      
      ctx.beginPath();
      const startPos = this.worldToScreen(route[0].x, route[0].y);
      ctx.moveTo(startPos.x, startPos.y);
      
      for (let i = 1; i < route.length; i++) {
        const pos = this.worldToScreen(route[i].x, route[i].y);
        ctx.lineTo(pos.x, pos.y);
      }
      ctx.stroke();
      
      // 绘制路径点
      route.forEach((point, index) => {
        const screenPos = this.worldToScreen(point.x, point.y);
        
        ctx.fillStyle = index === 0 ? '#4CAF50' : 
                       index === route.length - 1 ? '#FF5722' : '#667eea';
        ctx.beginPath();
        ctx.arc(screenPos.x, screenPos.y, 6, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.strokeStyle = 'white';
        ctx.lineWidth = 2;
        ctx.stroke();
      });
    },

    /**
     * 绘制用户位置
     */
    drawUserPosition(ctx) {
      const { userPosition, userHeading } = this.data;
      const screenPos = this.worldToScreen(userPosition.x, userPosition.y);
      
      ctx.save();
      ctx.translate(screenPos.x, screenPos.y);
      
      // 绘制位置圆圈
      ctx.fillStyle = '#2196F3';
      ctx.beginPath();
      ctx.arc(0, 0, 8, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.strokeStyle = 'white';
      ctx.lineWidth = 3;
      ctx.stroke();
      
      // 绘制方向指示器
      ctx.rotate(userHeading * Math.PI / 180);
      ctx.fillStyle = 'white';
      ctx.beginPath();
      ctx.moveTo(0, -6);
      ctx.lineTo(-3, 3);
      ctx.lineTo(3, 3);
      ctx.closePath();
      ctx.fill();
      
      ctx.restore();
      
      // 绘制精度圆圈
      const accuracy = 2; // 2米精度
      const accuracyRadius = accuracy * this.data.scale;
      ctx.strokeStyle = 'rgba(33, 150, 243, 0.3)';
      ctx.lineWidth = 1;
      ctx.setLineDash([5, 5]);
      ctx.beginPath();
      ctx.arc(screenPos.x, screenPos.y, accuracyRadius, 0, Math.PI * 2);
      ctx.stroke();
      ctx.setLineDash([]);
    },

    /**
     * 绘制指南针
     */
    drawCompass(ctx) {
      const compassX = this.data.canvasWidth - 50;
      const compassY = 50;
      
      ctx.save();
      ctx.translate(compassX, compassY);
      
      // 绘制指南针背景
      ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
      ctx.beginPath();
      ctx.arc(0, 0, 20, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.strokeStyle = '#cccccc';
      ctx.lineWidth = 1;
      ctx.stroke();
      
      // 绘制指北针
      ctx.rotate(-this.data.userHeading * Math.PI / 180);
      
      // 北针（红色）
      ctx.fillStyle = '#FF5722';
      ctx.beginPath();
      ctx.moveTo(0, -15);
      ctx.lineTo(-3, -5);
      ctx.lineTo(3, -5);
      ctx.closePath();
      ctx.fill();
      
      // 南针（白色）
      ctx.fillStyle = 'white';
      ctx.strokeStyle = '#666666';
      ctx.beginPath();
      ctx.moveTo(0, 15);
      ctx.lineTo(-3, 5);
      ctx.lineTo(3, 5);
      ctx.closePath();
      ctx.fill();
      ctx.stroke();
      
      ctx.restore();
      
      // 绘制N标记
      ctx.fillStyle = '#333333';
      ctx.font = '10px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('N', compassX, compassY - 30);
    },

    /**
     * 绘制比例尺
     */
    drawScale(ctx) {
      const scaleX = 20;
      const scaleY = this.data.canvasHeight - 30;
      const scaleLength = 50; // 像素长度
      const realLength = scaleLength / this.data.scale; // 实际长度（米）
      
      // 绘制比例尺背景
      ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
      ctx.fillRect(scaleX - 5, scaleY - 15, scaleLength + 10, 25);
      
      // 绘制比例尺线
      ctx.strokeStyle = '#333333';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(scaleX, scaleY);
      ctx.lineTo(scaleX + scaleLength, scaleY);
      ctx.stroke();
      
      // 绘制刻度
      ctx.beginPath();
      ctx.moveTo(scaleX, scaleY - 3);
      ctx.lineTo(scaleX, scaleY + 3);
      ctx.moveTo(scaleX + scaleLength, scaleY - 3);
      ctx.lineTo(scaleX + scaleLength, scaleY + 3);
      ctx.stroke();
      
      // 绘制标签
      ctx.fillStyle = '#333333';
      ctx.font = '10px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(`${realLength.toFixed(1)}m`, scaleX + scaleLength / 2, scaleY + 15);
    },

    /**
     * 世界坐标转屏幕坐标
     */
    worldToScreen(worldX, worldY) {
      const { viewCenter, scale, canvasWidth, canvasHeight } = this.data;
      
      return {
        x: (worldX - viewCenter.x) * scale + canvasWidth / 2,
        y: (worldY - viewCenter.y) * scale + canvasHeight / 2
      };
    },

    /**
     * 屏幕坐标转世界坐标
     */
    screenToWorld(screenX, screenY) {
      const { viewCenter, scale, canvasWidth, canvasHeight } = this.data;
      
      return {
        x: (screenX - canvasWidth / 2) / scale + viewCenter.x,
        y: (screenY - canvasHeight / 2) / scale + viewCenter.y
      };
    },

    /**
     * 更新用户位置
     */
    updateUserPosition(position, heading) {
      this.setData({
        userPosition: position,
        userHeading: heading || 0,
        needsRedraw: true
      });
      
      // 如果在导航模式，自动居中到用户位置
      if (this.data.mode === 'navigation') {
        this.centerToUser();
      }
    },

    /**
     * 更新导航路径
     */
    updateNavigationRoute(route) {
      this.setData({
        navigationRoute: route,
        needsRedraw: true
      });
    },

    /**
     * 居中到用户位置
     */
    centerToUser() {
      this.setData({
        viewCenter: {
          x: this.data.userPosition.x,
          y: this.data.userPosition.y
        },
        needsRedraw: true
      });
    },

    /**
     * 触摸开始
     */
    onTouchStart(e) {
      const touch = e.touches[0];
      this.setData({
        isDragging: true,
        lastTouchPos: { x: touch.x, y: touch.y }
      });
    },

    /**
     * 触摸移动
     */
    onTouchMove(e) {
      if (!this.data.isDragging) return;
      
      const touch = e.touches[0];
      const deltaX = touch.x - this.data.lastTouchPos.x;
      const deltaY = touch.y - this.data.lastTouchPos.y;
      
      // 移动视图中心
      const newViewCenter = {
        x: this.data.viewCenter.x - deltaX / this.data.scale,
        y: this.data.viewCenter.y - deltaY / this.data.scale
      };
      
      this.setData({
        viewCenter: newViewCenter,
        lastTouchPos: { x: touch.x, y: touch.y },
        needsRedraw: true
      });
    },

    /**
     * 触摸结束
     */
    onTouchEnd() {
      this.setData({ isDragging: false });
    },

    /**
     * 地图点击事件
     */
    onMapTap(e) {
      const touch = e.detail || e.touches[0];
      const worldPos = this.screenToWorld(touch.x, touch.y);
      
      // 触发地图点击事件
      this.triggerEvent('mapTap', {
        worldPosition: worldPos,
        screenPosition: { x: touch.x, y: touch.y }
      });
    },

    /**
     * 缩放地图
     */
    zoomMap(delta) {
      const currentZoom = this.data.zoomLevel;
      const newZoom = Math.max(0.5, Math.min(3.0, currentZoom + delta));
      
      this.setData({ zoomLevel: newZoom });
      this.updateScale(newZoom);
    },

    /**
     * 清理资源
     */
    cleanup() {
      if (this.data.animationFrame) {
        cancelAnimationFrame(this.data.animationFrame);
      }
    }
  }
});