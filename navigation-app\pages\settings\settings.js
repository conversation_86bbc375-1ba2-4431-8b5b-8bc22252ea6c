/**
 * 设置页面
 * 提供定位模式、传感器校准、个人偏好设置
 */

const app = getApp();

Page({
  data: {
    // 基础设置
    navigationMode: 'standard',
    sampleRate: 50,
    enableMLA: true,
    
    // 校准参数
    stepLength: 0.75,
    magneticDeclination: 0,
    
    // 个人偏好
    voicePrompt: true,
    vibrationFeedback: true,
    
    // 系统信息
    systemInfo: {},
    locationStatus: 'unknown',
    
    // 界面状态
    showModeSelector: false,
    showCalibrationPanel: false,
    
    // 模式选项
    modeOptions: [
      { value: 'lite', name: '轻量模式', desc: '省电模式，适合长时间使用' },
      { value: 'standard', name: '标准模式', desc: '平衡精度和性能，推荐使用' },
      { value: 'precise', name: '高精度模式', desc: '最高精度，适合精确导航' }
    ]
  },

  onLoad() {
    console.log('⚙️ 设置页面加载');
    this.loadUserSettings();
    this.getSystemInfo();
    this.checkLocationStatus();
  },

  onShow() {
    console.log('👀 设置页面显示');
    this.updateLocationStatus();
  },

  /**
   * 加载用户设置
   */
  loadUserSettings() {
    const settings = app.globalData.userSettings;
    this.setData({
      navigationMode: settings.navigationMode,
      stepLength: settings.stepLength,
      magneticDeclination: settings.magneticDeclination,
      voicePrompt: settings.voicePrompt,
      vibrationFeedback: settings.vibrationFeedback
    });
  },

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.setData({
          systemInfo: {
            model: res.model,
            system: res.system,
            platform: res.platform,
            version: res.version,
            battery: res.batteryLevel || 'unknown'
          }
        });
      }
    });
  },

  /**
   * 检查定位状态
   */
  checkLocationStatus() {
    const appStatus = app.getAppStatus();
    this.setData({
      locationStatus: appStatus.isNavigating ? 'running' : 'stopped'
    });
  },

  /**
   * 更新定位状态
   */
  updateLocationStatus() {
    this.checkLocationStatus();
  },

  /**
   * 显示模式选择器
   */
  showModeSelector() {
    this.setData({ showModeSelector: true });
  },

  /**
   * 隐藏模式选择器
   */
  hideModeSelector() {
    this.setData({ showModeSelector: false });
  },

  /**
   * 选择导航模式
   */
  selectNavigationMode(e) {
    const mode = e.currentTarget.dataset.mode;
    
    this.setData({ 
      navigationMode: mode,
      showModeSelector: false 
    });
    
    // 更新应用设置
    app.globalData.userSettings.navigationMode = mode;
    app.saveUserSettings();
    
    // 如果惯导系统正在运行，更新配置
    if (app.globalData.inertialNav) {
      app.globalData.inertialNav.updateConfig({ mode: mode });
    }
    
    wx.showToast({
      title: `已切换到${this.getModeDisplayName(mode)}`,
      icon: 'success'
    });
  },

  /**
   * 获取模式显示名称
   */
  getModeDisplayName(mode) {
    const modeMap = {
      lite: '轻量模式',
      standard: '标准模式', 
      precise: '高精度模式'
    };
    return modeMap[mode] || mode;
  },

  /**
   * 步长调整
   */
  onStepLengthChange(e) {
    const stepLength = parseFloat(e.detail.value);
    this.setData({ stepLength });
    
    // 更新应用设置
    app.globalData.userSettings.stepLength = stepLength;
    app.saveUserSettings();
    
    // 更新惯导配置
    if (app.globalData.inertialNav) {
      app.globalData.inertialNav.updateConfig({
        calibration: { stepLength }
      });
    }
  },

  /**
   * 磁偏角调整
   */
  onMagneticDeclinationChange(e) {
    const magneticDeclination = parseFloat(e.detail.value);
    this.setData({ magneticDeclination });
    
    // 更新应用设置
    app.globalData.userSettings.magneticDeclination = magneticDeclination;
    app.saveUserSettings();
    
    // 更新惯导配置
    if (app.globalData.inertialNav) {
      app.globalData.inertialNav.updateConfig({
        calibration: { magneticDeclination }
      });
    }
  },

  /**
   * 语音提示开关
   */
  toggleVoicePrompt(e) {
    const voicePrompt = e.detail.value;
    this.setData({ voicePrompt });
    
    app.globalData.userSettings.voicePrompt = voicePrompt;
    app.saveUserSettings();
  },

  /**
   * 震动反馈开关
   */
  toggleVibrationFeedback(e) {
    const vibrationFeedback = e.detail.value;
    this.setData({ vibrationFeedback });
    
    app.globalData.userSettings.vibrationFeedback = vibrationFeedback;
    app.saveUserSettings();
  },

  /**
   * 显示校准面板
   */
  showCalibrationPanel() {
    this.setData({ showCalibrationPanel: true });
  },

  /**
   * 隐藏校准面板
   */
  hideCalibrationPanel() {
    this.setData({ showCalibrationPanel: false });
  },

  /**
   * 开始传感器校准
   */
  async startCalibration() {
    wx.showLoading({ title: '校准中...' });
    
    try {
      if (!app.globalData.inertialNav) {
        throw new Error('惯导系统未初始化');
      }
      
      const success = await app.globalData.inertialNav.calibrate();
      
      wx.hideLoading();
      
      if (success) {
        wx.showToast({
          title: '校准成功',
          icon: 'success'
        });
        this.hideCalibrationPanel();
      } else {
        wx.showToast({
          title: '校准失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('❌ 校准失败:', error);
      wx.showToast({
        title: '校准异常',
        icon: 'error'
      });
    }
  },

  /**
   * 重置设置
   */
  resetSettings() {
    wx.showModal({
      title: '重置设置',
      content: '确定要重置所有设置为默认值吗？',
      success: (res) => {
        if (res.confirm) {
          // 恢复默认设置
          const defaultSettings = {
            navigationMode: 'standard',
            stepLength: 0.75,
            magneticDeclination: 0,
            voicePrompt: true,
            vibrationFeedback: true
          };
          
          this.setData(defaultSettings);
          app.globalData.userSettings = { ...defaultSettings };
          app.saveUserSettings();
          
          wx.showToast({
            title: '设置已重置',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 查看系统状态
   */
  viewSystemStatus() {
    let statusText = '系统状态信息:\n\n';
    
    if (app.globalData.inertialNav) {
      const status = app.globalData.inertialNav.getSystemStatus();
      const stats = app.globalData.inertialNav.getStatistics();
      
      statusText += `定位状态: ${status.isRunning ? '运行中' : '已停止'}\n`;
      statusText += `运行模式: ${status.operatingMode}\n`;
      statusText += `是否已校准: ${status.isCalibrated ? '是' : '否'}\n`;
      statusText += `质量等级: ${status.qualityLevel}\n`;
      statusText += `总步数: ${stats.totalSteps}\n`;
      statusText += `总距离: ${stats.totalDistance.toFixed(2)}米\n`;
      statusText += `平均置信度: ${(stats.averageConfidence * 100).toFixed(1)}%`;
    } else {
      statusText += '惯导系统未初始化';
    }
    
    wx.showModal({
      title: '系统状态',
      content: statusText,
      showCancel: false
    });
  },

  /**
   * 导出设置
   */
  exportSettings() {
    const settingsData = {
      userSettings: app.globalData.userSettings,
      systemInfo: this.data.systemInfo,
      exportTime: new Date().toISOString()
    };
    
    const dataStr = JSON.stringify(settingsData, null, 2);
    
    wx.setClipboardData({
      data: dataStr,
      success: () => {
        wx.showToast({
          title: '设置已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 清除数据
   */
  clearData() {
    wx.showModal({
      title: '清除数据',
      content: '确定要清除所有应用数据吗？这将删除轨迹记录和统计信息。',
      success: (res) => {
        if (res.confirm) {
          try {
            // 清除本地存储
            wx.clearStorageSync();
            
            // 重置惯导系统
            if (app.globalData.inertialNav) {
              app.globalData.inertialNav.reset();
            }
            
            wx.showToast({
              title: '数据已清除',
              icon: 'success'
            });
            
            // 重新加载设置
            setTimeout(() => {
              this.loadUserSettings();
            }, 1000);
            
          } catch (error) {
            console.error('❌ 清除数据失败:', error);
            wx.showToast({
              title: '清除失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  /**
   * 关于信息
   */
  showAbout() {
    wx.showModal({
      title: '关于室内导航',
      content: '基于惯性导航技术的室内定位系统\n\n版本: 1.0.0\n技术: PDR + 传感器融合\n支持: 加速度计、陀螺仪、磁力计\n\n技术支持: PDR Team',
      showCancel: false
    });
  }
});