/**
 * 惯导传感器管理器
 * 专门为库封装的传感器数据采集和预处理模块
 */

export default class InertialSensorManager {
  constructor(config = {}) {
    this.config = {
      sampleRate: config.sampleRate || 50,
      enableAccelerometer: true,
      enableGyroscope: true,
      enableMagnetometer: true,
      enableBarometer: false,
      // 数据预处理配置
      enableFiltering: config.enableFiltering !== false,
      filterWindow: config.filterWindow || 5,
      enableCalibration: config.enableCalibration !== false,
      ...config
    };
    
    // 传感器状态
    this.sensorStatus = {
      accelerometer: { available: false, active: false, lastData: null },
      gyroscope: { available: false, active: false, lastData: null },
      magnetometer: { available: false, active: false, lastData: null },
      barometer: { available: false, active: false, lastData: null }
    };
    
    // 数据缓存
    this.dataBuffer = {
      accelerometer: [],
      gyroscope: [],
      magnetometer: [],
      barometer: []
    };
    
    // 校准参数
    this.calibration = {
      accelerometerBias: config.calibration?.accelerometerBias || [0, 0, 0],
      gyroscopeBias: config.calibration?.gyroscopeBias || [0, 0, 0],
      magnetometerBias: config.calibration?.magnetometerBias || [0, 0, 0],
      accelerometerScale: config.calibration?.accelerometerScale || [1, 1, 1],
      magneticDeclination: config.calibration?.magneticDeclination || 0
    };
    
    // 滤波器
    this.filters = {
      accelerometer: new MovingAverageFilter(this.config.filterWindow),
      gyroscope: new MovingAverageFilter(this.config.filterWindow),
      magnetometer: new MovingAverageFilter(this.config.filterWindow)
    };
    
    // 采样控制
    this.samplingInterval = null;
    this.lastSampleTime = 0;
    this.sampleInterval = 1000 / this.config.sampleRate;
    
    // 数据回调
    this.dataCallback = null;
    
    // 运行状态
    this.isRunning = false;
    this.isPaused = false;
    
    // 错误处理
    this.errorCallback = null;
    
    console.log('📡 传感器管理器初始化完成', {
      sampleRate: this.config.sampleRate,
      enableFiltering: this.config.enableFiltering
    });
  }
  
  /**
   * 启动传感器采集
   */
  async start(config = {}) {
    if (this.isRunning) {
      console.warn('⚠️ 传感器管理器已在运行');
      return;
    }
    
    try {
      console.log('🚀 启动传感器采集...');
      
      // 更新配置
      this.updateConfig(config);
      
      // 检查传感器可用性
      await this.checkSensorAvailability();
      
      // 启动各个传感器
      await this.startAccelerometer();
      await this.startGyroscope();
      await this.startMagnetometer();
      
      if (this.config.enableBarometer) {
        await this.startBarometer();
      }
      
      // 启动数据采样
      this.startSampling();
      
      this.isRunning = true;
      console.log('✅ 传感器采集启动成功');
      
    } catch (error) {
      console.error('❌ 传感器启动失败:', error);
      this.handleError('sensor_start_failed', error);
      throw error;
    }
  }
  
  /**
   * 停止传感器采集
   */
  stop() {
    if (!this.isRunning) {
      return;
    }
    
    console.log('⏹️ 停止传感器采集');
    
    // 停止采样
    this.stopSampling();
    
    // 停止各个传感器
    this.stopAccelerometer();
    this.stopGyroscope();
    this.stopMagnetometer();
    this.stopBarometer();
    
    // 清空缓存
    this.clearBuffers();
    
    this.isRunning = false;
    this.isPaused = false;
  }
  
  /**
   * 暂停传感器采集
   */
  pause() {
    if (!this.isRunning || this.isPaused) {
      return;
    }
    
    this.isPaused = true;
    console.log('⏸️ 暂停传感器采集');
  }
  
  /**
   * 恢复传感器采集
   */
  resume() {
    if (!this.isRunning || !this.isPaused) {
      return;
    }
    
    this.isPaused = false;
    console.log('▶️ 恢复传感器采集');
  }
  
  /**
   * 重启传感器
   */
  async restart() {
    console.log('🔄 重启传感器管理器');
    this.stop();
    await new Promise(resolve => setTimeout(resolve, 500));
    await this.start();
  }
  
  /**
   * 检查传感器可用性
   */
  async checkSensorAvailability() {
    return new Promise((resolve) => {
      // 检查加速计
      wx.onAccelerometerChange(() => {
        this.sensorStatus.accelerometer.available = true;
      });
      wx.startAccelerometer({ 
        interval: 'game',
        success: () => {
          this.sensorStatus.accelerometer.available = true;
        },
        fail: () => {
          this.sensorStatus.accelerometer.available = false;
        }
      });
      
      // 检查陀螺仪
      wx.onGyroscopeChange(() => {
        this.sensorStatus.gyroscope.available = true;
      });
      wx.startGyroscope({
        interval: 'game',
        success: () => {
          this.sensorStatus.gyroscope.available = true;
        },
        fail: () => {
          this.sensorStatus.gyroscope.available = false;
        }
      });
      
      // 检查罗盘
      wx.onCompassChange(() => {
        this.sensorStatus.magnetometer.available = true;
      });
      wx.startCompass({
        success: () => {
          this.sensorStatus.magnetometer.available = true;
        },
        fail: () => {
          this.sensorStatus.magnetometer.available = false;
        }
      });
      
      // 等待检查完成
      setTimeout(() => {
        console.log('📊 传感器可用性检查完成:', this.sensorStatus);
        resolve();
      }, 1000);
    });
  }
  
  /**
   * 启动加速计
   */
  async startAccelerometer() {
    if (!this.sensorStatus.accelerometer.available) {
      throw new Error('加速计不可用');
    }
    
    wx.onAccelerometerChange((data) => {
      if (this.isPaused) return;
      
      // 应用校准
      const calibratedData = this.calibrateAccelerometer(data);
      
      // 添加到缓存
      this.dataBuffer.accelerometer.push({
        ...calibratedData,
        timestamp: Date.now()
      });
      
      // 限制缓存大小
      if (this.dataBuffer.accelerometer.length > 100) {
        this.dataBuffer.accelerometer.shift();
      }
      
      this.sensorStatus.accelerometer.lastData = calibratedData;
    });
    
    wx.startAccelerometer({
      interval: 'game', // 20ms采样间隔
      success: () => {
        this.sensorStatus.accelerometer.active = true;
        console.log('✅ 加速计启动成功');
      },
      fail: (error) => {
        console.error('❌ 加速计启动失败:', error);
        throw error;
      }
    });
  }
  
  /**
   * 启动陀螺仪
   */
  async startGyroscope() {
    if (!this.sensorStatus.gyroscope.available) {
      throw new Error('陀螺仪不可用');
    }
    
    wx.onGyroscopeChange((data) => {
      if (this.isPaused) return;
      
      // 应用校准
      const calibratedData = this.calibrateGyroscope(data);
      
      // 添加到缓存
      this.dataBuffer.gyroscope.push({
        ...calibratedData,
        timestamp: Date.now()
      });
      
      // 限制缓存大小
      if (this.dataBuffer.gyroscope.length > 100) {
        this.dataBuffer.gyroscope.shift();
      }
      
      this.sensorStatus.gyroscope.lastData = calibratedData;
    });
    
    wx.startGyroscope({
      interval: 'game', // 20ms采样间隔
      success: () => {
        this.sensorStatus.gyroscope.active = true;
        console.log('✅ 陀螺仪启动成功');
      },
      fail: (error) => {
        console.error('❌ 陀螺仪启动失败:', error);
        throw error;
      }
    });
  }
  
  /**
   * 启动罗盘
   */
  async startMagnetometer() {
    if (!this.sensorStatus.magnetometer.available) {
      throw new Error('罗盘不可用');
    }
    
    wx.onCompassChange((data) => {
      if (this.isPaused) return;
      
      // 转换罗盘数据格式
      const magnetometerData = {
        x: Math.cos((data.direction - 90) * Math.PI / 180),
        y: Math.sin((data.direction - 90) * Math.PI / 180),
        z: 0,
        direction: data.direction,
        accuracy: data.accuracy
      };
      
      // 应用校准
      const calibratedData = this.calibrateMagnetometer(magnetometerData);
      
      // 添加到缓存
      this.dataBuffer.magnetometer.push({
        ...calibratedData,
        timestamp: Date.now()
      });
      
      // 限制缓存大小
      if (this.dataBuffer.magnetometer.length > 100) {
        this.dataBuffer.magnetometer.shift();
      }
      
      this.sensorStatus.magnetometer.lastData = calibratedData;
    });
    
    wx.startCompass({
      success: () => {
        this.sensorStatus.magnetometer.active = true;
        console.log('✅ 罗盘启动成功');
      },
      fail: (error) => {
        console.error('❌ 罗盘启动失败:', error);
        throw error;
      }
    });
  }
  
  /**
   * 启动气压计（如果支持）
   */
  async startBarometer() {
    // 微信小程序暂不支持气压计，保留接口
    this.sensorStatus.barometer.available = false;
    console.log('ℹ️ 气压计暂不支持');
  }
  
  /**
   * 停止各个传感器
   */
  stopAccelerometer() {
    if (this.sensorStatus.accelerometer.active) {
      wx.stopAccelerometer();
      this.sensorStatus.accelerometer.active = false;
    }
  }
  
  stopGyroscope() {
    if (this.sensorStatus.gyroscope.active) {
      wx.stopGyroscope();
      this.sensorStatus.gyroscope.active = false;
    }
  }
  
  stopMagnetometer() {
    if (this.sensorStatus.magnetometer.active) {
      wx.stopCompass();
      this.sensorStatus.magnetometer.active = false;
    }
  }
  
  stopBarometer() {
    // 预留接口
  }
  
  /**
   * 启动数据采样
   */
  startSampling() {
    this.samplingInterval = setInterval(() => {
      if (this.isPaused || !this.isRunning) return;
      
      const now = Date.now();
      if (now - this.lastSampleTime < this.sampleInterval) {
        return;
      }
      
      this.lastSampleTime = now;
      
      // 采集当前传感器数据
      const sensorData = this.collectCurrentData();
      
      // 触发数据回调
      if (this.dataCallback && sensorData) {
        this.dataCallback(sensorData);
      }
      
    }, this.sampleInterval);
  }
  
  /**
   * 停止数据采样
   */
  stopSampling() {
    if (this.samplingInterval) {
      clearInterval(this.samplingInterval);
      this.samplingInterval = null;
    }
  }
  
  /**
   * 收集当前传感器数据
   */
  collectCurrentData() {
    const timestamp = Date.now();
    
    // 检查是否有有效数据
    if (!this.sensorStatus.accelerometer.lastData || 
        !this.sensorStatus.gyroscope.lastData) {
      return null;
    }
    
    const rawData = {
      timestamp,
      accelerometer: this.sensorStatus.accelerometer.lastData,
      gyroscope: this.sensorStatus.gyroscope.lastData,
      magnetometer: this.sensorStatus.magnetometer.lastData
    };
    
    // 应用滤波
    let processedData = rawData;
    if (this.config.enableFiltering) {
      processedData = this.applyFiltering(rawData);
    }
    
    return processedData;
  }
  
  /**
   * 应用数据滤波
   */
  applyFiltering(data) {
    const filtered = { ...data };
    
    if (data.accelerometer) {
      filtered.accelerometer = this.filters.accelerometer.filter(data.accelerometer);
    }
    
    if (data.gyroscope) {
      filtered.gyroscope = this.filters.gyroscope.filter(data.gyroscope);
    }
    
    if (data.magnetometer) {
      filtered.magnetometer = this.filters.magnetometer.filter(data.magnetometer);
    }
    
    return filtered;
  }
  
  /**
   * 加速计校准
   */
  calibrateAccelerometer(data) {
    const bias = this.calibration.accelerometerBias;
    const scale = this.calibration.accelerometerScale;
    
    return {
      x: (data.x - bias[0]) * scale[0],
      y: (data.y - bias[1]) * scale[1],
      z: (data.z - bias[2]) * scale[2]
    };
  }
  
  /**
   * 陀螺仪校准
   */
  calibrateGyroscope(data) {
    const bias = this.calibration.gyroscopeBias;
    
    return {
      x: data.x - bias[0],
      y: data.y - bias[1],
      z: data.z - bias[2]
    };
  }
  
  /**
   * 磁力计校准
   */
  calibrateMagnetometer(data) {
    // 应用磁偏角校正
    const declination = this.calibration.magneticDeclination;
    const correctedDirection = (data.direction + declination + 360) % 360;
    
    return {
      ...data,
      direction: correctedDirection,
      x: Math.cos((correctedDirection - 90) * Math.PI / 180),
      y: Math.sin((correctedDirection - 90) * Math.PI / 180)
    };
  }
  
  /**
   * 传感器校准
   */
  async calibrate(calibrationData = {}) {
    console.log('🔧 开始传感器校准...');
    
    try {
      // 静态校准 - 收集静止状态下的传感器数据
      const staticData = await this.collectStaticCalibrationData();
      
      // 计算偏置
      this.calibration.accelerometerBias = this.calculateAccelerometerBias(staticData.accelerometer);
      this.calibration.gyroscopeBias = this.calculateGyroscopeBias(staticData.gyroscope);
      
      // 应用用户提供的校准数据
      if (calibrationData.stepLength) {
        this.calibration.stepLength = calibrationData.stepLength;
      }
      
      if (calibrationData.magneticDeclination !== undefined) {
        this.calibration.magneticDeclination = calibrationData.magneticDeclination;
      }
      
      console.log('✅ 传感器校准完成:', this.calibration);
      
      return {
        success: true,
        parameters: this.calibration
      };
      
    } catch (error) {
      console.error('❌ 传感器校准失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * 收集静态校准数据
   */
  async collectStaticCalibrationData() {
    return new Promise((resolve) => {
      const calibrationData = {
        accelerometer: [],
        gyroscope: []
      };
      
      let sampleCount = 0;
      const targetSamples = 100; // 2秒数据
      
      const collectData = () => {
        if (this.sensorStatus.accelerometer.lastData) {
          calibrationData.accelerometer.push(this.sensorStatus.accelerometer.lastData);
        }
        
        if (this.sensorStatus.gyroscope.lastData) {
          calibrationData.gyroscope.push(this.sensorStatus.gyroscope.lastData);
        }
        
        sampleCount++;
        
        if (sampleCount >= targetSamples) {
          resolve(calibrationData);
        } else {
          setTimeout(collectData, 20);
        }
      };
      
      collectData();
    });
  }
  
  /**
   * 计算加速计偏置
   */
  calculateAccelerometerBias(data) {
    if (data.length === 0) return [0, 0, 0];
    
    const sum = data.reduce((acc, curr) => ({
      x: acc.x + curr.x,
      y: acc.y + curr.y,
      z: acc.z + curr.z
    }), { x: 0, y: 0, z: 0 });
    
    const avg = {
      x: sum.x / data.length,
      y: sum.y / data.length,
      z: sum.z / data.length
    };
    
    // Z轴应该是重力加速度，减去理论值
    return [avg.x, avg.y, avg.z - 1.0]; // 1g = 1.0 in normalized units
  }
  
  /**
   * 计算陀螺仪偏置
   */
  calculateGyroscopeBias(data) {
    if (data.length === 0) return [0, 0, 0];
    
    const sum = data.reduce((acc, curr) => ({
      x: acc.x + curr.x,
      y: acc.y + curr.y,
      z: acc.z + curr.z
    }), { x: 0, y: 0, z: 0 });
    
    return [
      sum.x / data.length,
      sum.y / data.length,
      sum.z / data.length
    ];
  }
  
  /**
   * 清空数据缓存
   */
  clearBuffers() {
    this.dataBuffer.accelerometer = [];
    this.dataBuffer.gyroscope = [];
    this.dataBuffer.magnetometer = [];
    this.dataBuffer.barometer = [];
  }
  
  /**
   * 设置数据回调
   */
  setDataCallback(callback) {
    this.dataCallback = callback;
  }
  
  /**
   * 设置错误回调
   */
  setErrorCallback(callback) {
    this.errorCallback = callback;
  }
  
  /**
   * 更新配置
   */
  updateConfig(config) {
    this.config = { ...this.config, ...config };
    
    // 更新采样间隔
    if (config.sampleRate) {
      this.sampleInterval = 1000 / config.sampleRate;
    }
    
    // 更新校准参数
    if (config.calibration) {
      this.calibration = { ...this.calibration, ...config.calibration };
    }
    
    // 更新滤波窗口
    if (config.filterWindow) {
      this.filters.accelerometer.setWindowSize(config.filterWindow);
      this.filters.gyroscope.setWindowSize(config.filterWindow);
      this.filters.magnetometer.setWindowSize(config.filterWindow);
    }
  }
  
  /**
   * 获取传感器状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      isPaused: this.isPaused,
      sensors: { ...this.sensorStatus },
      calibration: { ...this.calibration },
      bufferSizes: {
        accelerometer: this.dataBuffer.accelerometer.length,
        gyroscope: this.dataBuffer.gyroscope.length,
        magnetometer: this.dataBuffer.magnetometer.length
      }
    };
  }
  
  /**
   * 错误处理
   */
  handleError(type, error) {
    const errorInfo = {
      type,
      message: error.message || error,
      timestamp: Date.now(),
      sensorStatus: this.sensorStatus
    };
    
    if (this.errorCallback) {
      this.errorCallback(errorInfo);
    }
    
    console.error('传感器管理器错误:', errorInfo);
  }
}

/**
 * 移动平均滤波器
 */
class MovingAverageFilter {
  constructor(windowSize = 5) {
    this.windowSize = windowSize;
    this.buffer = {
      x: [],
      y: [],
      z: []
    };
  }
  
  filter(data) {
    // 添加新数据
    this.buffer.x.push(data.x);
    this.buffer.y.push(data.y);
    this.buffer.z.push(data.z);
    
    // 限制缓存大小
    if (this.buffer.x.length > this.windowSize) {
      this.buffer.x.shift();
      this.buffer.y.shift();
      this.buffer.z.shift();
    }
    
    // 计算平均值
    const avg = {
      x: this.buffer.x.reduce((a, b) => a + b, 0) / this.buffer.x.length,
      y: this.buffer.y.reduce((a, b) => a + b, 0) / this.buffer.y.length,
      z: this.buffer.z.reduce((a, b) => a + b, 0) / this.buffer.z.length
    };
    
    // 保留其他属性
    return { ...data, ...avg };
  }
  
  setWindowSize(size) {
    this.windowSize = size;
    
    // 截断缓存
    if (this.buffer.x.length > size) {
      this.buffer.x = this.buffer.x.slice(-size);
      this.buffer.y = this.buffer.y.slice(-size);
      this.buffer.z = this.buffer.z.slice(-size);
    }
  }
  
  reset() {
    this.buffer.x = [];
    this.buffer.y = [];
    this.buffer.z = [];
  }
}