/**
 * 室内导航小程序主应用入口
 * 基于微信小程序惯导定位库
 */

const WXInertialNavigation = require('./lib/WXInertialNavigation.js');

App({
  // 全局数据
  globalData: {
    // 惯导定位库实例
    inertialNav: null,
    
    // 应用状态
    isNavInitialized: false,
    isNavigating: false,
    
    // 用户信息
    userInfo: null,
    userSettings: {
      stepLength: 0.75,           // 步长
      magneticDeclination: 0,     // 磁偏角
      navigationMode: 'standard', // 导航模式
      voicePrompt: true,          // 语音提示
      vibrationFeedback: true     // 震动反馈
    },
    
    // 当前位置
    currentPosition: {
      x: 0,
      y: 0,
      z: 0,
      heading: 0,
      confidence: 0,
      timestamp: 0
    },
    
    // 导航数据
    navigationData: {
      startPoint: null,
      endPoint: null,
      route: [],
      currentStep: 0,
      remainingDistance: 0,
      isRouteActive: false
    },
    
    // 地图数据
    mapData: {
      currentFloor: 1,
      buildings: [],
      pois: [],        // 兴趣点
      landmarks: []    // 地标点
    },
    
    // 性能统计
    statistics: {
      totalDistance: 0,
      totalSteps: 0,
      navigationCount: 0,
      averageAccuracy: 0
    }
  },

  /**
   * 应用启动
   */
  onLaunch(options) {
    console.log('🚀 室内导航小程序启动');
    
    // 初始化用户设置
    this.loadUserSettings();
    
    // 初始化地图数据
    this.loadMapData();
    
    // 初始化惯导定位库
    this.initInertialNavigation();
    
    // 记录启动信息
    this.recordLaunchInfo(options);
  },

  /**
   * 应用显示
   */
  onShow() {
    console.log('👀 应用显示');
    
    // 恢复惯导定位
    if (this.globalData.inertialNav && this.globalData.isNavigating) {
      this.globalData.inertialNav.pause(false);
    }
  },

  /**
   * 应用隐藏
   */
  onHide() {
    console.log('🙈 应用隐藏');
    
    // 暂停惯导定位以节省电量
    if (this.globalData.inertialNav && this.globalData.isNavigating) {
      this.globalData.inertialNav.pause(true);
    }
    
    // 保存当前状态
    this.saveAppState();
  },

  /**
   * 应用错误处理
   */
  onError(error) {
    console.error('💥 应用错误:', error);
    
    // 记录错误信息
    this.recordError(error);
    
    // 尝试恢复
    this.recoverFromError(error);
  },

  /**
   * 初始化惯导定位库
   */
  initInertialNavigation() {
    try {
      console.log('🧭 初始化惯导定位库...');
      
      // 首先尝试清理可能存在的传感器状态
      this.cleanupSensorState();
      
      // 创建惯导实例
      this.globalData.inertialNav = new WXInertialNavigation({
        initialPosition: { x: 0, y: 0, z: 0 },
        mode: this.globalData.userSettings.navigationMode,
        sampleRate: 50,
        
        calibration: {
          stepLength: this.globalData.userSettings.stepLength,
          magneticDeclination: this.globalData.userSettings.magneticDeclination
        },
        
        fusion: {
          adaptiveWeighting: true,
          confidenceThreshold: 0.2
        }
      });
      
      // 设置回调函数
      this.setupNavigationCallbacks();
      
      this.globalData.isNavInitialized = true;
      console.log('✅ 惯导定位库初始化完成');
      
    } catch (error) {
      console.error('❌ 惯导定位库初始化失败:', error);
      this.globalData.isNavInitialized = false;
    }
  },

  /**
   * 设置惯导回调函数
   */
  setupNavigationCallbacks() {
    this.globalData.inertialNav.setCallbacks({
      // 位置更新回调
      onLocationUpdate: (location) => {
        this.handleLocationUpdate(location);
      },
      
      // 步态检测回调
      onStepDetected: (stepInfo) => {
        this.handleStepDetected(stepInfo);
      },
      
      // 错误处理回调
      onError: (error) => {
        this.handleNavigationError(error);
      },
      
      // 校准需求回调
      onCalibrationRequired: () => {
        this.handleCalibrationRequired();
      }
    });
  },

  /**
   * 处理位置更新
   */
  handleLocationUpdate(location) {
    // 检查位置数据是否有效
    if (!location) {
      console.warn('⚠️ 位置数据为空');
      return;
    }
    
    // 检查位置数据结构
    let position;
    if (location.position) {
      // 新的数据结构：location.position.x
      position = location.position;
    } else if (location.x !== undefined) {
      // 直接位置数据：location.x
      position = location;
    } else {
      console.warn('⚠️ 位置数据格式无效:', location);
      return;
    }
    
    // 完善的位置坐标验证
    if (!this.isValidPosition(position)) {
      console.warn('⚠️ 位置坐标验证失败:', position);
      return;
    }
    
    // 检查位置跳变
    if (!this.isReasonablePositionChange(position)) {
      console.warn('⚠️ 检测到位置跳变，忽略本次更新:', position);
      return;
    }
    
    // 更新当前位置
    this.globalData.currentPosition = {
      x: position.x,
      y: position.y,
      z: position.z || 0,
      heading: location.heading || 0,
      confidence: location.confidence || 0,
      timestamp: location.timestamp || Date.now()
    };
    
    // 更新统计信息
    this.updateStatistics(location);
    
    // 如果正在导航，更新导航状态
    if (this.globalData.navigationData.isRouteActive) {
      this.updateNavigationProgress(location);
    }
    
    // 通知页面更新
    this.notifyLocationUpdate(location);
  },

  /**
   * 处理步态检测
   */
  handleStepDetected(stepInfo) {
    // 更新步数统计
    this.globalData.statistics.totalSteps = stepInfo.stepCount;
    
    // 震动反馈
    if (this.globalData.userSettings.vibrationFeedback) {
      wx.vibrateShort({ type: 'light' });
    }
    
    // 通知页面更新
    this.notifyStepDetected(stepInfo);
  },

  /**
   * 处理导航错误
   */
  handleNavigationError(error) {
    console.error('🚨 导航错误:', error);
    
    // 显示错误提示
    wx.showToast({
      title: '定位异常',
      icon: 'none',
      duration: 2000
    });
    
    // 记录错误
    this.recordError(error);
  },

  /**
   * 处理校准需求
   */
  handleCalibrationRequired() {
    wx.showModal({
      title: '需要校准',
      content: '为了提高导航精度，建议进行传感器校准',
      confirmText: '立即校准',
      cancelText: '稍后',
      success: (res) => {
        if (res.confirm) {
          // 跳转到校准页面
          wx.navigateTo({
            url: '/pages/settings/calibration/calibration'
          });
        }
      }
    });
  },

  /**
   * 更新导航进度
   */
  updateNavigationProgress(location) {
    const navData = this.globalData.navigationData;
    
    if (!navData.route || navData.route.length === 0) return;
    
    // 获取当前位置
    const currentPosition = this.globalData.currentPosition;
    if (!currentPosition || currentPosition.x === undefined) {
      return;
    }
    
    // 计算到当前目标点的距离
    const currentTarget = navData.route[navData.currentStep];
    if (currentTarget) {
      const distance = this.calculateDistance(currentPosition, currentTarget);
      
      // 如果接近当前目标点，切换到下一个
      if (distance < 2.0 && navData.currentStep < navData.route.length - 1) {
        navData.currentStep++;
        this.announceNextDirection();
      }
      
      // 更新剩余距离
      navData.remainingDistance = this.calculateRemainingDistance(currentPosition, navData);
    }
  },

  /**
   * 语音导航提示
   */
  announceNextDirection() {
    if (!this.globalData.userSettings.voicePrompt) return;
    
    const navData = this.globalData.navigationData;
    const nextTarget = navData.route[navData.currentStep];
    
    if (nextTarget) {
      // 简单的方向提示
      const direction = this.getDirectionText(nextTarget);
      wx.showToast({
        title: direction,
        icon: 'none',
        duration: 3000
      });
    }
  },

  /**
   * 获取方向文字
   */
  getDirectionText(target) {
    const current = this.globalData.currentPosition;
    const dx = target.x - current.x;
    const dy = target.y - current.y;
    
    if (Math.abs(dx) > Math.abs(dy)) {
      return dx > 0 ? '向右前进' : '向左前进';
    } else {
      return dy > 0 ? '向前前进' : '向后前进';
    }
  },

  /**
   * 计算距离
   */
  calculateDistance(pos1, pos2) {
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    return Math.sqrt(dx * dx + dy * dy);
  },

  /**
   * 计算剩余距离
   */
  calculateRemainingDistance(currentPos, navData) {
    let totalDistance = 0;
    
    // 到当前目标点的距离
    if (navData.currentStep < navData.route.length) {
      totalDistance += this.calculateDistance(currentPos, navData.route[navData.currentStep]);
      
      // 剩余路径点之间的距离
      for (let i = navData.currentStep; i < navData.route.length - 1; i++) {
        totalDistance += this.calculateDistance(navData.route[i], navData.route[i + 1]);
      }
    }
    
    return totalDistance;
  },

  /**
   * 更新统计信息
   */
  updateStatistics(location) {
    const stats = this.globalData.statistics;
    
    // 更新总距离（基于惯导库的统计）
    if (this.globalData.inertialNav) {
      const navStats = this.globalData.inertialNav.getStatistics();
      stats.totalDistance = navStats.totalDistance;
      stats.averageAccuracy = navStats.averageConfidence;
    }
  },

  /**
   * 通知页面位置更新
   */
  notifyLocationUpdate(location) {
    // 获取当前页面
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      
      // 如果页面有位置更新处理方法，调用它
      if (currentPage.onLocationUpdate && typeof currentPage.onLocationUpdate === 'function') {
        currentPage.onLocationUpdate(location);
      }
    }
  },

  /**
   * 通知页面步态检测
   */
  notifyStepDetected(stepInfo) {
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      
      if (currentPage.onStepDetected && typeof currentPage.onStepDetected === 'function') {
        currentPage.onStepDetected(stepInfo);
      }
    }
  },

  /**
   * 加载用户设置
   */
  loadUserSettings() {
    try {
      const settings = wx.getStorageSync('user_settings');
      if (settings) {
        this.globalData.userSettings = { ...this.globalData.userSettings, ...settings };
      }
      console.log('👤 用户设置已加载:', this.globalData.userSettings);
    } catch (error) {
      console.warn('⚠️ 用户设置加载失败:', error);
    }
  },

  /**
   * 保存用户设置
   */
  saveUserSettings() {
    try {
      wx.setStorageSync('user_settings', this.globalData.userSettings);
      console.log('💾 用户设置已保存');
    } catch (error) {
      console.error('❌ 用户设置保存失败:', error);
    }
  },

  /**
   * 加载地图数据
   */
  loadMapData() {
    try {
      // 从本地存储加载地图数据
      const mapData = wx.getStorageSync('map_data');
      if (mapData) {
        this.globalData.mapData = { ...this.globalData.mapData, ...mapData };
      } else {
        // 使用默认地图数据
        this.loadDefaultMapData();
      }
      console.log('🗺️ 地图数据已加载');
    } catch (error) {
      console.warn('⚠️ 地图数据加载失败，使用默认数据');
      this.loadDefaultMapData();
    }
  },

  /**
   * 加载默认地图数据
   */
  loadDefaultMapData() {
    this.globalData.mapData = {
      currentFloor: 1,
      buildings: [
        {
          id: 'building_1',
          name: '办公楼A',
          floors: [1, 2, 3, 4],
          bounds: { x: 0, y: 0, width: 100, height: 80 }
        }
      ],
      pois: [
        { id: 'poi_1', name: '大厅', x: 50, y: 10, floor: 1, type: 'entrance' },
        { id: 'poi_2', name: '电梯', x: 45, y: 40, floor: 1, type: 'elevator' },
        { id: 'poi_3', name: '会议室A', x: 20, y: 30, floor: 1, type: 'meeting' },
        { id: 'poi_4', name: '会议室B', x: 80, y: 30, floor: 1, type: 'meeting' },
        { id: 'poi_5', name: '洗手间', x: 10, y: 70, floor: 1, type: 'restroom' }
      ],
      landmarks: [
        { id: 'landmark_1', x: 50, y: 10, description: '入口大厅' },
        { id: 'landmark_2', x: 45, y: 40, description: '中央电梯' }
      ]
    };
  },

  /**
   * 保存应用状态
   */
  saveAppState() {
    try {
      const appState = {
        currentPosition: this.globalData.currentPosition,
        navigationData: this.globalData.navigationData,
        statistics: this.globalData.statistics,
        timestamp: Date.now()
      };
      
      wx.setStorageSync('app_state', appState);
      console.log('💾 应用状态已保存');
    } catch (error) {
      console.error('❌ 应用状态保存失败:', error);
    }
  },

  /**
   * 记录启动信息
   */
  recordLaunchInfo(options) {
    const launchInfo = {
      scene: options.scene,
      path: options.path,
      query: options.query,
      timestamp: Date.now()
    };
    
    console.log('📋 启动信息:', launchInfo);
  },

  /**
   * 记录错误信息
   */
  recordError(error) {
    const errorInfo = {
      message: error.message || error,
      stack: error.stack,
      timestamp: Date.now(),
      page: getCurrentPages().length > 0 ? getCurrentPages().pop().route : 'unknown'
    };
    
    // 保存到本地
    try {
      let errors = wx.getStorageSync('error_logs') || [];
      errors.push(errorInfo);
      
      // 只保留最近50条错误
      if (errors.length > 50) {
        errors = errors.slice(-50);
      }
      
      wx.setStorageSync('error_logs', errors);
    } catch (e) {
      console.error('错误日志保存失败:', e);
    }
  },

  /**
   * 从错误中恢复
   */
  recoverFromError(error) {
    // 尝试重新初始化惯导库
    if (!this.globalData.isNavInitialized) {
      setTimeout(() => {
        this.initInertialNavigation();
      }, 2000);
    }
  },

  // ==================== 传感器管理方法 ====================
  
  /**
   * 清理传感器状态
   */
  cleanupSensorState() {
    console.log('🧹 清理可能残留的传感器状态...');
    
    try {
      // 尝试停止所有可能运行的传感器
      wx.stopAccelerometer({
        success: () => console.log('✅ 加速计已停止'),
        fail: () => console.log('ℹ️ 加速计停止失败（可能未启动）')
      });
      
      wx.stopGyroscope({
        success: () => console.log('✅ 陀螺仪已停止'),
        fail: () => console.log('ℹ️ 陀螺仪停止失败（可能未启动）')
      });
      
      wx.stopCompass({
        success: () => console.log('✅ 罗盘已停止'),
        fail: () => console.log('ℹ️ 罗盘停止失败（可能未启动）')
      });
    } catch (error) {
      console.log('⚠️ 传感器状态清理异常:', error);
    }
  },

  // ==================== 数据验证方法 ====================
  
  /**
   * 验证位置数据是否有效
   * @param {Object} position - 位置数据
   * @returns {boolean} 是否有效
   */
  isValidPosition(position) {
    if (!position || typeof position !== 'object') {
      return false;
    }
    
    // 检查必需的坐标字段
    if (position.x === null || position.x === undefined ||
        position.y === null || position.y === undefined) {
      return false;
    }
    
    // 检查数据类型
    if (typeof position.x !== 'number' || typeof position.y !== 'number') {
      return false;
    }
    
    // 检查NaN和Infinity
    if (isNaN(position.x) || isNaN(position.y) || 
        !isFinite(position.x) || !isFinite(position.y)) {
      return false;
    }
    
    // Z坐标可选，但如果存在也需要验证
    if (position.z !== undefined && position.z !== null) {
      if (typeof position.z !== 'number' || isNaN(position.z) || !isFinite(position.z)) {
        return false;
      }
    }
    
    // 检查合理的坐标范围（可根据实际场景调整）
    const MAX_COORDINATE = 10000; // 10km范围
    if (Math.abs(position.x) > MAX_COORDINATE || 
        Math.abs(position.y) > MAX_COORDINATE) {
      return false;
    }
    
    return true;
  },
  
  /**
   * 检查位置变化是否合理，防止位置跳变
   * @param {Object} newPosition - 新位置
   * @returns {boolean} 变化是否合理
   */
  isReasonablePositionChange(newPosition) {
    const currentPos = this.globalData.currentPosition;
    
    // 如果是第一次位置更新，直接接受
    if (!currentPos || currentPos.timestamp === 0) {
      console.log('📍 首次位置更新，直接接受:', newPosition);
      return true;
    }
    
    // 如果新位置和当前位置完全相同，接受（避免误判静止状态）
    if (newPosition.x === currentPos.x && 
        newPosition.y === currentPos.y && 
        newPosition.z === currentPos.z) {
      return true;
    }
    
    // 计算位置变化距离
    const dx = newPosition.x - currentPos.x;
    const dy = newPosition.y - currentPos.y;
    const distance = Math.sqrt(dx * dx + dy * dy);
    
    // 计算时间间隔（秒）
    const timeInterval = (Date.now() - currentPos.timestamp) / 1000;
    
    // 如果时间间隔太小，忽略微小变化
    if (timeInterval < 0.1 && distance < 0.01) {
      return false;
    }
    
    // 特殊处理：如果当前位置是初始位置(0,0,0)，放宽限制
    const isCurrentInitial = (currentPos.x === 0 && currentPos.y === 0 && currentPos.z === 0);
    const isNewInitial = (newPosition.x === 0 && newPosition.y === 0 && newPosition.z === 0);
    
    if (isCurrentInitial || isNewInitial) {
      console.log('📍 涉及初始位置的更新，放宽检查:', { current: currentPos, new: newPosition });
      // 对于涉及初始位置的更新，只检查极端跳变
      if (distance > 1000) { // 1公里
        console.warn(`⚠️ 极端位置跳变: ${distance.toFixed(2)}m`);
        return false;
      }
      return true;
    }
    
    // 检查移动速度是否合理（最大10m/s，约36km/h）
    const MAX_SPEED = 10; // m/s
    if (timeInterval > 0) {
      const speed = distance / timeInterval;
      if (speed > MAX_SPEED) {
        console.warn(`⚠️ 移动速度异常: ${speed.toFixed(2)}m/s, 距离: ${distance.toFixed(2)}m, 时间: ${timeInterval.toFixed(2)}s`);
        return false;
      }
    }
    
    // 检查单次跳变距离（最大50米）
    const MAX_JUMP_DISTANCE = 50;
    if (distance > MAX_JUMP_DISTANCE) {
      console.warn(`⚠️ 位置跳变过大: ${distance.toFixed(2)}m`);
      return false;
    }
    
    return true;
  },

  // ==================== 公共API方法 ====================

  /**
   * 启动导航
   */
  async startNavigation() {
    if (!this.globalData.isNavInitialized) {
      throw new Error('惯导定位库未初始化');
    }
    
    try {
      const success = await this.globalData.inertialNav.start();
      if (success) {
        this.globalData.isNavigating = true;
        console.log('🚀 导航已启动');
        return true;
      }
      return false;
    } catch (error) {
      console.error('❌ 导航启动失败:', error);
      throw error;
    }
  },

  /**
   * 停止导航
   */
  stopNavigation() {
    if (this.globalData.inertialNav) {
      this.globalData.inertialNav.stop();
    }
    
    this.globalData.isNavigating = false;
    this.globalData.navigationData.isRouteActive = false;
    
    console.log('⏹️ 导航已停止');
  },

  /**
   * 设置导航路线
   */
  setNavigationRoute(startPoint, endPoint) {
    // 简单的直线路径规划
    const route = this.planRoute(startPoint, endPoint);
    
    this.globalData.navigationData = {
      startPoint: startPoint,
      endPoint: endPoint,
      route: route,
      currentStep: 0,
      remainingDistance: this.calculateRemainingDistance(startPoint, { route: route, currentStep: 0 }),
      isRouteActive: true
    };
    
    console.log('🗺️ 导航路线已设置:', route);
    return route;
  },

  /**
   * 简单路径规划
   */
  planRoute(start, end) {
    // 这里实现简单的直线路径，实际应用中可以实现更复杂的路径规划算法
    return [start, end];
  },

  /**
   * 获取应用状态
   */
  getAppStatus() {
    return {
      isNavInitialized: this.globalData.isNavInitialized,
      isNavigating: this.globalData.isNavigating,
      currentPosition: this.globalData.currentPosition,
      navigationData: this.globalData.navigationData,
      statistics: this.globalData.statistics
    };
  }
});