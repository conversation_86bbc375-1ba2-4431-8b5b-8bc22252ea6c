# 室内导航小程序

基于微信小程序惯导定位库的室内导航应用，提供高精度的室内定位和导航功能。

## ✨ 功能特性

### 🧭 核心定位功能
- **多模式定位**：lite（轻量）、standard（标准）、precise（高精度）
- **传感器融合**：加速度计 + 陀螺仪 + 磁力计
- **步态检测**：智能识别用户行走模式
- **航向估计**：准确的方向角计算
- **MLA校正**：磁场辅助定位校正

### 🗺️ 地图功能
- **室内地图显示**：多楼层支持
- **实时位置显示**：用户位置和航向
- **POI搜索**：兴趣点查找和导航
- **测量工具**：距离测量功能
- **轨迹记录**：运动轨迹可视化

### 🧭 导航功能
- **路径规划**：智能路径计算
- **实时导航**：语音+文字指引
- **目标选择**：多种目标点类型
- **导航统计**：步数、距离、时间

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <project-url>
cd navigation-app

# 检查文件结构
ls -la
```

### 2. 微信开发者工具

1. 打开微信开发者工具
2. 导入项目（选择navigation-app目录）
3. 配置AppID（可使用测试号）
4. 确保权限设置正确

### 3. 真机测试

1. 点击"真机调试"
2. 微信扫码打开
3. 允许位置和传感器权限
4. 进入地图页面测试定位

## 📱 页面介绍

### 导航页面 (`pages/navigation/`)
- 主导航界面
- 目标选择和路径规划
- 实时导航指引

### 地图页面 (`pages/map/`)
- 室内地图显示
- 位置跟踪控制
- POI搜索和标记
- 测量工具

### 历史页面 (`pages/history/`)
- 导航历史记录
- 轨迹回放
- 统计信息

### 设置页面 (`pages/settings/`)
- 定位模式选择
- 传感器校准
- 个人偏好设置

## 🔧 核心配置

### 惯导库配置 (`app.js`)

```javascript
// 初始化惯导定位库
this.globalData.inertialNav = new WXInertialNavigation({
  initialPosition: { x: 0, y: 0, z: 0 },    // 初始位置
  mode: 'standard',                          // 运行模式
  sampleRate: 50,                           // 采样率 (Hz)
  
  calibration: {
    stepLength: 0.75,                       // 步长 (米)
    magneticDeclination: 0                  // 磁偏角 (度)
  },
  
  fusion: {
    adaptiveWeighting: true,                // 自适应权重
    confidenceThreshold: 0.4                // 置信度阈值
  }
});
```

### 运行模式对比

| 模式 | 精度 | 功耗 | 特点 |
|------|------|------|------|
| lite | 中等 | 低 | 省电模式，适合长时间使用 |
| standard | 高 | 中 | 平衡模式，推荐日常使用 |
| precise | 最高 | 高 | 高精度模式，适合精确导航 |

## 🧪 测试指南

### 基础测试

```bash
# 运行集成测试
node test-integration.js
```

### 真机测试

详细测试流程请参考 [TESTING_GUIDE.md](./TESTING_GUIDE.md)

1. **权限测试**：确保获取传感器权限
2. **定位测试**：测试启停和精度
3. **导航测试**：测试路径规划和指引
4. **性能测试**：监控电量和内存

## 📊 API 参考

### App 全局方法

```javascript
// 启动导航
await app.startNavigation()

// 停止导航  
app.stopNavigation()

// 获取应用状态
const status = app.getAppStatus()

// 设置导航路线
app.setNavigationRoute(startPoint, endPoint)
```

### 惯导库方法

```javascript
// 获取当前位置
const location = nav.getCurrentLocation()

// 获取轨迹数据
const trajectory = nav.getTrajectory()

// 获取性能统计
const stats = nav.getStatistics()

// 导出数据
const data = nav.exportData('json')
```

## 🔍 故障排查

### 常见问题

**Q: 定位无法启动**
```
A: 检查权限设置，确保传感器权限已授予
```

**Q: 位置精度很差**
```
A: 1. 重新校准磁力计
   2. 避免磁场干扰
   3. 尝试切换到precise模式
```

**Q: 耗电严重**
```
A: 1. 切换到lite模式
   2. 降低采样率
   3. 及时停止不需要的定位
```

### 调试技巧

1. **查看控制台日志**：关注错误和警告信息
2. **导出轨迹数据**：分析定位精度
3. **监控性能指标**：观察CPU和内存使用

## 📁 项目结构

```
navigation-app/
├── app.js                 # 小程序入口
├── app.json               # 小程序配置
├── lib/                   # 惯导定位库
│   ├── WXInertialNavigation.js
│   └── core/              # 核心组件
├── pages/                 # 页面文件
│   ├── navigation/        # 导航页面
│   ├── map/              # 地图页面
│   ├── history/          # 历史页面
│   └── settings/         # 设置页面
├── components/           # 自定义组件
│   └── indoor-map/       # 室内地图组件
├── assets/               # 静态资源
├── utils/                # 工具函数
└── test-integration.js   # 集成测试
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 技术支持

- **文档**：查看 [TESTING_GUIDE.md](./TESTING_GUIDE.md)
- **示例**：参考 lib/example/ 目录
- **问题反馈**：提交 Issue
- **技术交流**：联系 PDR Team

---

## 🎯 下一步开发

- [ ] 增加更多POI类型支持
- [ ] 优化地图渲染性能
- [ ] 添加语音导航功能
- [ ] 支持多建筑物导航
- [ ] 集成更多传感器数据
- [ ] 开发轨迹分享功能

**注意**：本项目专注于室内导航场景，在室外环境下建议结合GPS定位使用。