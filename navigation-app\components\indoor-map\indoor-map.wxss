/* 室内地图组件样式 */

.map-container {
  position: relative;
  background: #f8f9fa;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 地图Canvas */
.map-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

/* 地图控制器 */
.map-controls {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  z-index: 10;
}

/* 缩放控制 */
.zoom-controls {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.zoom-button {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.zoom-icon {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 定位控制 */
.location-controls {
  display: flex;
  flex-direction: column;
}

.location-button {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.9);
  backdrop-filter: blur(10rpx);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(102, 126, 234, 0.3);
}

.location-icon {
  font-size: 28rpx;
  color: white;
}

/* 楼层选择器 */
.floor-selector {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border-radius: 10rpx;
  padding: 15rpx;
  min-width: 120rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.floor-title {
  font-size: 22rpx;
  color: #666;
  text-align: center;
  margin-bottom: 10rpx;
}

.floor-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.floor-button {
  width: 50rpx;
  height: 50rpx;
  border-radius: 8rpx;
  background: #f0f0f0;
  color: #666;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
}

.floor-button.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

/* 地图信息面板 */
.map-info {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border-radius: 10rpx;
  padding: 15rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 22rpx;
  color: #666;
  margin-right: 15rpx;
}

.info-value {
  font-size: 22rpx;
  color: #333;
  font-weight: bold;
  font-family: monospace;
}

/* 图例 */
.map-legend {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border-radius: 10rpx;
  padding: 15rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
  max-width: 200rpx;
}

.legend-title {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  text-align: center;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.legend-symbol {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
  flex-shrink: 0;
}

.legend-symbol.user-position {
  background: #2196F3;
  border: 2rpx solid white;
}

.legend-symbol.navigation-route {
  background: #667eea;
  border-radius: 4rpx;
  height: 6rpx;
}

.legend-symbol.poi-entrance,
.legend-symbol.poi-elevator,
.legend-symbol.poi-meeting,
.legend-symbol.poi-restroom {
  background: none;
  border: none;
  font-size: 20rpx;
}

.legend-text {
  font-size: 20rpx;
  color: #666;
  flex: 1;
}

/* 加载状态 */
.map-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(248, 249, 250, 0.9);
  z-index: 100;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f0f0f0;
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 320px) {
  .map-controls {
    top: 10rpx;
    right: 10rpx;
  }
  
  .zoom-button,
  .location-button {
    width: 50rpx;
    height: 50rpx;
  }
  
  .zoom-icon {
    font-size: 28rpx;
  }
  
  .location-icon {
    font-size: 24rpx;
  }
  
  .floor-selector {
    padding: 10rpx;
    min-width: 100rpx;
  }
  
  .floor-button {
    width: 40rpx;
    height: 40rpx;
    font-size: 18rpx;
  }
  
  .map-info,
  .map-legend {
    bottom: 10rpx;
    left: 10rpx;
    padding: 10rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .map-container {
    background: #2c2c2c;
  }
  
  .zoom-button,
  .floor-selector,
  .map-info,
  .map-legend {
    background: rgba(60, 60, 60, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .zoom-icon,
  .info-value,
  .legend-title {
    color: #f0f0f0;
  }
  
  .floor-title,
  .info-label,
  .legend-text,
  .loading-text {
    color: #ccc;
  }
  
  .floor-button {
    background: #4a4a4a;
    color: #ccc;
    border-color: #666;
  }
  
  .floor-button.active {
    background: #667eea;
    color: white;
  }
}