declare namespace mlArrayMax {
  export interface ArrayMaxOptions {
    /**
     * Start index (inclusive) for the slice within which we look for the maximum.
     */
    fromIndex?: number;
    /**
     * End index (exclusive) for the slice within which we look for the maximum
     */
    toIndex?: number;
  }
}

/**
 * Computes the maximum of the given values.
 */
declare function mlArrayMax(
  array: ArrayLike<number>,
  options?: mlArrayMax.ArrayMaxOptions,
): number;

export = mlArrayMax;
