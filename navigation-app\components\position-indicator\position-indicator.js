/**
 * 位置指示器组件
 * 在地图上显示用户当前位置、航向角和置信度
 */

Component({
  properties: {
    // 位置坐标
    position: {
      type: Object,
      value: { x: 0, y: 0, z: 0 }
    },
    
    // 航向角（度）
    heading: {
      type: Number,
      value: 0
    },
    
    // 位置置信度 (0-1)
    confidence: {
      type: Number,
      value: 0
    },
    
    // 是否显示
    visible: {
      type: Boolean,
      value: true
    },
    
    // 指示器大小
    size: {
      type: String,
      value: 'normal' // small, normal, large
    },
    
    // 是否显示轨迹
    showTrail: {
      type: Boolean,
      value: false
    },
    
    // 是否显示置信度圆圈
    showConfidenceCircle: {
      type: Boolean,
      value: true
    }
  },

  data: {
    // 动画状态
    isAnimating: false,
    
    // 上次位置
    lastPosition: null,
    
    // 移动方向
    movementDirection: 0
  },

  observers: {
    'position': function(newPosition) {
      if (this.data.lastPosition) {
        this.updateMovementDirection(this.data.lastPosition, newPosition);
      }
      this.setData({
        lastPosition: newPosition
      });
    }
  },

  methods: {
    /**
     * 更新移动方向
     */
    updateMovementDirection(oldPos, newPos) {
      const dx = newPos.x - oldPos.x;
      const dy = newPos.y - oldPos.y;
      
      if (Math.abs(dx) > 0.1 || Math.abs(dy) > 0.1) {
        const direction = Math.atan2(dy, dx) * 180 / Math.PI;
        this.setData({
          movementDirection: direction,
          isAnimating: true
        });
        
        // 动画结束后重置状态
        setTimeout(() => {
          this.setData({ isAnimating: false });
        }, 500);
      }
    },

    /**
     * 获取指示器样式类名
     */
    getIndicatorClass() {
      const { size, confidence } = this.properties;
      let className = `position-indicator ${size}`;
      
      // 根据置信度添加样式
      if (confidence > 0.8) {
        className += ' high-confidence';
      } else if (confidence > 0.5) {
        className += ' medium-confidence';
      } else {
        className += ' low-confidence';
      }
      
      // 动画状态
      if (this.data.isAnimating) {
        className += ' moving';
      }
      
      return className;
    },

    /**
     * 获取置信度圆圈半径
     */
    getConfidenceRadius() {
      const baseRadius = this.properties.size === 'large' ? 40 : 
                        this.properties.size === 'small' ? 20 : 30;
      
      // 置信度越低，圆圈越大
      const confidenceMultiplier = 1 + (1 - this.properties.confidence) * 2;
      return baseRadius * confidenceMultiplier;
    },

    /**
     * 获取置信度颜色
     */
    getConfidenceColor() {
      const confidence = this.properties.confidence;
      
      if (confidence > 0.8) {
        return '#28a745'; // 绿色 - 高置信度
      } else if (confidence > 0.5) {
        return '#ffc107'; // 黄色 - 中等置信度
      } else {
        return '#dc3545'; // 红色 - 低置信度
      }
    },

    /**
     * 点击指示器
     */
    onIndicatorTap() {
      this.triggerEvent('positionTap', {
        position: this.properties.position,
        heading: this.properties.heading,
        confidence: this.properties.confidence
      });
    },

    /**
     * 长按指示器
     */
    onIndicatorLongPress() {
      this.triggerEvent('positionLongPress', {
        position: this.properties.position,
        heading: this.properties.heading,
        confidence: this.properties.confidence
      });
    }
  },

  lifetimes: {
    attached() {
      console.log('📍 位置指示器组件已挂载');
    },
    
    detached() {
      console.log('📍 位置指示器组件已卸载');
    }
  }
});