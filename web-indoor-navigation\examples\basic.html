<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web室内惯导定位 - 演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
            background: #000;
            color: #fff;
            height: 100vh;
            overflow: hidden;
            position: fixed;
            width: 100%;
            top: 0;
            left: 0;
        }
        
        .navigation-container {
            height: 100vh;
            width: 100vw;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        /* 顶部信息栏 - 移动端优化 */
        .top-info-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            padding: env(safe-area-inset-top, 20px) 16px 12px 16px;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .heading-display {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(0, 120, 255, 0.2);
            padding: 6px 12px;
            border-radius: 16px;
            border: 1px solid rgba(0, 120, 255, 0.3);
            font-size: 14px;
            font-weight: 600;
        }
        
        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #00ff88;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }
        
        .position-display {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        /* 主地图区域 */
        .map-container {
            flex: 1;
            position: relative;
            background: #111;
            width: 100%;
            height: 100%;
        }
        
        /* 底部控制栏 - 移动端优化，增加高度容纳两行统计 */
        .bottom-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            padding: 16px 16px calc(env(safe-area-inset-bottom, 12px) + 4px) 16px;
            z-index: 1000;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            min-height: 120px; /* 确保容纳两行统计信息 */
        }
        
        .main-controls {
            display: flex;
            justify-content: center;
            gap: 12px;
            margin-bottom: 8px;
        }
        
        .nav-button {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
            min-width: 60px;
            justify-content: center;
            transition: all 0.2s ease;
            -webkit-tap-highlight-color: transparent;
        }
        
        .nav-button:active {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(0.95);
        }
        
        .nav-button:disabled {
            opacity: 0.4;
            pointer-events: none;
        }
        
        .nav-button.primary {
            background: rgba(0, 255, 136, 0.2);
            border-color: rgba(0, 255, 136, 0.4);
            color: #00ff88;
        }
        
        .nav-button.danger {
            background: rgba(255, 69, 58, 0.2);
            border-color: rgba(255, 69, 58, 0.4);
            color: #ff453a;
        }
        
        .stats-row {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 8px;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.6);
            text-align: center;
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .stat-value {
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            font-size: 11px;
        }
        
        /* 渐进模式状态样式 - 移动端优化 */
        .progressive-stats {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .progressive-stats .stat-value {
            font-size: 12px;
            font-weight: 700;
            color: rgba(255, 255, 255, 0.95);
        }
        
        /* 移动端优化 */
        @media (max-width: 768px) {
            .progressive-stats {
                font-size: 12px;
            }
            
            .progressive-stats .stat-value {
                font-size: 14px;
                font-weight: 800;
            }
            
            .progressive-stats .stat-item {
                min-height: 35px;
            }
        }
        
        /* 右侧控制面板 - 移动端优化 */
        .side-panel {
            position: absolute;
            top: calc(env(safe-area-inset-top, 20px) + 60px);
            right: 12px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            z-index: 999;
        }
        
        .panel-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            color: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.2s ease;
            -webkit-tap-highlight-color: transparent;
        }
        
        .panel-button:active {
            background: rgba(255, 255, 255, 0.1);
            transform: scale(0.9);
        }
        
        /* 全屏地图样式 */
        .trajectory-canvas {
            display: block;
            background: #111;
            width: 100%;
            height: 100%;
            touch-action: pan-x pan-y;
        }
        
        /* 移动端特定优化 */
        @media (max-width: 768px) {
            .heading-display {
                font-size: 13px;
                padding: 4px 10px;
            }
            
            .position-display {
                font-size: 11px;
            }
            
            .nav-button {
                padding: 6px 12px;
                font-size: 12px;
                min-width: 50px;
            }
            
            .main-controls {
                gap: 8px;
            }
            
            .panel-button {
                width: 36px;
                height: 36px;
                font-size: 14px;
            }
            
            .stats-row {
                font-size: 9px;
                gap: 4px;
            }
            
            .stat-value {
                font-size: 10px;
            }
        }
        
        /* 高DPI屏幕优化 */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 2dppx) {
            .trajectory-canvas {
                image-rendering: -webkit-optimize-contrast;
                image-rendering: crisp-edges;
            }
        }
        
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
        
        * {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
    </style>
</head>
<body>
    <div class="navigation-container">
        <!-- 顶部信息栏 -->
        <div class="top-info-bar">
            <div class="heading-display">
                <div class="status-dot" id="statusIndicator"></div>
                <span id="heading">0°</span>
            </div>
            <div class="position-display">
                <span id="posX">0.00</span>, <span id="posY">0.00</span>m
            </div>
        </div>
        
        <!-- 右侧控制面板 -->
        <div class="side-panel">
            <div class="panel-button" id="zoomInBtn" title="放大">+</div>
            <div class="panel-button" id="zoomOutBtn" title="缩小">-</div>
            <div class="panel-button" id="centerViewBtn" title="居中">⊙</div>
            <div class="panel-button" id="resetViewBtn" title="重置">⌂</div>
        </div>
        
        <!-- 主地图区域 -->
        <div class="map-container">
            <canvas id="trajectoryCanvas" class="trajectory-canvas"></canvas>
        </div>
        
        <!-- 底部控制栏 -->
        <div class="bottom-controls">
            <div class="main-controls">
                <button class="nav-button primary" id="startBtn">启动</button>
                <button class="nav-button danger" id="stopBtn" disabled>停止</button>
                <button class="nav-button" id="resetBtn">重置</button>
                <button class="nav-button" id="clearTrajectoryBtn">清空</button>
                <button class="nav-button" id="calibrateBtn">校准</button>
            </div>
            <div class="stats-row">
                <div class="stat-item">
                    <div class="stat-value" id="steps">0</div>
                    <div>步数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalDistance">0.00</div>
                    <div>距离</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="runtime">0s</div>
                    <div>时长</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="trajectoryPoints">0</div>
                    <div>轨迹</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="confidence">0%</div>
                    <div>精度</div>
                </div>
            </div>
            <!-- 渐进模式状态显示 - 移动端优化 -->
            <div class="stats-row progressive-stats" style="margin-top: 8px;">
                <div class="stat-item">
                    <div class="stat-value" id="progressiveMode">快速</div>
                    <div>模式</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="progressiveSteps">0</div>
                    <div>稳定步</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="progressiveSuccess">0%</div>
                    <div>成功率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="progressiveThreshold">0.08</div>
                    <div>阈值</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="nextTransition">3步</div>
                    <div>升级</div>
                </div>
            </div>
        </div>
        
        <!-- 隐藏的警告信息 -->
        <div id="permissionWarning" style="display:none;"></div>
        <div id="mobileWarning" style="display:none;"></div>
    </div>

    <!-- 引入室内定位库 -->
    <script type="module">
        import InertialNavigationWeb from '../src/InertialNavigationWeb.js';
        import AutoModeManager from '../src/AutoModeManager.js';

        // 全局变量
        let navigation = null;
        let startTime = null;
        let updateTimer = null;

        // Canvas 相关变量
        let canvas, ctx;
        let canvasWidth, canvasHeight;
        
        // 动态计算Canvas尺寸（全屏）
        function calculateCanvasSize() {
            return {
                width: window.innerWidth,
                height: window.innerHeight
            };
        }
        
        // 视图控制变量
        let viewState = {
            offsetX: 0,
            offsetY: 0,
            scale: 50,              // 像素/米比例
            zoom: 1.0,              // 缩放倍数
            centerX: canvasWidth / 2,
            centerY: canvasHeight / 2,
            mapRotation: 0,         // 地图旋转角度（弧度）- 保持上北下南
            isDragging: false,
            lastMouseX: 0,
            lastMouseY: 0
        };

        // 绝对航向角状态
        let absoluteHeading = {
            current: 0,             // 当前绝对航向角（度）
            isAbsolute: false,      // 是否为绝对方向
            source: 'unknown',      // 数据来源
            lastUpdateTime: 0,      // 上次更新时间
            stabilityCount: 0,      // 稳定性计数器
            stableThreshold: 5      // 需要连续5次相同状态才确认切换
        };

        // 地图旋转平滑过渡
        let mapRotationSmooth = {
            target: 0,              // 目标旋转角度
            current: 0,             // 当前旋转角度  
            smoothing: 0.1,         // 平滑系数(0-1)
            threshold: 0.017        // 最小变化阈值(1度)
        };

        // 轨迹数据
        const trajectoryData = [];

        // DOM 元素
        const elements = {
            // 按钮
            startBtn: document.getElementById('startBtn'),
            stopBtn: document.getElementById('stopBtn'),
            resetBtn: document.getElementById('resetBtn'),
            clearTrajectoryBtn: document.getElementById('clearTrajectoryBtn'),
            calibrateBtn: document.getElementById('calibrateBtn'),
            
            // 显示元素
            statusIndicator: document.getElementById('statusIndicator'),
            permissionWarning: document.getElementById('permissionWarning'),
            
            // 位置数据
            posX: document.getElementById('posX'),
            posY: document.getElementById('posY'),
            heading: document.getElementById('heading'),
            steps: document.getElementById('steps'),
            confidence: document.getElementById('confidence'),
            
            // 统计数据
            runtime: document.getElementById('runtime'),
            totalDistance: document.getElementById('totalDistance'),
            trajectoryPoints: document.getElementById('trajectoryPoints'),
            
            // Canvas 控制
            zoomInBtn: document.getElementById('zoomInBtn'),
            zoomOutBtn: document.getElementById('zoomOutBtn'),
            resetViewBtn: document.getElementById('resetViewBtn'),
            centerViewBtn: document.getElementById('centerViewBtn'),
            
            // 渐进模式状态
            progressiveMode: document.getElementById('progressiveMode'),
            progressiveSteps: document.getElementById('progressiveSteps'),
            progressiveSuccess: document.getElementById('progressiveSuccess'),
            progressiveThreshold: document.getElementById('progressiveThreshold'),
            nextTransition: document.getElementById('nextTransition')
        };

        // 页面加载后初始化
        document.addEventListener('DOMContentLoaded', async () => {
            await initializeApp();
        });

        // 初始化应用
        async function initializeApp() {
            try {
                console.log('🔄 初始化Web室内惯导定位系统...');
                
                // 检查传感器支持
                if (!checkSensorSupport()) {
                    throw new Error('浏览器不支持必要的传感器API');
                }

                // 创建惯导实例
                navigation = new InertialNavigationWeb({
                    initialPosition: { x: 0, y: 0, z: 0 },
                    mode: 'standard',
                    sampleRate: 50,
                    enableMLA: false,
                    calibration: {
                        stepLength: 0.65,
                        magneticDeclination: 0,
                        needsAbsoluteHeading: true,  // 启用绝对航向角
                        hasAbsoluteOrientationSupport: false  // 将在运行时检测
                    }
                });

                // 设置回调函数
                setupCallbacks();
                
                // 绑定事件监听器
                bindEventListeners();
                
                // 初始化画布
                initCanvas();
                
                // 添加窗口大小变化监听器
                window.addEventListener('resize', () => {
                    setTimeout(() => {
                        console.log('📱 窗口大小变化，重新调整Canvas');
                        resizeCanvas();
                        drawTrajectory();
                    }, 100); // 延迟确保DOM更新完成
                });
                
                // 添加屏幕方向变化监听器
                window.addEventListener('orientationchange', () => {
                    setTimeout(() => {
                        console.log('🔄 屏幕方向变化，重新调整Canvas');
                        resizeCanvas();
                        drawTrajectory();
                    }, 200);
                });
                
                // 检测移动设备并显示相应提示
                checkMobileDevice();
                
                console.log('✅ 室内定位系统初始化成功');
                
            } catch (error) {
                console.error('❌ 初始化失败:', error);
                showError('初始化失败: ' + error.message);
            }
        }

        // 检查传感器支持
        function checkSensorSupport() {
            return typeof DeviceMotionEvent !== 'undefined' && 
                   typeof DeviceOrientationEvent !== 'undefined';
        }
        
        // 检测移动设备
        function checkMobileDevice() {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                           window.innerWidth <= 768 ||
                           'ontouchstart' in window;
            
            const mobileWarning = document.getElementById('mobileWarning');
            if (isMobile && mobileWarning) {
                mobileWarning.style.display = 'block';
                console.log('📱 检测到移动设备，显示移动端提示');
            }
            
            return isMobile;
        }

        // 设置回调函数
        function setupCallbacks() {
            navigation.onPositionUpdate = (location) => {
                console.log('📍 位置更新:', location);
                updateDisplay(location);
                updateTrajectory(location.position);
                
                // 调试：显示轨迹更新频率
                if (!window.trajectoryUpdateCount) window.trajectoryUpdateCount = 0;
                window.trajectoryUpdateCount++;
                if (window.trajectoryUpdateCount % 5 === 0) {
                    console.log(`🎯 轨迹更新 #${window.trajectoryUpdateCount}:`, {
                        位置: `(${location.position.x.toFixed(2)}, ${location.position.y.toFixed(2)})`,
                        航向: `${location.heading.toFixed(1)}°`,
                        步数: location.stepCount,
                        置信度: `${Math.round(location.confidence * 100)}%`
                    });
                }
            };

            navigation.onStepDetected = (stepInfo) => {
                console.log('👣 检测到步态:', stepInfo);
            };

            navigation.onError = (error) => {
                console.error('❌ 定位错误:', error);
                if (error.type === 'sensor_error') {
                    elements.permissionWarning.style.display = 'block';
                }
                showError('定位错误: ' + error.message);
            };
        }

        // 绑定事件监听器
        function bindEventListeners() {
            // 导航按钮
            elements.startBtn.addEventListener('click', startNavigation);
            elements.stopBtn.addEventListener('click', stopNavigation);
            elements.resetBtn.addEventListener('click', resetPosition);
            elements.clearTrajectoryBtn.addEventListener('click', clearTrajectory);
            elements.calibrateBtn.addEventListener('click', calibrateSensors);
            
            // Canvas 控制按钮
            elements.zoomInBtn.addEventListener('click', () => zoomView(1.2));
            elements.zoomOutBtn.addEventListener('click', () => zoomView(0.8));
            elements.resetViewBtn.addEventListener('click', resetView);
            elements.centerViewBtn.addEventListener('click', centerOnCurrentPosition);
            
            // Canvas 拖拽和缩放
            setupCanvasInteraction();
        }

        // 设置Canvas交互
        function setupCanvasInteraction() {
            // 鼠标事件
            canvas.addEventListener('mousedown', handleMouseDown);
            canvas.addEventListener('mousemove', handleMouseMove);
            canvas.addEventListener('mouseup', handleMouseUp);
            canvas.addEventListener('mouseleave', handleMouseUp);
            
            // 滚轮缩放
            canvas.addEventListener('wheel', handleWheel, { passive: false });
            
            // 触摸事件（移动设备）
            canvas.addEventListener('touchstart', handleTouchStart, { passive: false });
            canvas.addEventListener('touchmove', handleTouchMove, { passive: false });
            canvas.addEventListener('touchend', handleTouchEnd);
        }

        // 鼠标按下
        function handleMouseDown(e) {
            viewState.isDragging = true;
            const rect = canvas.getBoundingClientRect();
            viewState.lastMouseX = e.clientX - rect.left;
            viewState.lastMouseY = e.clientY - rect.top;
            elements.canvasContainer.style.cursor = 'grabbing';
        }

        // 鼠标移动
        function handleMouseMove(e) {
            if (!viewState.isDragging) return;
            
            const rect = canvas.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;
            
            const deltaX = mouseX - viewState.lastMouseX;
            const deltaY = mouseY - viewState.lastMouseY;
            
            viewState.offsetX += deltaX;
            viewState.offsetY += deltaY;
            
            viewState.lastMouseX = mouseX;
            viewState.lastMouseY = mouseY;
            
            drawTrajectory();
        }

        // 鼠标释放
        function handleMouseUp(e) {
            viewState.isDragging = false;
            elements.canvasContainer.style.cursor = 'grab';
        }

        // 滚轮缩放
        function handleWheel(e) {
            e.preventDefault();
            const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
            
            const rect = canvas.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;
            
            zoomViewAtPoint(zoomFactor, mouseX, mouseY);
        }

        // 触摸开始
        function handleTouchStart(e) {
            e.preventDefault();
            if (e.touches.length === 1) {
                const touch = e.touches[0];
                const rect = canvas.getBoundingClientRect();
                viewState.isDragging = true;
                viewState.lastMouseX = touch.clientX - rect.left;
                viewState.lastMouseY = touch.clientY - rect.top;
            }
        }

        // 触摸移动
        function handleTouchMove(e) {
            e.preventDefault();
            if (e.touches.length === 1 && viewState.isDragging) {
                const touch = e.touches[0];
                const rect = canvas.getBoundingClientRect();
                const touchX = touch.clientX - rect.left;
                const touchY = touch.clientY - rect.top;
                
                const deltaX = touchX - viewState.lastMouseX;
                const deltaY = touchY - viewState.lastMouseY;
                
                viewState.offsetX += deltaX;
                viewState.offsetY += deltaY;
                
                viewState.lastMouseX = touchX;
                viewState.lastMouseY = touchY;
                
                drawTrajectory();
            }
        }

        // 触摸结束
        function handleTouchEnd(e) {
            e.preventDefault();
            viewState.isDragging = false;
        }

        // 启动导航
        async function startNavigation() {
            try {
                showStatus('正在启动...', 'running');
                elements.startBtn.disabled = true;
                
                const success = await navigation.start();
                
                if (success) {
                    showStatus('运行中', 'running');
                    elements.startBtn.disabled = true;
                    elements.stopBtn.disabled = false;
                    startTime = Date.now();
                    
                    // 启动定时器更新显示
                    updateTimer = setInterval(updateStatistics, 1000);
                    
                    // 启动地图旋转平滑动画
                    setInterval(updateMapRotationAnimation, 16); // ~60fps
                    
                } else {
                    throw new Error('启动失败');
                }
                
            } catch (error) {
                console.error('❌ 启动失败:', error);
                showError('启动失败: ' + error.message);
                showStatus('启动失败', 'error');
                elements.startBtn.disabled = false;
            }
        }

        // 停止导航
        function stopNavigation() {
            try {
                navigation.stop();
                showStatus('已停止', 'stopped');
                
                elements.startBtn.disabled = false;
                elements.stopBtn.disabled = true;
                
                if (updateTimer) {
                    clearInterval(updateTimer);
                    updateTimer = null;
                }
                
            } catch (error) {
                console.error('❌ 停止失败:', error);
                showError('停止失败: ' + error.message);
            }
        }

        // 重置位置
        function resetPosition() {
            if (navigation) {
                navigation.reset();
                // 注意：不清空轨迹数据，保持历史记录
                initCanvas();
                updateDisplay({
                    position: { x: 0, y: 0, z: 0 },
                    heading: 0,
                    velocity: 0,
                    stepCount: 0,
                    confidence: 0
                });
                console.log('🔄 位置已重置');
            }
        }

        // 清空轨迹
        function clearTrajectory() {
            trajectoryData.length = 0;
            drawTrajectory();
            elements.trajectoryPoints.textContent = '0';
            console.log('🧹 轨迹已清空');
        }

        // 传感器校准
        async function calibrateSensors() {
            try {
                if (!navigation) {
                    throw new Error('导航系统未初始化');
                }
                
                console.log('🔧 开始传感器校准...');
                showStatus('校准中...', 'running');
                elements.calibrateBtn.disabled = true;
                
                // 模拟校准过程
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                const result = await navigation.calibrate({
                    stepLength: 0.65,
                    magneticDeclination: 0
                });
                
                console.log('📋 校准结果:', result);
                
                if (result && result.success) {
                    console.log('✅ 传感器校准成功:', result.message);
                    showStatus('校准成功', 'running');
                    
                    // 短暂延迟后恢复运行状态
                    setTimeout(() => {
                        if (navigation && navigation.isRunning && navigation.isRunning()) {
                            showStatus('运行中', 'running');
                        } else {
                            showStatus('已停止', 'stopped');
                        }
                    }, 2000);
                } else {
                    throw new Error(result?.message || '校准失败');
                }
                
            } catch (error) {
                console.error('❌ 校准失败:', error);
                showError('校准失败: ' + error.message);
                showStatus('校准失败', 'error');
            } finally {
                elements.calibrateBtn.disabled = false;
                console.log('🔧 校准过程结束');
            }
        }

        // 初始化画布
        function initCanvas() {
            console.log('🖼️ 初始化Canvas...');
            canvas = document.getElementById('trajectoryCanvas');
            if (!canvas) {
                console.error('❌ Canvas元素未找到');
                return;
            }
            
            ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('❌ Canvas上下文获取失败');
                return;
            }
            
            // 动态设置Canvas尺寸
            resizeCanvas();
            
            console.log('✅ Canvas初始化成功, 尺寸:', canvasWidth, 'x', canvasHeight);
            updateZoomDisplay();
            drawTrajectory();
        }
        
        // 调整Canvas尺寸
        function resizeCanvas() {
            const size = calculateCanvasSize();
            canvasWidth = size.width;
            canvasHeight = size.height;
            
            if (canvas) {
                canvas.width = canvasWidth;
                canvas.height = canvasHeight;
                canvas.style.width = '100%';
                canvas.style.height = '100%';
                
                // 重新设置视图中心点
                viewState.centerX = canvasWidth / 2;
                viewState.centerY = canvasHeight / 2;
                
                console.log('📏 Canvas尺寸已调整:', canvasWidth, 'x', canvasHeight);
            }
        }

        // 更新显示
        function updateDisplay(location) {
            elements.posX.textContent = location.position.x.toFixed(2);
            elements.posY.textContent = location.position.y.toFixed(2);
            
            // 更新航向角显示，只显示绝对航向角
            updateHeadingDisplay(location);
            
            elements.steps.textContent = location.stepCount || 0;
            elements.confidence.textContent = Math.round((location.confidence || 0) * 100) + '%';
        }

        // 更新航向角显示（只显示绝对航向角）
        function updateHeadingDisplay(location) {
            let displayHeading = 0;
            let hasAbsoluteData = false;
            
            // 强制只获取绝对航向角数据
            if (navigation && navigation.sensorManager) {
                const sensorStatus = navigation.sensorManager.getStatus();
                const magnetometerData = sensorStatus.sensors?.magnetometer?.lastData;
                
                // 只有在确实是绝对方向时才更新显示
                if (magnetometerData && magnetometerData.isAbsolute && magnetometerData.direction !== undefined) {
                    displayHeading = magnetometerData.direction;
                    hasAbsoluteData = true;
                    
                    // 更新内部状态
                    absoluteHeading.current = displayHeading;
                    absoluteHeading.isAbsolute = true;
                    absoluteHeading.source = magnetometerData.source;
                    absoluteHeading.lastUpdateTime = Date.now();
                    
                    // 更新地图旋转
                    updateMapRotationSmooth(displayHeading);
                    
                    console.log('🧭 绝对航向角更新:', {
                        角度: displayHeading.toFixed(1) + '°',
                        来源: magnetometerData.source
                    });
                }
            }
            
            // 只显示绝对航向角，如果没有绝对数据则显示等待状态
            if (hasAbsoluteData) {
                elements.heading.textContent = `${Math.round(displayHeading)}°`;
                // 更新状态指示为绿色（有绝对数据）
                elements.statusIndicator.style.background = '#00ff88';
            } else {
                elements.heading.textContent = '等待GPS...';
                // 更新状态指示为橙色（等待绝对数据）
                elements.statusIndicator.style.background = '#ffaa00';
            }
        }

        // 平滑更新地图旋转角度
        function updateMapRotationSmooth(targetHeading) {
            // 将航向角转换为目标旋转角度（保持上北下南）
            // heading: 0° = 北，90° = 东，180° = 南，270° = 西
            // 地图需要反向旋转以保持北方始终向上
            const targetRotation = -targetHeading * Math.PI / 180; // 转换为弧度并反向
            
            // 处理角度跨越边界的情况
            let angleDiff = targetRotation - mapRotationSmooth.current;
            if (angleDiff > Math.PI) angleDiff -= 2 * Math.PI;
            if (angleDiff < -Math.PI) angleDiff += 2 * Math.PI;
            
            mapRotationSmooth.target = mapRotationSmooth.current + angleDiff;
            
            console.log('🗺️ 地图旋转目标更新:', {
                航向角: targetHeading.toFixed(1) + '°',
                目标旋转: (mapRotationSmooth.target * 180 / Math.PI).toFixed(1) + '°',
                角度差: (angleDiff * 180 / Math.PI).toFixed(1) + '°'
            });
        }

        // 平滑动画循环
        function updateMapRotationAnimation() {
            const diff = mapRotationSmooth.target - mapRotationSmooth.current;
            
            if (Math.abs(diff) > mapRotationSmooth.threshold) {
                mapRotationSmooth.current += diff * mapRotationSmooth.smoothing;
                viewState.mapRotation = mapRotationSmooth.current;
                
                // 重绘地图
                if (ctx) {
                    drawTrajectory();
                }
            } else {
                // 到达目标，停止动画
                mapRotationSmooth.current = mapRotationSmooth.target;
                viewState.mapRotation = mapRotationSmooth.current;
            }
        }


        // 更新轨迹
        function updateTrajectory(position) {
            console.log('🔄 更新轨迹:', position);
            
            // 获取当前渐进模式状态
            let progressiveMode = 'quick'; // 默认快速模式
            let confidence = 0.5;
            try {
                if (navigation && navigation.positionCalculator) {
                    const progressiveState = navigation.positionCalculator.getProgressiveState();
                    if (progressiveState) {
                        progressiveMode = progressiveState.currentMode;
                        confidence = progressiveState.successRate / 100;
                        console.log(`🎨 轨迹模式获取: ${progressiveMode}, 步数: ${progressiveState.stableStepCount}, 成功率: ${progressiveState.successRate}%`);
                    } else {
                        console.warn('⚠️ progressiveState为空');
                    }
                } else {
                    console.warn('⚠️ navigation或positionCalculator未初始化');
                }
            } catch (error) {
                console.warn('获取渐进模式状态失败:', error);
            }
            
            // 添加新位置到轨迹数据（包含模式信息）
            trajectoryData.push({ 
                x: position.x, 
                y: position.y, 
                timestamp: Date.now(),
                mode: progressiveMode,      // 添加模式信息
                confidence: confidence      // 添加置信度信息
            });
            
            console.log(`🔗 轨迹点添加: 位置(${position.x.toFixed(2)}, ${position.y.toFixed(2)}), 模式=${progressiveMode}, 轨迹总数=${trajectoryData.length}`);
            
            // 轨迹点数量管理（保留更多历史轨迹，仅在内存不足时清理最旧的点）
            if (trajectoryData.length > 5000) { // 增加到5000个点
                // 只删除最旧的1000个点，保留大部分历史
                trajectoryData.splice(0, 1000);
                console.log('🧹 清理了1000个最旧的轨迹点，保留最近4000个点');
            }
            
            console.log('📊 轨迹点数量:', trajectoryData.length);
            elements.trajectoryPoints.textContent = trajectoryData.length.toString();
            
            // 确保Canvas已初始化
            if (!ctx) {
                console.warn('⚠️ Canvas未初始化，重新初始化');
                initCanvas();
                return;
            }
            
            drawTrajectory();
        }

        // 绘制分段彩色轨迹
        function drawColoredTrajectory() {
            // 渐进模式颜色映射
            const modeColors = {
                'quick': '#ff6b35',      // 橙红色 - 快速模式
                'balanced': '#4dabf7',   // 蓝色 - 平衡模式  
                'precise': '#51cf66'     // 绿色 - 精确模式
            };
            
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            // 统计各模式的轨迹点数量
            const modeStats = { quick: 0, balanced: 0, precise: 0 };
            trajectoryData.forEach(p => {
                const mode = p.mode || 'quick';
                if (modeStats[mode] !== undefined) modeStats[mode]++;
            });
            
            console.log(`🎨 绘制轨迹: 总点数=${trajectoryData.length}, 快速=${modeStats.quick}, 平衡=${modeStats.balanced}, 精确=${modeStats.precise}`);
            
            // 按模式分组绘制
            let currentMode = null;
            let currentPath = [];
            
            for (let i = 0; i < trajectoryData.length; i++) {
                const point = trajectoryData[i];
                const screenPos = worldToScreen(point.x, point.y);
                
                // 检查模式是否变化
                const pointMode = point.mode || 'quick';
                
                if (currentMode !== pointMode) {
                    // 绘制之前的路径段
                    if (currentPath.length > 1) {
                        drawPathSegment(currentPath, modeColors[currentMode] || '#007bff', currentMode);
                    }
                    
                    // 开始新的路径段
                    currentMode = pointMode;
                    currentPath = [screenPos];
                } else {
                    currentPath.push(screenPos);
                }
            }
            
            // 绘制最后一个路径段
            if (currentPath.length > 1) {
                drawPathSegment(currentPath, modeColors[currentMode] || '#007bff', currentMode);
            }
            
            // 绘制模式切换点
            drawModeTransitionPoints();
        }
        
        // 绘制路径段
        function drawPathSegment(pathPoints, color, mode) {
            if (pathPoints.length < 2) return;
            
            // 根据模式设置线宽
            const lineWidths = {
                'quick': 2,      // 快速模式：细线
                'balanced': 3,   // 平衡模式：中等
                'precise': 4     // 精确模式：粗线
            };
            
            ctx.strokeStyle = color;
            ctx.lineWidth = lineWidths[mode] || 3;
            ctx.globalAlpha = 0.8;
            
            ctx.beginPath();
            let pathStarted = false;
            
            for (let i = 0; i < pathPoints.length; i++) {
                const screenPos = pathPoints[i];
                
                if (isPointVisible(screenPos.x, screenPos.y)) {
                    if (!pathStarted) {
                        ctx.moveTo(screenPos.x, screenPos.y);
                        pathStarted = true;
                    } else {
                        ctx.lineTo(screenPos.x, screenPos.y);
                    }
                } else if (pathStarted) {
                    // 如果点不可见，结束当前路径段
                    ctx.stroke();
                    ctx.beginPath();
                    pathStarted = false;
                }
            }
            
            if (pathStarted) {
                ctx.stroke();
            }
            
            ctx.globalAlpha = 1.0; // 恢复透明度
        }
        
        // 绘制模式切换点
        function drawModeTransitionPoints() {
            let lastMode = null;
            
            for (let i = 0; i < trajectoryData.length; i++) {
                const point = trajectoryData[i];
                const currentMode = point.mode || 'quick';
                
                // 检测模式切换
                if (lastMode && lastMode !== currentMode) {
                    const screenPos = worldToScreen(point.x, point.y);
                    
                    if (isPointVisible(screenPos.x, screenPos.y)) {
                        // 绘制切换点标记
                        ctx.fillStyle = '#ffffff';
                        ctx.strokeStyle = '#333333';
                        ctx.lineWidth = 2;
                        
                        ctx.beginPath();
                        ctx.arc(screenPos.x, screenPos.y, 6, 0, 2 * Math.PI);
                        ctx.fill();
                        ctx.stroke();
                        
                        // 添加模式标签
                        const modeLabels = {
                            'quick': 'Q',
                            'balanced': 'B', 
                            'precise': 'P'
                        };
                        
                        ctx.fillStyle = '#333333';
                        ctx.font = '10px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(modeLabels[currentMode] || '?', screenPos.x, screenPos.y + 3);
                    }
                }
                
                lastMode = currentMode;
            }
        }

        // 绘制轨迹
        function drawTrajectory() {
            if (!ctx) {
                console.warn('⚠️ Canvas上下文不存在，无法绘制');
                return;
            }
            
            console.log('🎨 绘制轨迹，数据点:', trajectoryData.length);
            
            // 清空画布
            ctx.clearRect(0, 0, canvasWidth, canvasHeight);
            
            // 绘制网格
            drawGrid();
            
            if (trajectoryData.length === 0) {
                console.log('📈 暂无轨迹数据');
                return;
            }
            
            // 绘制起始点
            if (trajectoryData.length > 0) {
                const startPoint = trajectoryData[0];
                const startScreenPos = worldToScreen(startPoint.x, startPoint.y);
                
                if (isPointVisible(startScreenPos.x, startScreenPos.y)) {
                    ctx.fillStyle = '#28a745';
                    ctx.beginPath();
                    ctx.arc(startScreenPos.x, startScreenPos.y, 8, 0, 2 * Math.PI);
                    ctx.fill();
                    
                    // 起始点标签
                    ctx.fillStyle = '#000';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('起点', startScreenPos.x, startScreenPos.y - 12);
                }
            }
            
            // 绘制分段彩色轨迹线
            if (trajectoryData.length >= 2) {
                drawColoredTrajectory();
            }
            
            // 绘制当前位置和方向箭头
            if (trajectoryData.length > 0) {
                const currentPos = trajectoryData[trajectoryData.length - 1];
                const currentScreenPos = worldToScreen(currentPos.x, currentPos.y);
                
                if (isPointVisible(currentScreenPos.x, currentScreenPos.y)) {
                    // 当前位置圆点
                    ctx.fillStyle = '#dc3545';
                    ctx.beginPath();
                    ctx.arc(currentScreenPos.x, currentScreenPos.y, 8, 0, 2 * Math.PI);
                    ctx.fill();
                    
                    // 方向箭头 - 基于真实运动方向
                    const movementDirection = calculateMovementDirection();
                    drawDirectionArrow(currentScreenPos.x, currentScreenPos.y, movementDirection);
                }
            }
        }

        // 计算真实运动方向
        function calculateMovementDirection() {
            if (trajectoryData.length < 2) {
                // 如果轨迹点不足，使用导航系统的航向
                return navigation ? navigation.getCurrentLocation().heading : 0;
            }
            
            // 取最近的几个点计算平均运动方向，减少抖动
            const recentPointCount = Math.min(5, trajectoryData.length);
            const recentPoints = trajectoryData.slice(-recentPointCount);
            
            if (recentPoints.length < 2) {
                return navigation ? navigation.getCurrentLocation().heading : 0;
            }
            
            // 计算从第一个点到最后一个点的方向
            const startPoint = recentPoints[0];
            const endPoint = recentPoints[recentPoints.length - 1];
            
            const deltaX = endPoint.x - startPoint.x;
            const deltaY = endPoint.y - startPoint.y;
            
            // 检查是否有显著移动
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            if (distance < 0.1) {
                // 移动距离太小，使用导航系统的航向
                return navigation ? navigation.getCurrentLocation().heading : 0;
            }
            
            // 计算运动方向角度（弧度转角度）
            // 注意：使用atan2(deltaY, deltaX)得到数学角度（0° = 东，逆时针为正）
            let movementAngle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;
            
            // 确保角度在0-360度范围内
            if (movementAngle < 0) {
                movementAngle += 360;
            }
            
            return movementAngle;
        }

        // 绘制方向箭头（支持绝对航向角和地图旋转）
        function drawDirectionArrow(x, y, heading) {
            const arrowLength = 25;
            const arrowWidth = 8;
            
            // 确定实际的航向角
            let actualHeading = heading;
            
            // 如果有绝对航向角且正在使用绝对方向，使用绝对航向角
            if (absoluteHeading.isAbsolute) {
                actualHeading = absoluteHeading.current;
                
                // 绘制绝对航向角（红色箭头 - 指向真北方向）
                drawAbsoluteDirectionArrow(x, y, actualHeading);
                
                // 绘制运动方向（蓝色箭头 - 指向实际移动方向）
                drawMovementDirectionArrow(x, y, heading);
                
                return;
            }
            
            // 降级：只绘制相对运动方向
            drawSingleDirectionArrow(x, y, actualHeading, '#dc3545');
        }

        // 绘制绝对方向箭头（指向真北）
        function drawAbsoluteDirectionArrow(x, y, heading) {
            // 绝对航向角：0° = 北，90° = 东，180° = 南，270° = 西
            // 转换为屏幕坐标系：0° = 北（向上），90° = 东（向右）
            const angleRad = ((90 - heading) * Math.PI) / 180; // 修正坐标系
            
            drawSingleDirectionArrow(x, y, angleRad, '#ff4757', true); // 红色，表示绝对方向
            
            // 添加"N"标记
            ctx.fillStyle = '#ff4757';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('N', x + Math.cos(angleRad) * 35, y - Math.sin(angleRad) * 35);
        }

        // 绘制运动方向箭头
        function drawMovementDirectionArrow(x, y, movementHeading) {
            // 运动方向基于轨迹计算
            const angleRad = (movementHeading * Math.PI) / 180;
            drawSingleDirectionArrow(x, y, angleRad, '#2196f3', false); // 蓝色，表示运动方向
        }

        // 绘制单个方向箭头的通用函数
        function drawSingleDirectionArrow(x, y, angleRad, color, isAbsolute = false) {
            const arrowLength = isAbsolute ? 30 : 25;
            const arrowWidth = 8;
            
            // 计算箭头端点
            const endX = x + Math.cos(angleRad) * arrowLength;
            const endY = y - Math.sin(angleRad) * arrowLength; // Canvas Y轴向下，取负号
            
            // 绘制箭头主线
            ctx.strokeStyle = color;
            ctx.lineWidth = isAbsolute ? 4 : 3;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(endX, endY);
            ctx.stroke();
            
            // 绘制箭头头部
            const arrowHeadAngle = Math.PI / 6; // 30度
            const backAngle = angleRad + Math.PI; // 反向180度
            
            // 左侧和右侧箭头线
            const leftAngle = backAngle - arrowHeadAngle;
            const leftX = endX + Math.cos(leftAngle) * arrowWidth;
            const leftY = endY - Math.sin(leftAngle) * arrowWidth;
            
            const rightAngle = backAngle + arrowHeadAngle;
            const rightX = endX + Math.cos(rightAngle) * arrowWidth;
            const rightY = endY - Math.sin(rightAngle) * arrowWidth;
            
            // 绘制箭头头部
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(leftX, leftY);
            ctx.moveTo(endX, endY);
            ctx.lineTo(rightX, rightY);
            ctx.stroke();
        }

        // 绘制网格
        function drawGrid() {
            const effectiveScale = viewState.scale * viewState.zoom;
            
            ctx.strokeStyle = '#e0e0e0';
            ctx.lineWidth = 1;
            
            // 网格间距（米）
            let gridSpacing = 1;
            if (effectiveScale < 20) gridSpacing = 5;
            else if (effectiveScale < 10) gridSpacing = 10;
            
            const gridPixelSpacing = gridSpacing * effectiveScale;
            
            // 计算网格起始位置
            const startX = -(viewState.offsetX % gridPixelSpacing);
            const startY = -(viewState.offsetY % gridPixelSpacing);
            
            ctx.beginPath();
            
            // 垂直线
            for (let x = startX; x <= canvasWidth; x += gridPixelSpacing) {
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvasHeight);
            }
            
            // 水平线
            for (let y = startY; y <= canvasHeight; y += gridPixelSpacing) {
                ctx.moveTo(0, y);
                ctx.lineTo(canvasWidth, y);
            }
            
            ctx.stroke();
            
            // 绘制坐标轴
            ctx.strokeStyle = '#007bff';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            const originScreen = worldToScreen(0, 0);
            
            // Y轴
            if (originScreen.x >= 0 && originScreen.x <= canvasWidth) {
                ctx.moveTo(originScreen.x, 0);
                ctx.lineTo(originScreen.x, canvasHeight);
            }
            
            // X轴
            if (originScreen.y >= 0 && originScreen.y <= canvasHeight) {
                ctx.moveTo(0, originScreen.y);
                ctx.lineTo(canvasWidth, originScreen.y);
            }
            
            ctx.stroke();
            
            // 绘制北方指示器（右上角固定位置）
            drawNorthIndicator();
            
            // 绘制渐进模式图例（左上角）
            drawProgressiveLegend();
        }

        // 绘制北方指示器
        function drawNorthIndicator() {
            const indicatorX = canvasWidth - 60;
            const indicatorY = 60;
            const radius = 20;
            
            // 背景圆
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(indicatorX, indicatorY, radius, 0, 2 * Math.PI);
            ctx.fill();
            ctx.stroke();
            
            // 计算北方方向在屏幕上的角度
            let northAngle = 0; // 默认北方向上
            
            if (absoluteHeading.isAbsolute) {
                // 如果有绝对航向角，计算真北在地图旋转后的方向
                northAngle = -viewState.mapRotation; // 地图旋转的反方向就是北方
            }
            
            // 绘制北方箭头
            const arrowLength = radius * 0.7;
            const northX = indicatorX + Math.cos(northAngle - Math.PI / 2) * arrowLength;
            const northY = indicatorY + Math.sin(northAngle - Math.PI / 2) * arrowLength;
            
            ctx.strokeStyle = '#ff4757';
            ctx.lineWidth = 3;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(indicatorX, indicatorY);
            ctx.lineTo(northX, northY);
            ctx.stroke();
            
            // 箭头头部
            const arrowHeadAngle = Math.PI / 6;
            const baseAngle = northAngle - Math.PI / 2 + Math.PI;
            
            const leftX = northX + Math.cos(baseAngle - arrowHeadAngle) * 6;
            const leftY = northY + Math.sin(baseAngle - arrowHeadAngle) * 6;
            const rightX = northX + Math.cos(baseAngle + arrowHeadAngle) * 6;
            const rightY = northY + Math.sin(baseAngle + arrowHeadAngle) * 6;
            
            ctx.beginPath();
            ctx.moveTo(northX, northY);
            ctx.lineTo(leftX, leftY);
            ctx.moveTo(northX, northY);
            ctx.lineTo(rightX, rightY);
            ctx.stroke();
            
            // "N" 标记
            ctx.fillStyle = '#ff4757';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('N', northX, northY - 15);
            
            // 指示器状态文字
            ctx.fillStyle = '#666';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(absoluteHeading.isAbsolute ? '绝对' : '相对', indicatorX, indicatorY + radius + 15);
        }
        
        // 绘制渐进模式图例
        function drawProgressiveLegend() {
            const legendX = 20;
            const legendY = 80;
            const legendWidth = 120;
            const legendHeight = 80;
            
            // 背景
            ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
            ctx.fillRect(legendX, legendY, legendWidth, legendHeight);
            
            // 边框
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            ctx.strokeRect(legendX, legendY, legendWidth, legendHeight);
            
            // 标题
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('定位模式', legendX + legendWidth/2, legendY + 15);
            
            // 图例项
            const legendItems = [
                { mode: 'quick', color: '#ff6b35', label: '快速模式', width: 2 },
                { mode: 'balanced', color: '#4dabf7', label: '平衡模式', width: 3 },
                { mode: 'precise', color: '#51cf66', label: '精确模式', width: 4 }
            ];
            
            legendItems.forEach((item, index) => {
                const itemY = legendY + 25 + index * 16;
                
                // 绘制颜色线条
                ctx.strokeStyle = item.color;
                ctx.lineWidth = item.width;
                ctx.lineCap = 'round';
                ctx.beginPath();
                ctx.moveTo(legendX + 10, itemY);
                ctx.lineTo(legendX + 30, itemY);
                ctx.stroke();
                
                // 绘制标签
                ctx.fillStyle = '#fff';
                ctx.font = '10px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(item.label, legendX + 35, itemY + 3);
            });
        }

        // 世界坐标转屏幕坐标（支持地图旋转）
        function worldToScreen(worldX, worldY) {
            const effectiveScale = viewState.scale * viewState.zoom;
            
            // 应用地图旋转
            if (viewState.mapRotation !== 0) {
                const cos = Math.cos(viewState.mapRotation);
                const sin = Math.sin(viewState.mapRotation);
                
                // 旋转坐标
                const rotatedX = worldX * cos - worldY * sin;
                const rotatedY = worldX * sin + worldY * cos;
                
                return {
                    x: viewState.centerX + rotatedX * effectiveScale + viewState.offsetX,
                    y: viewState.centerY - rotatedY * effectiveScale + viewState.offsetY // Y轴翻转
                };
            }
            
            return {
                x: viewState.centerX + worldX * effectiveScale + viewState.offsetX,
                y: viewState.centerY - worldY * effectiveScale + viewState.offsetY // Y轴翻转
            };
        }

        // 屏幕坐标转世界坐标（支持地图旋转）
        function screenToWorld(screenX, screenY) {
            const effectiveScale = viewState.scale * viewState.zoom;
            
            // 逆向变换
            const x = (screenX - viewState.centerX - viewState.offsetX) / effectiveScale;
            const y = -(screenY - viewState.centerY - viewState.offsetY) / effectiveScale; // Y轴翻转
            
            // 应用反向旋转
            if (viewState.mapRotation !== 0) {
                const cos = Math.cos(-viewState.mapRotation); // 反向旋转
                const sin = Math.sin(-viewState.mapRotation);
                
                return {
                    x: x * cos - y * sin,
                    y: x * sin + y * cos
                };
            }
            
            return { x, y };
        }

        // 检查点是否在可见区域内
        function isPointVisible(screenX, screenY, margin = 50) {
            return screenX >= -margin && screenX <= canvasWidth + margin &&
                   screenY >= -margin && screenY <= canvasHeight + margin;
        }

        // 缩放视图
        function zoomView(factor) {
            const newZoom = viewState.zoom * factor;
            if (newZoom >= 0.1 && newZoom <= 10) {
                viewState.zoom = newZoom;
                updateZoomDisplay();
                drawTrajectory();
            }
        }

        // 在指定点缩放
        function zoomViewAtPoint(factor, pointX, pointY) {
            const newZoom = viewState.zoom * factor;
            if (newZoom >= 0.1 && newZoom <= 10) {
                // 计算缩放中心
                const centerX = pointX - viewState.centerX - viewState.offsetX;
                const centerY = pointY - viewState.centerY - viewState.offsetY;
                
                // 应用缩放
                const scaleFactor = factor - 1;
                viewState.offsetX -= centerX * scaleFactor;
                viewState.offsetY -= centerY * scaleFactor;
                viewState.zoom = newZoom;
                
                updateZoomDisplay();
                drawTrajectory();
            }
        }

        // 重置视图
        function resetView() {
            viewState.offsetX = 0;
            viewState.offsetY = 0;
            viewState.zoom = 1.0;
            updateZoomDisplay();
            drawTrajectory();
        }

        // 居中到当前位置
        function centerOnCurrentPosition() {
            if (trajectoryData.length > 0) {
                const currentPos = trajectoryData[trajectoryData.length - 1];
                const targetScreenPos = worldToScreen(currentPos.x, currentPos.y);
                
                viewState.offsetX += viewState.centerX - targetScreenPos.x;
                viewState.offsetY += viewState.centerY - targetScreenPos.y;
                
                drawTrajectory();
            }
        }

        // 更新缩放显示（在控制台显示）
        function updateZoomDisplay() {
            console.log('🔍 当前缩放:', Math.round(viewState.zoom * 100) + '%');
        }

        // 更新统计信息
        function updateStatistics() {
            if (!startTime) return;
            
            const runtime = Math.floor((Date.now() - startTime) / 1000);
            elements.runtime.textContent = formatTime(runtime);
            
            if (navigation) {
                const stats = navigation.getStatistics();
                if (stats) {
                    elements.totalDistance.textContent = (stats.totalDistance || 0).toFixed(2);
                }
                
                // 更新渐进模式状态
                updateProgressiveStatus();
            }
        }

        // 格式化时间
        function formatTime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            
            if (hours > 0) {
                return `${hours}h ${minutes}m ${secs}s`;
            } else if (minutes > 0) {
                return `${minutes}m ${secs}s`;
            } else {
                return `${secs}s`;
            }
        }
        
        // 更新渐进模式状态显示
        function updateProgressiveStatus() {
            if (!navigation || !navigation.positionCalculator) return;
            
            try {
                const progressiveState = navigation.positionCalculator.getProgressiveState();
                
                if (progressiveState) {
                    // 模式名称的中文映射
                    const modeNames = {
                        'quick': '快速',
                        'balanced': '平衡', 
                        'precise': '精确'
                    };
                    
                    // 更新显示
                    elements.progressiveMode.textContent = modeNames[progressiveState.currentMode] || progressiveState.currentMode;
                    elements.progressiveSteps.textContent = progressiveState.stableStepCount.toString();
                    elements.progressiveSuccess.textContent = progressiveState.successRate + '%';
                    elements.progressiveThreshold.textContent = progressiveState.currentThreshold.toFixed(2);
                    
                    // 下次转换信息
                    if (progressiveState.nextTransition.stepsRemaining > 0) {
                        elements.nextTransition.textContent = progressiveState.nextTransition.stepsRemaining + '步';
                    } else {
                        elements.nextTransition.textContent = '已满级';
                    }
                    
                    // 根据模式设置颜色
                    const modeColors = {
                        'quick': '#ffaa00',     // 橙色
                        'balanced': '#00aaff',  // 蓝色
                        'precise': '#00ff88'    // 绿色
                    };
                    const color = modeColors[progressiveState.currentMode] || '#ffffff';
                    elements.progressiveMode.style.color = color;
                }
            } catch (error) {
                console.warn('获取渐进模式状态失败:', error);
            }
        }

        // 显示状态
        function showStatus(message, type) {
            // 更新状态指示器颜色
            if (type === 'running') {
                elements.statusIndicator.style.background = '#00ff88';
            } else if (type === 'error') {
                elements.statusIndicator.style.background = '#ff453a';
            } else {
                elements.statusIndicator.style.background = '#666';
            }
            console.log('📍 状态更新:', message);
        }

        // 显示错误
        function showError(message) {
            console.error(message);
            // 可以在这里添加更多的错误显示逻辑
        }

    </script>
</body>
</html>