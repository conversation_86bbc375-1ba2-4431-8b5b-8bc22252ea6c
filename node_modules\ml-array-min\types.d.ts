declare namespace mlArrayMin {
  export interface ArrayMinOptions {
    /**
     * Start index (inclusive) for the slice within which we look for the minimum.
     */
    fromIndex?: number;
    /**
     * End index (exclusive) for the slice within which we look for the minimum
     */
    toIndex?: number;
  }
}

/**
 * Computes the minimum of the given values.
 */
declare function mlArrayMin(
  array: ArrayLike<number>,
  options?: mlArrayMin.ArrayMinOptions,
): number;

export = mlArrayMin;
