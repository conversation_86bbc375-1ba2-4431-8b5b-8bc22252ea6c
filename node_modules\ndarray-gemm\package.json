{"name": "n<PERSON><PERSON>-gemm", "version": "1.0.0", "description": "Matrix multiplication for ndarrays", "main": "gemm.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"dup": "^1.0.0", "ndarray": "^1.0.18", "ndarray-ops": "^1.2.2", "tape": "^4.0.0", "zeros": "^1.0.0", "numeric": "^1.2.6"}, "scripts": {"test": "tape test/*.js"}, "repository": {"type": "git", "url": "git://github.com/scijs/ndarray-gemm.git"}, "keywords": ["scijs", "matrix", "multiply", "n<PERSON><PERSON>", "blas", "level", "3", "general", "vector", "outer", "inner", "product", "linear", "algebra", "numerical", "methods"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/scijs/ndarray-gemm/issues"}}