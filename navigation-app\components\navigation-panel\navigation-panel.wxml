<!--导航面板组件-->
<view class="navigation-panel" wx:if="{{isNavigating}}">
  
  <!-- 主导航信息 -->
  <view class="nav-main-info" wx:if="{{showPanel}}">
    
    <!-- 方向箭头 -->
    <view class="direction-arrow-container">
      <view class="direction-arrow" style="transform: rotate({{arrowAngle}}deg)">
        <text class="arrow-icon">↑</text>
      </view>
    </view>
    
    <!-- 导航文字信息 -->
    <view class="nav-text-info">
      <view class="nav-instruction">
        <text class="instruction-text">{{currentInstruction || getDirectionText()}}</text>
      </view>
      
      <view class="nav-distance">
        <text class="distance-label">剩余距离</text>
        <text class="distance-value">{{formatDistance(remainingDistance)}}</text>
      </view>
      
      <view class="nav-time" wx:if="{{estimatedTime > 0}}">
        <text class="time-label">预计时间</text>
        <text class="time-value">{{formatTime(estimatedTime)}}</text>
      </view>
    </view>
  </view>
  
  <!-- 导航控制按钮 -->
  <view class="nav-controls">
    
    <!-- 折叠/展开按钮 -->
    <button class="control-btn toggle-btn" bindtap="togglePanel" size="mini">
      <text class="btn-icon">{{showPanel ? '▼' : '▲'}}</text>
    </button>
    
    <!-- 语音播报按钮 -->
    <button class="control-btn voice-btn" bindtap="speakInstruction" size="mini">
      <text class="btn-icon">🔊</text>
    </button>
    
    <!-- 重新规划按钮 -->
    <button class="control-btn replan-btn" bindtap="replanRoute" size="mini">
      <text class="btn-icon">🔄</text>
    </button>
    
    <!-- 停止导航按钮 -->
    <button class="control-btn stop-btn" bindtap="stopNavigation" size="mini">
      <text class="btn-icon">⏹️</text>
    </button>
  </view>
  
  <!-- 进度指示器 -->
  <view class="nav-progress" wx:if="{{showPanel && totalSteps > 0}}">
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{(currentStep / totalSteps * 100).toFixed(0)}}%"></view>
    </view>
    <text class="progress-text">{{currentStep}}/{{totalSteps}}</text>
  </view>
</view>

<!-- 简化状态（非导航时） -->
<view class="navigation-panel simple" wx:if="{{!isNavigating && targetPosition}}">
  <view class="simple-info">
    <text class="simple-text">点击开始导航</text>
    <text class="simple-distance">{{formatDistance(remainingDistance)}}</text>
  </view>
</view>