# PDR演示页面使用说明

## 问题修复

### ✅ 解决了构造函数参数缺失问题
- **问题**: WXInertialNavigation构造函数需要必需的`initialPosition`参数
- **解决**: 在所有实例化和方法调用中正确提供了初始位置配置

### ✅ 解决了回调方法不存在问题
- **问题**: `onPositionUpdate`方法不存在，导致回调设置失败
- **解决**: 使用正确的`setCallbacks`方法和`onLocationUpdate`回调

### ✅ 简化了页面结构
- **移除**: 删除了不需要的导航和地图页面
- **保留**: 只保留PDR演示、历史记录和设置页面

## 使用步骤

### 1. 初始化系统
1. 打开PDR演示页面（现在是默认首页）
2. 点击 **INIT** 按钮初始化PDR系统
3. 系统会自动配置传感器并设置初始位置为 (0, 0, 0)

### 2. 启动定位
1. 点击 **START** 按钮启动位置跟踪
2. 系统开始收集传感器数据并计算位置
3. 界面实时显示：
   - 方向角、步长、坐标数据
   - 步数统计和总距离
   - 实时轨迹路径

### 3. 查看数据
- **传感器数据**：左上角显示实时方向角、步长、X/Y/Z坐标
- **统计信息**：显示步数、楼层、总距离等
- **轨迹可视化**：中心区域显示移动路径和当前位置

### 4. 控制功能
- **STOP**：停止位置跟踪，保留已收集的数据
- **DESTROY**：完全重置系统，清空所有数据
- **保存数据**：将轨迹数据保存到本地存储
- **导出**：查看数据统计信息

## 技术特性

### 配置参数
```javascript
{
  initialPosition: { x: 0, y: 0, z: 0 },  // 初始位置坐标
  mode: 'standard',                       // 运行模式
  sampleRate: 50,                        // 采样频率50Hz
  quality: {
    minConfidence: 0.1,                  // 最低置信度阈值
    maxSpeed: 10.0,                      // 最大速度限制
    maxJumpDistance: 100.0               // 最大跳跃距离
  }
}
```

### 支持的传感器
- ✅ 加速计（运动检测和步态分析）
- ✅ 陀螺仪（角速度测量）
- ✅ 磁力计/指南针（方向测定）

### 数据输出
- **位置坐标**: X/Y/Z三维坐标（米）
- **方向角**: 0-360度方位角
- **步长**: 实时计算的步态长度
- **运动统计**: 步数、距离、速度等

## 界面布局

```
┌─────────────────────────────────────┐
│            PDR演示                   │ ← 标题栏
├─────────────────────┬───────────────┤
│ 方向角[度]: 45.2    │     INIT      │ ← 传感器数据
│ 步长[米]:   0.65    │     START     │   & 控制按钮
│ X坐标[米]:  1.23    │     STOP      │
│ Y坐标[米]:  2.45    │   DESTROY     │
│ Z坐标[米]:  0.00    │               │
├─────────────────────┼───────────────┤
│ 步数: 15  楼层: 1   │ 保存  │ 导出  │ ← 统计信息
├─────────────────────────────────────┤
│                                     │
│        轨迹显示区域                  │ ← 坐标系和
│     ┌─────┼─────┐                   │   轨迹可视化
│     │     │     │                   │
│     ├─────┼─────┤                   │
│     │     │▲    │                   │
│     └─────┼─────┘                   │
│                                     │
├─────────────────────────────────────┤
│ 总距离: 3.45米 | 状态: 运行中        │ ← 状态信息
└─────────────────────────────────────┘
```

## 注意事项

1. **首次使用**: 确保授予小程序传感器权限
2. **测试环境**: 在真机上测试效果更好，模拟器传感器数据可能不准确
3. **移动测试**: 实际走动或轻微晃动设备以产生传感器数据
4. **数据保存**: 重要轨迹可通过"保存数据"功能持久化存储

## 调试信息

控制台会输出详细的调试日志：
- 📱 页面加载和初始化状态
- 📍 位置更新和传感器数据
- 🔧 系统启动和控制操作
- ❌ 错误信息和异常处理