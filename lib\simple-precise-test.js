#!/usr/bin/env node

/**
 * 简单的高精度模式功能验证
 */

async function testPreciseModeBasic() {
    console.log('🧪 简单高精度模式功能验证\n');
    
    try {
        // 导入真实库
        const { default: WXInertialNavigation } = await import('./WXInertialNavigation.js');
        
        // 创建高精度模式实例
        console.log('🚀 创建高精度模式实例...');
        const preciseNav = new WXInertialNavigation({
            initialPosition: { x: 0, y: 0, z: 0 },
            mode: 'precise',
            sampleRate: 100
        });
        
        console.log('✅ 高精度模式实例创建成功');
        
        // 检查配置
        console.log('\n🔍 配置检查:');
        console.log('-'.repeat(20));
        console.log('模式:', preciseNav.config.mode);
        console.log('采样率:', preciseNav.config.sampleRate);
        console.log('MLA启用:', preciseNav.config.enableMLA);
        
        // 测试基本方法
        console.log('\n🧪 方法可用性检查:');
        console.log('-'.repeat(25));
        
        const methods = ['start', 'stop', 'pause', 'getCurrentLocation', 'getStatistics', 'getTrajectory'];
        for (const method of methods) {
            const exists = typeof preciseNav[method] === 'function';
            console.log(`${method}: ${exists ? '✅' : '❌'}`);
        }
        
        // 测试启动（在模拟环境中可能不会真正启动传感器）
        console.log('\n🚀 测试启动方法...');
        try {
            const startResult = await preciseNav.start();
            console.log('启动结果:', startResult);
        } catch (error) {
            console.log('启动可能需要在真实环境中进行:', error.message);
        }
        
        // 测试位置获取
        console.log('\n📍 测试位置获取...');
        const location = preciseNav.getCurrentLocation();
        console.log('初始位置:', location);
        
        // 测试统计信息
        console.log('\n📊 测试统计信息...');
        const stats = preciseNav.getStatistics();
        console.log('初始统计:', stats);
        
        // 测试轨迹
        console.log('\n🛤️ 测试轨迹获取...');
        const trajectory = preciseNav.getTrajectory();
        console.log('轨迹点数:', trajectory.length);
        
        console.log('\n🎉 高精度模式基本功能验证完成');
        return true;
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        return false;
    }
}

// 运行测试
testPreciseModeBasic().then(success => {
    console.log('\n' + '='.repeat(50));
    console.log(success ? '✅ 所有测试通过' : '❌ 测试失败');
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('❌ 测试运行失败:', error);
    process.exit(1);
});