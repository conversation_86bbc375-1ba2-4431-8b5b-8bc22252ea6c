/**
 * 质量控制器
 * 负责传感器数据质量检测和系统状态监控
 */

class QualityController {
  constructor(config = {}) {
    this.config = {
      // 异常值检测
      outlierThreshold: config.quality?.outlierThreshold || 3.0,
      
      // 置信度控制
      minConfidence: config.quality?.minConfidence || 0.2,
      maxDeviation: config.quality?.maxDeviation || 5.0,
      
      // 一致性检查
      consistencyWindow: config.quality?.consistencyWindow || 5,
      
      // 传感器健康检查
      healthCheckInterval: config.quality?.healthCheckInterval || 5000,
      
      ...config.quality
    };
    
    // 数据质量历史
    this.qualityHistory = {
      accelerometer: [],
      gyroscope: [],
      magnetometer: [],
      overall: []
    };
    
    // 质量指标
    this.qualityMetrics = {
      accelerometerQuality: 1.0,
      gyroscopeQuality: 1.0,
      magnetometerQuality: 1.0,
      overallQuality: 1.0,
      confidenceLevel: 1.0
    };
    
    // 异常统计
    this.anomalyStats = {
      accelerometerOutliers: 0,
      gyroscopeOutliers: 0,
      magnetometerOutliers: 0,
      totalOutliers: 0,
      lastOutlierTime: 0
    };
    
    // 健康检查
    this.healthCheckTimer = null;
    this.lastDataTime = 0;
    this.dataTimeout = 2000; // 2秒无数据认为异常
    
    // 质量等级
    this.currentQualityLevel = 'excellent'; // excellent, good, fair, poor
    
    console.log('🔍 质量控制器初始化完成');
  }
  
  /**
   * 初始化质量控制器
   */
  initialize() {
    // 启动健康检查
    this.startHealthCheck();
    
    // 重置质量指标
    this.resetQualityMetrics();
    
    console.log('✅ 质量控制器已初始化');
  }
  
  /**
   * 检查传感器数据质量
   * @param {Object} sensorData - 传感器数据
   * @returns {Object} 质量检查结果
   */
  checkDataQuality(sensorData) {
    this.lastDataTime = Date.now();
    
    const qualityResult = {
      isValid: true,
      confidence: 1.0,
      issues: [],
      qualityLevel: 'excellent'
    };
    
    try {
      // 检查数据完整性
      const completenessCheck = this.checkDataCompleteness(sensorData);
      if (!completenessCheck.isValid) {
        qualityResult.isValid = false;
        qualityResult.issues.push(completenessCheck.issue);
        qualityResult.confidence *= 0.5;
      }
      
      // 检查数据范围
      const rangeCheck = this.checkDataRange(sensorData);
      if (!rangeCheck.isValid) {
        qualityResult.issues.push(rangeCheck.issue);
        qualityResult.confidence *= 0.8;
      }
      
      // 异常值检测
      const outlierCheck = this.detectOutliers(sensorData);
      if (outlierCheck.hasOutliers) {
        qualityResult.issues.push('检测到异常值');
        qualityResult.confidence *= 0.7;
        this.recordAnomalies(outlierCheck.outliers);
      }
      
      // 一致性检查
      const consistencyCheck = this.checkConsistency(sensorData);
      if (!consistencyCheck.isConsistent) {
        qualityResult.issues.push('数据一致性问题');
        qualityResult.confidence *= 0.9;
      }
      
      // 更新质量历史
      this.updateQualityHistory(sensorData, qualityResult.confidence);
      
      // 计算整体质量等级
      qualityResult.qualityLevel = this.calculateQualityLevel(qualityResult.confidence);
      this.currentQualityLevel = qualityResult.qualityLevel;
      
      // 如果置信度过低，标记为无效
      if (qualityResult.confidence < this.config.minConfidence) {
        qualityResult.isValid = false;
        qualityResult.issues.push('置信度过低');
      }
      
      return qualityResult;
      
    } catch (error) {
      console.error('数据质量检查异常:', error);
      return {
        isValid: false,
        confidence: 0,
        issues: ['质量检查异常'],
        qualityLevel: 'poor'
      };
    }
  }
  
  /**
   * 检查数据完整性
   */
  checkDataCompleteness(sensorData) {
    // 检查必需的传感器数据
    if (!sensorData.accelerometer || !sensorData.gyroscope) {
      return {
        isValid: false,
        issue: '缺少关键传感器数据'
      };
    }
    
    // 检查数据结构
    const acc = sensorData.accelerometer;
    const gyro = sensorData.gyroscope;
    
    if (typeof acc.x !== 'number' || typeof acc.y !== 'number' || typeof acc.z !== 'number') {
      return {
        isValid: false,
        issue: '加速计数据格式错误'
      };
    }
    
    if (typeof gyro.x !== 'number' || typeof gyro.y !== 'number' || typeof gyro.z !== 'number') {
      return {
        isValid: false,
        issue: '陀螺仪数据格式错误'
      };
    }
    
    return { isValid: true };
  }
  
  /**
   * 检查数据范围
   */
  checkDataRange(sensorData) {
    const issues = [];
    
    // 加速计范围检查 (一般在-4g到+4g之间)
    const acc = sensorData.accelerometer;
    const accMagnitude = Math.sqrt(acc.x * acc.x + acc.y * acc.y + acc.z * acc.z);
    if (accMagnitude > 4.0 || accMagnitude < 0.1) {
      issues.push('加速计数据超出正常范围');
    }
    
    // 陀螺仪范围检查 (一般在-10到+10 rad/s之间)
    const gyro = sensorData.gyroscope;
    if (Math.abs(gyro.x) > 10 || Math.abs(gyro.y) > 10 || Math.abs(gyro.z) > 10) {
      issues.push('陀螺仪数据超出正常范围');
    }
    
    // 检查NaN和Infinity
    const allValues = [acc.x, acc.y, acc.z, gyro.x, gyro.y, gyro.z];
    if (allValues.some(v => !isFinite(v))) {
      issues.push('数据包含无效值');
    }
    
    return {
      isValid: issues.length === 0,
      issues,
      issue: issues.join(', ')
    };
  }
  
  /**
   * 异常值检测
   */
  detectOutliers(sensorData) {
    const outliers = [];
    
    // 使用Z-score方法检测异常值
    const acc = sensorData.accelerometer;
    const gyro = sensorData.gyroscope;
    
    // 加速计异常检测
    const accHistory = this.qualityHistory.accelerometer.slice(-20); // 最近20个样本
    if (accHistory.length >= 5) {
      const accOutlier = this.detectZScoreOutlier(acc, accHistory, 'accelerometer');
      if (accOutlier) {
        outliers.push(accOutlier);
      }
    }
    
    // 陀螺仪异常检测
    const gyroHistory = this.qualityHistory.gyroscope.slice(-20);
    if (gyroHistory.length >= 5) {
      const gyroOutlier = this.detectZScoreOutlier(gyro, gyroHistory, 'gyroscope');
      if (gyroOutlier) {
        outliers.push(gyroOutlier);
      }
    }
    
    return {
      hasOutliers: outliers.length > 0,
      outliers
    };
  }
  
  /**
   * Z-score异常检测
   */
  detectZScoreOutlier(currentData, history, sensorType) {
    if (history.length < 5) return null;
    
    // 计算历史数据的均值和标准差
    const means = this.calculateMeans(history);
    const stds = this.calculateStandardDeviations(history, means);
    
    // 计算当前数据的Z-score
    const zScores = {
      x: Math.abs((currentData.x - means.x) / (stds.x || 1)),
      y: Math.abs((currentData.y - means.y) / (stds.y || 1)),
      z: Math.abs((currentData.z - means.z) / (stds.z || 1))
    };
    
    // 检查是否超过阈值
    const threshold = this.config.outlierThreshold;
    if (zScores.x > threshold || zScores.y > threshold || zScores.z > threshold) {
      return {
        sensorType,
        zScores,
        severity: Math.max(zScores.x, zScores.y, zScores.z)
      };
    }
    
    return null;
  }
  
  /**
   * 计算均值
   */
  calculateMeans(data) {
    const sums = data.reduce((acc, curr) => ({
      x: acc.x + curr.x,
      y: acc.y + curr.y,
      z: acc.z + curr.z
    }), { x: 0, y: 0, z: 0 });
    
    return {
      x: sums.x / data.length,
      y: sums.y / data.length,
      z: sums.z / data.length
    };
  }
  
  /**
   * 计算标准差
   */
  calculateStandardDeviations(data, means) {
    const variances = data.reduce((acc, curr) => ({
      x: acc.x + Math.pow(curr.x - means.x, 2),
      y: acc.y + Math.pow(curr.y - means.y, 2),
      z: acc.z + Math.pow(curr.z - means.z, 2)
    }), { x: 0, y: 0, z: 0 });
    
    return {
      x: Math.sqrt(variances.x / data.length),
      y: Math.sqrt(variances.y / data.length),
      z: Math.sqrt(variances.z / data.length)
    };
  }
  
  /**
   * 一致性检查
   */
  checkConsistency(sensorData) {
    const recentHistory = this.qualityHistory.overall.slice(-this.config.consistencyWindow);
    
    if (recentHistory.length < 3) {
      return { isConsistent: true };
    }
    
    // 检查数据变化的一致性
    const variations = this.calculateDataVariations(recentHistory);
    const threshold = 0.5; // 一致性阈值
    
    return {
      isConsistent: variations.overall < threshold,
      variations
    };
  }
  
  /**
   * 计算数据变化
   */
  calculateDataVariations(history) {
    if (history.length < 2) return { overall: 0 };
    
    let totalVariation = 0;
    for (let i = 1; i < history.length; i++) {
      const prev = history[i - 1];
      const curr = history[i];
      
      const variation = Math.sqrt(
        Math.pow(curr.x - prev.x, 2) + 
        Math.pow(curr.y - prev.y, 2) + 
        Math.pow(curr.z - prev.z, 2)
      );
      
      totalVariation += variation;
    }
    
    return {
      overall: totalVariation / (history.length - 1)
    };
  }
  
  /**
   * 记录异常
   */
  recordAnomalies(outliers) {
    outliers.forEach(outlier => {
      switch (outlier.sensorType) {
        case 'accelerometer':
          this.anomalyStats.accelerometerOutliers++;
          break;
        case 'gyroscope':
          this.anomalyStats.gyroscopeOutliers++;
          break;
        case 'magnetometer':
          this.anomalyStats.magnetometerOutliers++;
          break;
      }
    });
    
    this.anomalyStats.totalOutliers++;
    this.anomalyStats.lastOutlierTime = Date.now();
  }
  
  /**
   * 更新质量历史
   */
  updateQualityHistory(sensorData, confidence) {
    const timestamp = Date.now();
    
    // 更新各传感器质量历史
    if (sensorData.accelerometer) {
      this.qualityHistory.accelerometer.push({
        ...sensorData.accelerometer,
        timestamp,
        confidence
      });
      
      // 限制历史长度
      if (this.qualityHistory.accelerometer.length > 100) {
        this.qualityHistory.accelerometer.shift();
      }
    }
    
    if (sensorData.gyroscope) {
      this.qualityHistory.gyroscope.push({
        ...sensorData.gyroscope,
        timestamp,
        confidence
      });
      
      if (this.qualityHistory.gyroscope.length > 100) {
        this.qualityHistory.gyroscope.shift();
      }
    }
    
    // 更新整体质量历史
    this.qualityHistory.overall.push({
      timestamp,
      confidence,
      quality: this.calculateQualityLevel(confidence)
    });
    
    if (this.qualityHistory.overall.length > 50) {
      this.qualityHistory.overall.shift();
    }
    
    // 更新质量指标
    this.updateQualityMetrics(confidence);
  }
  
  /**
   * 更新质量指标
   */
  updateQualityMetrics(confidence) {
    // 使用指数移动平均更新指标
    const alpha = 0.1; // 平滑因子
    
    this.qualityMetrics.overallQuality = 
      alpha * confidence + (1 - alpha) * this.qualityMetrics.overallQuality;
    
    this.qualityMetrics.confidenceLevel = confidence;
    
    // 根据异常统计更新传感器质量
    const recentOutliers = this.getRecentOutlierRate();
    this.qualityMetrics.accelerometerQuality = Math.max(0.1, 1.0 - recentOutliers.accelerometer);
    this.qualityMetrics.gyroscopeQuality = Math.max(0.1, 1.0 - recentOutliers.gyroscope);
    this.qualityMetrics.magnetometerQuality = Math.max(0.1, 1.0 - recentOutliers.magnetometer);
  }
  
  /**
   * 获取最近异常率
   */
  getRecentOutlierRate() {
    const recentPeriod = 60000; // 1分钟
    const currentTime = Date.now();
    
    // 计算最近时间内的异常率
    const recentHistory = this.qualityHistory.overall.filter(
      item => currentTime - item.timestamp < recentPeriod
    );
    
    if (recentHistory.length === 0) {
      return { accelerometer: 0, gyroscope: 0, magnetometer: 0 };
    }
    
    const outlierRate = this.anomalyStats.totalOutliers / recentHistory.length;
    
    return {
      accelerometer: outlierRate * 0.4, // 假设40%的异常来自加速计
      gyroscope: outlierRate * 0.4,     // 40%来自陀螺仪
      magnetometer: outlierRate * 0.2   // 20%来自磁力计
    };
  }
  
  /**
   * 计算质量等级
   */
  calculateQualityLevel(confidence) {
    if (confidence >= 0.9) return 'excellent';
    if (confidence >= 0.7) return 'good';
    if (confidence >= 0.5) return 'fair';
    return 'poor';
  }
  
  /**
   * 启动健康检查
   */
  startHealthCheck() {
    this.healthCheckTimer = setInterval(() => {
      this.performHealthCheck();
    }, this.config.healthCheckInterval);
  }
  
  /**
   * 执行健康检查
   */
  performHealthCheck() {
    const currentTime = Date.now();
    
    // 检查数据流是否正常
    if (currentTime - this.lastDataTime > this.dataTimeout) {
      console.warn('⚠️ 传感器数据流中断');
      this.qualityMetrics.overallQuality *= 0.8;
    }
    
    // 检查异常率
    const recentOutlierRate = this.getRecentOutlierRate();
    if (recentOutlierRate.accelerometer > 0.3 || recentOutlierRate.gyroscope > 0.3) {
      console.warn('⚠️ 传感器异常率过高');
    }
    
    // 更新整体健康状态
    this.updateOverallHealth();
  }
  
  /**
   * 更新整体健康状态
   */
  updateOverallHealth() {
    const avgQuality = (
      this.qualityMetrics.accelerometerQuality +
      this.qualityMetrics.gyroscopeQuality +
      this.qualityMetrics.magnetometerQuality
    ) / 3;
    
    this.qualityMetrics.overallQuality = Math.min(
      this.qualityMetrics.overallQuality,
      avgQuality
    );
    
    this.currentQualityLevel = this.calculateQualityLevel(this.qualityMetrics.overallQuality);
  }
  
  /**
   * 停止健康检查
   */
  stopHealthCheck() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }
  }
  
  /**
   * 重置质量指标
   */
  resetQualityMetrics() {
    this.qualityMetrics = {
      accelerometerQuality: 1.0,
      gyroscopeQuality: 1.0,
      magnetometerQuality: 1.0,
      overallQuality: 1.0,
      confidenceLevel: 1.0
    };
    
    this.anomalyStats = {
      accelerometerOutliers: 0,
      gyroscopeOutliers: 0,
      magnetometerOutliers: 0,
      totalOutliers: 0,
      lastOutlierTime: 0
    };
    
    this.qualityHistory = {
      accelerometer: [],
      gyroscope: [],
      magnetometer: [],
      overall: []
    };
    
    this.currentQualityLevel = 'excellent';
  }
  
  /**
   * 获取质量等级
   */
  getQualityLevel() {
    return this.currentQualityLevel;
  }
  
  /**
   * 获取质量报告
   */
  getQualityReport() {
    const recentOutlierRate = this.getRecentOutlierRate();
    
    return {
      qualityLevel: this.currentQualityLevel,
      metrics: { ...this.qualityMetrics },
      anomalyStats: { ...this.anomalyStats },
      outlierRates: recentOutlierRate,
      dataFlow: {
        isActive: Date.now() - this.lastDataTime < this.dataTimeout,
        lastDataTime: this.lastDataTime
      },
      historyLength: {
        accelerometer: this.qualityHistory.accelerometer.length,
        gyroscope: this.qualityHistory.gyroscope.length,
        overall: this.qualityHistory.overall.length
      }
    };
  }
  
  /**
   * 更新配置
   */
  updateConfig(config) {
    this.config = { ...this.config, ...config.quality };
    console.log('⚙️ 质量控制器配置已更新');
  }
  
  /**
   * 清理资源
   */
  destroy() {
    this.stopHealthCheck();
    this.resetQualityMetrics();
    console.log('🧹 质量控制器已清理');
  }
}

// CommonJS导出
module.exports = QualityController;