# array-max

  [![NPM version][npm-image]][npm-url]
  [![npm download][download-image]][download-url]

Get the maximum value in an array.

## Installation

`$ npm install --save ml-array-max`

## Usage

```js
import max from 'ml-array-max';

const result = max([1, 5, 3, 2, 4]);
// 5
```

## License

  [MIT](./LICENSE)

[npm-image]: https://img.shields.io/npm/v/ml-array-max.svg?style=flat-square
[npm-url]: https://npmjs.org/package/ml-array-max
[download-image]: https://img.shields.io/npm/dm/ml-array-max.svg?style=flat-square
[download-url]: https://npmjs.org/package/ml-array-max
