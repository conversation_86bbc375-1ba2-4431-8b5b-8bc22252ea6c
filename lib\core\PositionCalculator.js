/**
 * 位置计算器
 * 负责PDR位置递推和传感器融合
 */

import MotionAnalyzer from './MotionAnalyzer.js';

export default class PositionCalculator {
  constructor(config = {}) {
    this.config = {
      // PDR计算配置
      pdr: {
        enabled: true,
        stepLengthMode: 'adaptive', // 'fixed', 'adaptive', 'calibrated'
        headingCorrection: true,
        smoothingFactor: 0.3,
        ...config.pdr
      },
      
      // 融合配置
      fusion: {
        enabled: config.enableMLA !== false,
        pdrWeight: 0.7,
        mlaWeight: 0.3,
        adaptiveWeighting: true,
        confidenceThreshold: 0.4,
        outlierThreshold: 3.0,
        maxCorrection: 5.0,
        ...config.fusion
      },
      
      // 质量控制
      quality: {
        minConfidence: 0.2,
        maxDeviation: 5.0,
        positionValidation: true,
        ...config.quality
      }
    };
    
    // 运动分析器
    this.motionAnalyzer = new MotionAnalyzer(config);
    
    // 当前位置状态
    this.position = {
      x: 0,
      y: 0,
      z: 0,
      heading: 0,
      confidence: 1.0,
      timestamp: Date.now()
    };
    
    // 初始位置
    this.initialPosition = { x: 0, y: 0, z: 0 };
    
    // 位置历史
    this.positionHistory = [];
    this.maxHistoryLength = 200;
    
    // PDR状态
    this.pdrState = {
      totalDistance: 0,
      totalSteps: 0,
      avgStepLength: 0.75,
      lastStepPosition: null,
      velocity: 0,
      isInitialized: false
    };
    
    // 融合状态
    this.fusionState = {
      lastCorrection: null,
      correctionCount: 0,
      adaptiveWeights: {
        pdr: this.config.fusion.pdrWeight,
        mla: this.config.fusion.mlaWeight
      },
      qualityMetrics: {
        consistency: 1.0,
        accuracy: 1.0,
        reliability: 1.0
      }
    };
    
    // MLA节点数据（可选）
    this.mlaNodes = [];
    
    // 性能统计
    this.statistics = {
      positionUpdates: 0,
      corrections: 0,
      averageConfidence: 0,
      processingTime: [],
      driftDistance: 0
    };
    
    console.log('📍 位置计算器初始化完成');
  }
  
  /**
   * 初始化位置计算器
   * @param {Object} initialPosition - 初始位置 {x, y, z}
   */
  initialize(initialPosition = { x: 0, y: 0, z: 0 }) {
    this.initialPosition = { ...initialPosition };
    this.position = {
      ...initialPosition,
      heading: 0,
      confidence: 1.0,
      timestamp: Date.now()
    };
    
    this.pdrState.isInitialized = true;
    this.positionHistory = [{ ...this.position }];
    
    console.log('✅ 位置计算器已初始化，起始位置:', initialPosition);
  }
  
  /**
   * 更新位置计算
   * @param {Object} sensorData - 传感器数据
   * @returns {Object} 位置更新结果
   */
  update(sensorData) {
    if (!this.pdrState.isInitialized) {
      console.warn('⚠️ 位置计算器未初始化');
      return null;
    }
    
    const startTime = Date.now();
    
    try {
      // 运动分析
      const motionResult = this.motionAnalyzer.analyze(sensorData);
      
      // PDR位置更新
      let positionUpdate = null;
      if (motionResult && motionResult.stepDetected) {
        positionUpdate = this.updatePDRPosition(motionResult);
      }
      
      // 航向角更新
      if (motionResult && motionResult.headingUpdated) {
        this.updateHeading(motionResult.headingData);
      }
      
      // 平滑位置更新（即使没有步态也要更新状态）
      if (!positionUpdate) {
        positionUpdate = this.smoothPositionUpdate(sensorData);
      }
      
      // 传感器融合校正
      if (this.config.fusion.enabled) {
        positionUpdate = this.applySensorFusion(positionUpdate, sensorData);
      }
      
      // 质量验证
      const validationResult = this.validatePosition(positionUpdate);
      if (!validationResult.isValid) {
        console.warn('⚠️ 位置更新被拒绝:', validationResult.reason);
        return this.createUpdateResult(false, 'validation_failed');
      }
      
      // 应用位置更新
      this.applyPositionUpdate(positionUpdate);
      
      // 更新统计信息
      this.updateStatistics(Date.now() - startTime, positionUpdate);
      
      return this.createUpdateResult(true, 'success', positionUpdate);
      
    } catch (error) {
      console.error('❌ 位置更新失败:', error);
      return this.createUpdateResult(false, 'error', null, error);
    }
  }
  
  /**
   * 更新PDR位置
   */
  updatePDRPosition(motionResult) {
    const stepData = motionResult.stepData;
    const currentMotion = this.motionAnalyzer.getCurrentState();
    
    // 获取步长和航向
    const stepLength = stepData.stepLength;
    const heading = currentMotion.heading;
    
    // PDR递推公式
    const headingRad = heading * Math.PI / 180;
    const deltaX = stepLength * Math.cos(headingRad);
    const deltaY = stepLength * Math.sin(headingRad);
    
    // 计算新位置
    const newPosition = {
      x: this.position.x + deltaX,
      y: this.position.y + deltaY,
      z: this.position.z, // 暂不考虑高度变化
      heading: heading,
      stepLength: stepLength,
      velocity: currentMotion.velocity,
      confidence: Math.min(stepData.confidence, currentMotion.confidence),
      timestamp: Date.now(),
      stepDetected: true,
      source: 'pdr'
    };
    
    // 更新PDR状态
    this.pdrState.totalSteps++;
    this.pdrState.totalDistance += stepLength;
    this.pdrState.avgStepLength = this.pdrState.totalDistance / this.pdrState.totalSteps;
    this.pdrState.velocity = currentMotion.velocity;
    this.pdrState.lastStepPosition = { ...newPosition };
    
    return newPosition;
  }
  
  /**
   * 平滑位置更新（无步态时）
   */
  smoothPositionUpdate(sensorData) {
    const currentMotion = this.motionAnalyzer.getCurrentState();
    
    // 如果处于运动状态但没有检测到步态，进行平滑更新
    if (currentMotion.isMoving && currentMotion.velocity > 0) {
      const dt = sensorData.timestamp ? 
        (sensorData.timestamp - this.position.timestamp) / 1000 : 0.02;
      
      if (dt > 0 && dt < 1.0) { // 合理的时间间隔
        const heading = currentMotion.heading;
        const headingRad = heading * Math.PI / 180;
        const distance = currentMotion.velocity * dt;
        
        const deltaX = distance * Math.cos(headingRad);
        const deltaY = distance * Math.sin(headingRad);
        
        return {
          x: this.position.x + deltaX,
          y: this.position.y + deltaY,
          z: this.position.z,
          heading: heading,
          velocity: currentMotion.velocity,
          confidence: Math.max(0.3, currentMotion.confidence * 0.8), // 降低置信度
          timestamp: Date.now(),
          stepDetected: false,
          source: 'smooth'
        };
      }
    }
    
    // 静止状态，只更新航向和时间戳
    return {
      ...this.position,
      heading: currentMotion.heading,
      velocity: 0,
      timestamp: Date.now(),
      stepDetected: false,
      source: 'stationary'
    };
  }
  
  /**
   * 更新航向角
   */
  updateHeading(headingData) {
    // 应用航向平滑
    if (this.config.pdr.headingCorrection) {
      const smoothing = this.config.pdr.smoothingFactor;
      const angleDiff = this.normalizeAngleDiff(headingData.heading - this.position.heading);
      this.position.heading = this.normalizeAngle(this.position.heading + angleDiff * smoothing);
    } else {
      this.position.heading = headingData.heading;
    }
  }
  
  /**
   * 应用传感器融合
   */
  applySensorFusion(positionUpdate, sensorData) {
    if (!positionUpdate) return null;
    
    // 如果没有外部校正源，直接返回PDR结果
    if (this.mlaNodes.length === 0) {
      return positionUpdate;
    }
    
    // 寻找最近的MLA节点
    const nearestNode = this.findNearestMlaNode(positionUpdate);
    if (!nearestNode || nearestNode.distance > this.config.fusion.outlierThreshold) {
      return positionUpdate;
    }
    
    // 计算融合权重
    const weights = this.calculateFusionWeights(positionUpdate, nearestNode);
    
    // 执行位置融合
    const fusedPosition = this.fusePositions(positionUpdate, nearestNode, weights);
    
    // 更新融合状态
    this.updateFusionState(positionUpdate, fusedPosition, nearestNode);
    
    return fusedPosition;
  }
  
  /**
   * 寻找最近的MLA节点
   */
  findNearestMlaNode(position) {
    if (this.mlaNodes.length === 0) return null;
    
    let nearestNode = null;
    let minDistance = Infinity;
    
    for (const node of this.mlaNodes) {
      const distance = this.calculateDistance(position, node);
      if (distance < minDistance) {
        minDistance = distance;
        nearestNode = {
          ...node,
          distance: distance,
          weight: this.calculateNodeWeight(distance, node)
        };
      }
    }
    
    return nearestNode;
  }
  
  /**
   * 计算融合权重
   */
  calculateFusionWeights(pdrPosition, mlaNode) {
    let pdrWeight = this.config.fusion.pdrWeight;
    let mlaWeight = this.config.fusion.mlaWeight;
    
    if (this.config.fusion.adaptiveWeighting) {
      // 基于距离的自适应权重
      const distanceFactor = Math.max(0.1, Math.min(1.0, 
        (this.config.fusion.outlierThreshold - mlaNode.distance) / this.config.fusion.outlierThreshold
      ));
      
      // 基于置信度的权重调整
      const confidenceFactor = pdrPosition.confidence || 0.5;
      
      // 基于历史校正效果的权重调整
      const correctionFactor = this.fusionState.qualityMetrics.accuracy;
      
      // 综合权重计算
      mlaWeight = this.config.fusion.mlaWeight * distanceFactor * correctionFactor;
      pdrWeight = 1.0 - mlaWeight;
      
      // 更新自适应权重
      this.fusionState.adaptiveWeights.pdr = pdrWeight;
      this.fusionState.adaptiveWeights.mla = mlaWeight;
    }
    
    return { pdr: pdrWeight, mla: mlaWeight };
  }
  
  /**
   * 位置融合
   */
  fusePositions(pdrPosition, mlaNode, weights) {
    const fusedX = pdrPosition.x * weights.pdr + mlaNode.x * weights.mla;
    const fusedY = pdrPosition.y * weights.pdr + mlaNode.y * weights.mla;
    const fusedZ = pdrPosition.z * weights.pdr + (mlaNode.z || pdrPosition.z) * weights.mla;
    
    // 限制校正幅度
    const correctionDistance = this.calculateDistance(
      { x: fusedX, y: fusedY, z: fusedZ },
      pdrPosition
    );
    
    if (correctionDistance > this.config.fusion.maxCorrection) {
      // 限制校正距离
      const correctionRatio = this.config.fusion.maxCorrection / correctionDistance;
      const limitedX = pdrPosition.x + (fusedX - pdrPosition.x) * correctionRatio;
      const limitedY = pdrPosition.y + (fusedY - pdrPosition.y) * correctionRatio;
      const limitedZ = pdrPosition.z + (fusedZ - pdrPosition.z) * correctionRatio;
      
      return {
        ...pdrPosition,
        x: limitedX,
        y: limitedY,
        z: limitedZ,
        confidence: Math.min(pdrPosition.confidence, 0.8), // 降低置信度
        corrected: true,
        correctionDistance: this.config.fusion.maxCorrection,
        source: 'fused_limited'
      };
    }
    
    return {
      ...pdrPosition,
      x: fusedX,
      y: fusedY,
      z: fusedZ,
      confidence: Math.min(1.0, pdrPosition.confidence + weights.mla * 0.3),
      corrected: true,
      correctionDistance: correctionDistance,
      source: 'fused'
    };
  }
  
  /**
   * 更新融合状态
   */
  updateFusionState(originalPosition, fusedPosition, mlaNode) {
    // 记录最后一次校正
    this.fusionState.lastCorrection = {
      timestamp: Date.now(),
      originalPosition: { ...originalPosition },
      fusedPosition: { ...fusedPosition },
      mlaNode: { ...mlaNode },
      correctionDistance: fusedPosition.correctionDistance
    };
    
    this.fusionState.correctionCount++;
    
    // 更新质量指标
    this.updateFusionQualityMetrics(fusedPosition);
  }
  
  /**
   * 更新融合质量指标
   */
  updateFusionQualityMetrics(fusedPosition) {
    // 一致性评估：基于连续校正的变化
    const recentCorrections = this.positionHistory
      .filter(p => p.corrected && Date.now() - p.timestamp < 10000)
      .slice(-5);
    
    if (recentCorrections.length >= 2) {
      const variations = recentCorrections.map((pos, index) => {
        if (index === 0) return 0;
        return this.calculateDistance(pos, recentCorrections[index - 1]);
      }).slice(1);
      
      const avgVariation = variations.reduce((a, b) => a + b, 0) / variations.length;
      this.fusionState.qualityMetrics.consistency = Math.max(0.1, 1.0 - avgVariation / 2.0);
    }
    
    // 精度评估：基于校正距离
    if (fusedPosition.correctionDistance !== undefined) {
      const accuracyScore = Math.max(0.1, 1.0 - fusedPosition.correctionDistance / 5.0);
      this.fusionState.qualityMetrics.accuracy = 
        0.8 * this.fusionState.qualityMetrics.accuracy + 0.2 * accuracyScore;
    }
    
    // 可靠性评估：基于校正频率
    const recentTime = 30000; // 30秒
    const recentUpdates = this.positionHistory.filter(p => 
      Date.now() - p.timestamp < recentTime
    ).length;
    const recentCorrectionRate = recentCorrections.length / Math.max(1, recentUpdates);
    this.fusionState.qualityMetrics.reliability = Math.min(1.0, recentCorrectionRate * 2);
  }
  
  /**
   * 位置验证
   */
  validatePosition(positionUpdate) {
    if (!positionUpdate) {
      return { isValid: false, reason: '位置更新为空' };
    }
    
    // 检查坐标有效性
    if (!isFinite(positionUpdate.x) || !isFinite(positionUpdate.y) || !isFinite(positionUpdate.z)) {
      return { isValid: false, reason: '坐标包含无效值' };
    }
    
    // 检查置信度
    if (positionUpdate.confidence < this.config.quality.minConfidence) {
      return { isValid: false, reason: '置信度过低' };
    }
    
    // 检查位置跳跃
    if (this.positionHistory.length > 0) {
      const lastPosition = this.positionHistory[this.positionHistory.length - 1];
      const jumpDistance = this.calculateDistance(positionUpdate, lastPosition);
      const timeInterval = (positionUpdate.timestamp - lastPosition.timestamp) / 1000;
      
      if (timeInterval > 0) {
        const maxSpeed = 5.0; // 最大速度 5m/s
        const maxJump = maxSpeed * timeInterval;
        
        if (jumpDistance > maxJump) {
          return { 
            isValid: false, 
            reason: `位置跳跃过大: ${jumpDistance.toFixed(2)}m > ${maxJump.toFixed(2)}m` 
          };
        }
      }
    }
    
    return { isValid: true };
  }
  
  /**
   * 应用位置更新
   */
  applyPositionUpdate(positionUpdate) {
    // 更新当前位置
    this.position = { ...positionUpdate };
    
    // 添加到历史记录
    this.positionHistory.push({ ...positionUpdate });
    
    // 限制历史长度
    if (this.positionHistory.length > this.maxHistoryLength) {
      this.positionHistory.shift();
    }
  }
  
  /**
   * 更新统计信息
   */
  updateStatistics(processingTime, positionUpdate) {
    this.statistics.positionUpdates++;
    this.statistics.processingTime.push(processingTime);
    
    if (this.statistics.processingTime.length > 100) {
      this.statistics.processingTime.shift();
    }
    
    if (positionUpdate && positionUpdate.corrected) {
      this.statistics.corrections++;
    }
    
    // 更新平均置信度
    if (positionUpdate && positionUpdate.confidence !== undefined) {
      const alpha = 0.1;
      this.statistics.averageConfidence = alpha * positionUpdate.confidence + 
        (1 - alpha) * this.statistics.averageConfidence;
    }
    
    // 计算累积漂移距离
    this.statistics.driftDistance = this.calculateDistance(this.position, this.initialPosition);
  }
  
  /**
   * 创建更新结果
   */
  createUpdateResult(success, status, positionUpdate = null, error = null) {
    return {
      success,
      status,
      position: success ? { ...this.position } : null,
      stepDetected: positionUpdate ? positionUpdate.stepDetected : false,
      stepLength: positionUpdate ? positionUpdate.stepLength : null,
      velocity: positionUpdate ? positionUpdate.velocity : 0,
      confidence: this.position.confidence,
      corrected: positionUpdate ? positionUpdate.corrected : false,
      timestamp: Date.now(),
      error: error ? error.message : null
    };
  }
  
  /**
   * 辅助函数：计算距离
   */
  calculateDistance(pos1, pos2) {
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    const dz = pos1.z - pos2.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }
  
  /**
   * 辅助函数：计算节点权重
   */
  calculateNodeWeight(distance, node) {
    const maxDistance = this.config.fusion.outlierThreshold;
    return Math.max(0.1, (maxDistance - distance) / maxDistance);
  }
  
  /**
   * 辅助函数：角度差值标准化
   */
  normalizeAngleDiff(angleDiff) {
    while (angleDiff > 180) angleDiff -= 360;
    while (angleDiff < -180) angleDiff += 360;
    return angleDiff;
  }
  
  /**
   * 辅助函数：角度标准化
   */
  normalizeAngle(angle) {
    while (angle >= 360) angle -= 360;
    while (angle < 0) angle += 360;
    return angle;
  }
  
  // ==================== 公共API方法 ====================
  
  /**
   * 获取当前位置
   */
  getCurrentPosition() {
    return {
      ...this.position,
      motionState: this.motionAnalyzer.getCurrentState(),
      pdrState: { ...this.pdrState },
      fusionState: {
        adaptiveWeights: { ...this.fusionState.adaptiveWeights },
        qualityMetrics: { ...this.fusionState.qualityMetrics },
        correctionCount: this.fusionState.correctionCount
      }
    };
  }
  
  /**
   * 获取位置历史
   */
  getPositionHistory(limit = null) {
    return limit ? this.positionHistory.slice(-limit) : [...this.positionHistory];
  }
  
  /**
   * 设置MLA节点
   */
  setMlaNodes(nodes) {
    this.mlaNodes = [...nodes];
    console.log('📍 已设置MLA节点:', nodes.length, '个');
  }
  
  /**
   * 添加MLA节点
   */
  addMlaNode(node) {
    this.mlaNodes.push(node);
  }
  
  /**
   * 重置位置
   */
  reset(newPosition = null) {
    const resetPosition = newPosition || this.initialPosition;
    this.initialize(resetPosition);
    
    // 重置统计
    this.statistics = {
      positionUpdates: 0,
      corrections: 0,
      averageConfidence: 0,
      processingTime: [],
      driftDistance: 0
    };
    
    // 重置融合状态
    this.fusionState.lastCorrection = null;
    this.fusionState.correctionCount = 0;
    this.fusionState.adaptiveWeights = {
      pdr: this.config.fusion.pdrWeight,
      mla: this.config.fusion.mlaWeight
    };
    
    // 重置运动分析器
    this.motionAnalyzer.reset();
    
    console.log('🔄 位置计算器已重置');
  }
  
  /**
   * 更新配置
   */
  updateConfig(config) {
    this.config = { ...this.config, ...config };
    this.motionAnalyzer.updateConfig(config);
  }
  
  /**
   * 获取性能统计
   */
  getStatistics() {
    const avgProcessingTime = this.statistics.processingTime.length > 0 ?
      this.statistics.processingTime.reduce((a, b) => a + b, 0) / this.statistics.processingTime.length : 0;
    
    return {
      ...this.statistics,
      averageProcessingTime: avgProcessingTime,
      correctionRate: this.statistics.positionUpdates > 0 ? 
        this.statistics.corrections / this.statistics.positionUpdates : 0,
      motionStatistics: this.motionAnalyzer.getPerformanceReport(),
      fusionQuality: { ...this.fusionState.qualityMetrics }
    };
  }
}