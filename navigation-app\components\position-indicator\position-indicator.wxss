/* 位置指示器组件样式 */

.position-indicator-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 置信度圆圈 */
.confidence-circle {
  position: absolute;
  border: 2rpx solid;
  border-radius: 50%;
  opacity: 0.3;
  z-index: 1;
  animation: confidencePulse 2s ease-in-out infinite;
}

@keyframes confidencePulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.1;
  }
}

/* 轨迹线 */
.position-trail {
  position: absolute;
  width: 4rpx;
  height: 40rpx;
  background: rgba(102, 126, 234, 0.5);
  border-radius: 2rpx;
  z-index: 2;
}

/* 主指示器 */
.position-indicator {
  position: relative;
  transition: transform 0.3s ease;
  z-index: 10;
  cursor: pointer;
}

/* 指示器尺寸 */
.position-indicator.small .indicator-core {
  width: 40rpx;
  height: 40rpx;
}

.position-indicator.normal .indicator-core {
  width: 60rpx;
  height: 60rpx;
}

.position-indicator.large .indicator-core {
  width: 80rpx;
  height: 80rpx;
}

/* 指示器核心 */
.indicator-core {
  position: relative;
  border-radius: 50%;
  background: white;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid #667eea;
}

/* 方向箭头 */
.direction-arrow {
  position: absolute;
  top: -5rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-symbol {
  font-size: 24rpx;
  color: #667eea;
  font-weight: bold;
}

.position-indicator.small .arrow-symbol {
  font-size: 20rpx;
}

.position-indicator.large .arrow-symbol {
  font-size: 28rpx;
}

/* 中心点 */
.center-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #667eea;
}

.position-indicator.small .center-dot {
  width: 8rpx;
  height: 8rpx;
}

.position-indicator.large .center-dot {
  width: 16rpx;
  height: 16rpx;
}

/* 置信度指示环 */
.confidence-ring {
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  right: -8rpx;
  bottom: -8rpx;
  border: 2rpx solid;
  border-radius: 50%;
  animation: confidenceRing 3s linear infinite;
}

@keyframes confidenceRing {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 置信度状态样式 */
.position-indicator.high-confidence .indicator-core {
  border-color: #28a745;
  box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.3);
}

.position-indicator.high-confidence .arrow-symbol,
.position-indicator.high-confidence .center-dot {
  color: #28a745;
  background: #28a745;
}

.position-indicator.medium-confidence .indicator-core {
  border-color: #ffc107;
  box-shadow: 0 4rpx 12rpx rgba(255, 193, 7, 0.3);
}

.position-indicator.medium-confidence .arrow-symbol,
.position-indicator.medium-confidence .center-dot {
  color: #ffc107;
  background: #ffc107;
}

.position-indicator.low-confidence .indicator-core {
  border-color: #dc3545;
  box-shadow: 0 4rpx 12rpx rgba(220, 53, 69, 0.3);
}

.position-indicator.low-confidence .arrow-symbol,
.position-indicator.low-confidence .center-dot {
  color: #dc3545;
  background: #dc3545;
}

/* 移动状态 */
.position-indicator.moving {
  animation: moveIndicator 0.5s ease-out;
}

@keyframes moveIndicator {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 位置信息提示 */
.position-info {
  position: absolute;
  top: 120rpx;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  white-space: nowrap;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 20;
}

.info-text {
  margin-bottom: 5rpx;
}

.confidence-text {
  font-size: 20rpx;
  opacity: 0.8;
}

/* 脉冲动画圆圈 */
.pulse-circle {
  position: absolute;
  width: 120rpx;
  height: 120rpx;
  border: 2rpx solid #667eea;
  border-radius: 50%;
  opacity: 0.6;
  z-index: 1;
  animation: pulseExpand 0.5s ease-out;
}

@keyframes pulseExpand {
  0% {
    transform: scale(0.5);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* 交互效果 */
.position-indicator:active {
  transform: scale(0.95);
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .indicator-core {
    background: #2d3748;
    border-color: #4a5568;
  }
  
  .position-info {
    background: rgba(45, 55, 72, 0.9);
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .position-indicator.large .indicator-core {
    width: 70rpx;
    height: 70rpx;
  }
  
  .position-info {
    font-size: 22rpx;
  }
}