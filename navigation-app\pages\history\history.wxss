/* 历史页面样式 */

.history-page {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx 40rpx;
  color: white;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

/* 标签页栏 */
.tab-bar {
  display: flex;
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: #667eea10;
  border-bottom: 4rpx solid #667eea;
}

.tab-icon {
  font-size: 36rpx;
  margin-bottom: 10rpx;
}

.tab-text {
  font-size: 24rpx;
  color: #666;
}

.tab-item.active .tab-text {
  color: #667eea;
  font-weight: 600;
}

/* 标签页内容 */
.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.action-button {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  padding: 15rpx 25rpx;
  border-radius: 30rpx;
  background: #f8f9fa;
  color: #333;
  font-size: 24rpx;
}

.action-button.danger {
  background: #dc354510;
  color: #dc3545;
}

.button-icon {
  font-size: 24rpx;
  margin-right: 10rpx;
}

/* 历史记录列表 */
.history-list {
  flex: 1;
  padding: 20rpx;
}

.history-item {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 记录头部 */
.history-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f8f9fa;
}

.history-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  background: #667eea10;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 20rpx;
}

.history-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.history-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 5rpx;
}

.history-time {
  font-size: 24rpx;
  color: #666;
}

.history-summary {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.summary-distance {
  font-size: 28rpx;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 5rpx;
}

.summary-duration {
  font-size: 24rpx;
  color: #666;
}

/* 记录详情 */
.history-details {
  display: flex;
  padding: 20rpx 30rpx;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.detail-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.detail-label {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 5rpx;
}

.detail-value {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

/* 操作按钮 */
.history-actions {
  display: flex;
  padding: 20rpx 30rpx;
}

.history-action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx 10rpx;
  margin: 0 5rpx;
  border-radius: 15rpx;
  background: #f8f9fa;
  color: #333;
  font-size: 22rpx;
}

.history-action-btn.danger {
  background: #dc354510;
  color: #dc3545;
}

.btn-icon {
  font-size: 28rpx;
  margin-bottom: 5rpx;
}

/* 空状态 */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 36rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
}

.empty-action {
  background: #667eea;
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
}

/* 统计内容 */
.statistics-content {
  flex: 1;
  padding: 20rpx;
}

.stats-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stats-card {
  position: relative;
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  overflow: hidden;
}

.stats-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 10rpx;
}

.stats-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.stats-icon {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 40rpx;
  opacity: 0.2;
}

/* 精度统计 */
.accuracy-stats {
  padding: 20rpx 0;
}

.accuracy-item {
  margin-bottom: 20rpx;
}

.accuracy-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.accuracy-bar {
  position: relative;
  height: 60rpx;
  background: #e9ecef;
  border-radius: 30rpx;
  overflow: hidden;
}

.accuracy-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 30rpx;
  transition: width 0.5s ease;
}

.accuracy-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 使用统计 */
.usage-stats {
  padding: 20rpx 0;
}

.usage-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.usage-item:last-child {
  border-bottom: none;
}

.usage-label {
  font-size: 28rpx;
  color: #666;
}

.usage-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 弹窗样式 */
.detail-overlay,
.trajectory-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.detail-popup,
.trajectory-popup {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.popup-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666;
}

.detail-content {
  padding: 40rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 40rpx;
}

.section-label {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1px solid #f8f9fa;
}

.detail-row:last-child {
  border-bottom: none;
}

.row-label {
  font-size: 26rpx;
  color: #666;
}

.row-value {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

/* 轨迹内容 */
.trajectory-content {
  padding: 40rpx;
}

.trajectory-info {
  margin-bottom: 30rpx;
}

.info-text {
  font-size: 28rpx;
  color: #666;
}

.trajectory-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
  border: 2rpx dashed #ddd;
}

.placeholder-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.placeholder-desc {
  font-size: 24rpx;
  color: #ccc;
}

/* 底部间距 */
.bottom-spacing {
  height: 100rpx;
}

/* 动画效果 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .history-actions {
    flex-wrap: wrap;
  }
  
  .history-action-btn {
    min-width: 120rpx;
    margin-bottom: 10rpx;
  }
}