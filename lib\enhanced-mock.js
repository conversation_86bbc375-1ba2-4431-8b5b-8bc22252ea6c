#!/usr/bin/env node

/**
 * 增强的微信模拟环境，提供更真实的传感器数据
 */

// 模拟步行运动
class StepSimulator {
    constructor() {
        this.stepCount = 0;
        this.lastStepTime = 0;
        this.stepInterval = 500; // 每500ms一步
        this.walking = false;
    }
    
    startWalking() {
        this.walking = true;
        this.stepCount = 0;
        this.lastStepTime = Date.now();
        console.log('🚶 开始模拟步行');
    }
    
    stopWalking() {
        this.walking = false;
        console.log('🛑 停止模拟步行');
    }
    
    generateAccelerometerData() {
        const now = Date.now();
        const baseX = Math.sin(now / 200) * 0.5;
        const baseY = Math.cos(now / 300) * 0.3;
        const baseZ = 9.8 + Math.sin(now / 400) * 0.2; // 重力加速度 + 微小波动
        
        // 模拟步态冲击
        if (this.walking && now - this.lastStepTime > this.stepInterval) {
            this.stepCount++;
            this.lastStepTime = now;
            
            // 步态冲击
            const impact = Math.random() * 2.0 + 1.0;
            return {
                x: baseX + (Math.random() - 0.5) * 0.1,
                y: baseY + impact * (Math.random() > 0.5 ? 1 : -1),
                z: baseZ + impact * 0.3
            };
        }
        
        return {
            x: baseX + (Math.random() - 0.5) * 0.05,
            y: baseY + (Math.random() - 0.5) * 0.05,
            z: baseZ + (Math.random() - 0.5) * 0.1
        };
    }
    
    generateGyroscopeData() {
        return {
            x: Math.sin(Date.now() / 1000) * 0.1,
            y: Math.cos(Date.now() / 800) * 0.05,
            z: Math.sin(Date.now() / 600) * 0.08
        };
    }
    
    generateMagnetometerData() {
        return {
            x: 20 + Math.sin(Date.now() / 5000) * 5,
            y: 5 + Math.cos(Date.now() / 4000) * 3,
            z: 45 + Math.sin(Date.now() / 3000) * 2
        };
    }
}

// 创建模拟器
const stepSimulator = new StepSimulator();

// 增强的微信模拟环境
const enhancedWxMock = {
    // 模拟传感器API
    onAccelerometerChange: (callback) => {
        console.log('📱 监听加速计变化');
        // 模拟传感器数据
        setInterval(() => {
            callback(stepSimulator.generateAccelerometerData());
        }, 50); // 20Hz
    },
    
    startAccelerometer: (options) => {
        console.log('🚀 启动加速计');
        stepSimulator.startWalking();
        if (options && options.success) options.success();
    },
    
    stopAccelerometer: () => {
        console.log('⏹️ 停止加速计');
        stepSimulator.stopWalking();
    },
    
    onGyroscopeChange: (callback) => {
        console.log('📱 监听陀螺仪变化');
        setInterval(() => {
            callback(stepSimulator.generateGyroscopeData());
        }, 50);
    },
    
    startGyroscope: (options) => {
        console.log('🚀 启动陀螺仪');
        if (options && options.success) options.success();
    },
    
    stopGyroscope: () => {
        console.log('⏹️ 停止陀螺仪');
    },
    
    onCompassChange: (callback) => {
        console.log('📱 监听罗盘变化');
        setInterval(() => {
            callback(stepSimulator.generateMagnetometerData());
        }, 100);
    },
    
    startCompass: (options) => {
        console.log('🚀 启动罗盘');
        if (options && options.success) options.success();
    },
    
    stopCompass: () => {
        console.log('⏹️ 停止罗盘');
    },
    
    getSystemInfo: (options) => {
        if (options && options.success) {
            options.success({
                system: 'iOS 15.0',
                platform: 'ios',
                model: 'iPhone 13',
                benchmarkLevel: 50
            });
        }
    },
    
    getSetting: (options) => {
        if (options && options.success) {
            options.success({
                authSetting: {
                    'scope.userLocation': true
                }
            });
        }
    },
    
    getStorageSync: (key) => {
        return null;
    },
    
    setStorageSync: (key, data) => {
        console.log(`💾 存储数据: ${key}`);
    },
    
    showToast: (options) => {
        console.log(`🔔 提示: ${options.title}`);
    },
    
    showModal: (options) => {
        console.log(`📢 弹窗: ${options.title} - ${options.content}`);
        if (options && options.success) {
            options.success({ confirm: true });
        }
    },
    
    vibrateShort: () => {
        console.log('📳 短震动');
    }
};

// 设置全局wx对象
if (typeof wx === 'undefined') {
    global.wx = enhancedWxMock;
    console.log('✅ 增强版微信模拟环境已设置');
} else {
    console.log('ℹ️  微信环境已存在');
}

// 导出模拟器
export { stepSimulator, enhancedWxMock };