"use strict"

module.exports = generateMatrixProduct

var BLOCK_SIZE = 32

function unpackOrder(order) {
  return order === "r" ? [1,0] : [0,1]
}

function unpackShape(name, type) {
  if(type[1] === "native") {
    return [
      name, "d0=", name, ".length,",
      name, "d1=", name, "[0].length,"
    ].join("")
  } else {
    return [
      name, "d0=", name, ".shape[0],",
      name, "d1=", name, ".shape[1],",
      name, "s0=", name, ".stride[0],",
      name, "s1=", name, ".stride[1],",
      name, "o=", name, ".offset,",
      name, "d=", name, ".data,"
    ].join("")
  }
}

function start(order, name, type, i, j, w) {
  var code = []
  if(type[1] === "native") {
    if(order[0]) {
      if(i) {
        code.push("var ", name, "p=", name, "[", i, "];")
      } else {
        code.push("var ", name, "p=", name, "[0];")
      }
    }
  } else {
    if(i && j) {
      if(w) {
        code.push(
          "var ", name, "t0=", name, "s", order[0], ",",
                  name, "t1=", name, "s", order[1], "-", name, "s", order[0], "*", w, ",",
                  name, "p=", name, "o+", i, "*", name, "s0+", j, "*", name, "s1;")
      } else {
        code.push(
          "var ", name, "t0=", name, "s", order[0], ",",
                  name, "p=", name, "o+", i, "*", name, "s0+", j, "*", name, "s1;")
      }
    } else if(i) {
      code.push(
        "var ", name, "t0=", name, "s", order[0], ",",
                name, "p=", name, "o+", i, "*", name, "s0;")
    } else if(j) {
      code.push(
        "var ", name, "t0=", name, "s", order[0], ",",
                name, "p=", name, "o+", j, "*", name, "s1;")
    } else  {
      code.push(
        "var ", name, "t0=", name, "s", order[0], ",",
                name, "t1=", name, "s", order[1], "-", name, "s", order[0], "*", name, "d", order[0], ",",
                name, "p=", name, "o;")
    }
  }
  return code
}

function walk(order, name, type, d, i) {
  var code = []
  if(type[1] === "native") {
    if(order[0] && d === 1) {
      code.push(name, "p=", name, "[", i, "+1]")
    }
  } else {
    code.push(name, "p+=", name, "t", d, ";")
  }
  return code
}

function write(order, name, type, i, j, w) {
  var code = []
  if(type[1] === "native") {
    if(order[0]) {
      code.push(name, "p[", j, "]=", w, ";")
    } else {
      code.push(name, "[", i, "][", j, "]=", w, ";")
    }
  } else if(type[1] === "generic") {
    code.push(name, "d.set(", name, "p,", w, ");")
  } else {
    code.push(name, "d[", name, "p]=", w, ";")
  }
  return code
}

function read(order, name, type, i, j) {
  var code = []
  if(type[1] === "native") {
    if(order[0]) {
      code.push(name, "p[", j, "]")
    } else {
      code.push(name, "[", i, "][", j, "]")
    }
  } else if(type[1] === "generic") {
    code.push(name, "d.get(", name, "p)")
  } else {
    code.push(name, "d[", name, "p]")
  }
  return code.join("")
}

function generateRowColumnLoop(oType, aType, bType, useAlpha, useBeta) {
  var code = []
  var oOrd = oType[0] === "r" ? [1,0] : [0,1], aOrd = [1, 0], bOrd = [0, 1]
  var symbols = ["i", "j"]

  code.push.apply(code, start(oOrd, "o", oType))
  
  if(oOrd[1]) {
    code.push("for(j=0;j<od1;++j){")
    code.push("for(i=0;i<od0;++i){")
  } else {
    code.push("for(i=0;i<od0;++i){")
    code.push("for(j=0;j<od1;++j){")
  }

  code.push.apply(code, start(aOrd, "a", aType, "i"))
  code.push.apply(code, start(bOrd, "b", bType, undefined, "j"))

  code.push(
      "var r=0.0;",
      "for(k=0;k<ad1;++k){",
      "r+=", 
        read(aOrd, "a", aType, "i", "k"), "*", 
        read(bOrd, "b", bType, "k", "j"), ";")

  //Terminate k loop
  code.push.apply(code, walk(aOrd, "a", aType, 0, "k"))
  code.push.apply(code, walk(bOrd, "b", bType, 0, "k"))
  code.push("}")

  //Write r to output
  if(useAlpha) {
    code.push("r*=A;")
  }
  if(useBeta) {
    code.push("r+=B*", read(oOrd, "o", oType, "i", "j"), ";")
  }
  code.push.apply(code, write(oOrd, "o", oType, "i", "j", "r"))
  
  //Terminate j loop loop
  code.push.apply(code, walk(oOrd, "o", oType, 0, symbols[1]))
  code.push("}")

  //Terminate i loop
  code.push.apply(code, walk(oOrd, "o", oType, 1, symbols[0]))
  code.push("}")

  return code
}

function generateBetaPass(oType, useBeta) {
  var code = []
  var oOrd = oType[0] === "r" ? [1,0] : [0,1], symbols
  if(useBeta) {
    code.push("if(B!==1.0){")
  }
  code.push.apply(code, start(oOrd, "o", oType))
  if(oOrd[0]) {
    code.push("for(i=0;i<od0;++i){for(j=0;j<od1;++j){")
    symbols = ["i", "j"]
  } else {
    code.push("for(j=0;j<od1;++j){for(i=0;i<od0;++i){")
    symbols = ["j", "i"]
  }
  if(useBeta) {
    code.push.apply(code, write(oOrd, "o", oType, "i", "j", 
      "B*"+read(oOrd, "o", oType, "i", "j")))
  } else {
    code.push.apply(code, write(oOrd, "o", oType, "i", "j", "0"))
  }
  code.push.apply(code, walk(oOrd, "o", oType, 0, symbols[1]))
  code.push("}")
  code.push.apply(code, walk(oOrd, "o", oType, 1, symbols[0]))
  code.push("}")
  if(useBeta) {
    code.push("}")
  }
  return code
}

function generateBlockLoop(oType, aType, bType, useAlpha, useBeta) {
  var code = []
  var shapes = [ "od0", "od1", "ad1" ]
  var oOrd = [1, 0]
  var aOrd = [1, 0]
  var bOrd = [0, 1]

  //Do pass over output to zero it out
  code.push.apply(code, generateBetaPass(oType, useBeta))

  for(var i=0; i<3; ++i) {
    code.push(
      "for(var i", i, "=", shapes[i], ";i", i, ">0;){",
        "var w", i, "=", BLOCK_SIZE, ";",
        "if(i", i, "<", BLOCK_SIZE, "){",
          "w", i, "=i", i, ";",
          "i", i, "=0;",
        "}else{",
          "i", i, "-=", BLOCK_SIZE, ";",
        "}")
  }

  code.push.apply(code, start(oOrd, "o", oType, "i0", "i1", "w1"))
  
  code.push("for(i=0;i<w0;++i){\
for(j=0;j<w1;++j){\
var r=0.0;")

  code.push.apply(code, start(aOrd, "a", aType, "(i0+i)", "i2"))
  code.push.apply(code, start(bOrd, "b", bType, "i2", "(i1+j)"))

  code.push("for(k=0;k<w2;++k){")

  code.push("r+=",
    read(aOrd, "a", aType, "(i0+i)", "(i2+k)"), "*", 
    read(bOrd, "b", bType, "(i2+k)", "(i1+j)"), ";")

  //Close off k-loop
  code.push.apply(code, walk(aOrd, "a", aType, 0, "(i2+k)"))
  code.push.apply(code, walk(bOrd, "b", bType, 0, "(i2+k)"))
  code.push("}")

  //Write r back to output array
  var sym = "r"
  if(useAlpha) {
    sym = "A*r"
  }
  code.push.apply(code, write(oOrd, "o", oType, "(i0+i)", "(i1+j)", 
    sym + "+" + read(oOrd, "o", oType, "(i0+i)", "(i1+j)")))

  //Close off j-loop
  code.push.apply(code, walk(oOrd, "o", oType, 0, "(i1+j)"))
  code.push("}")

  //Close off i-loop
  code.push.apply(code, walk(oOrd, "o", oType, 1, "(i0+i)"))
  code.push("}}}}")

  return code
}

function generateMatrixProduct(outType, aType, bType, useAlpha, useBeta) {
  var funcName = ["gemm", outType[0], outType[1], 
                     "a", aType[0], aType[1],
                     "b", bType[0], bType[1],
                     useAlpha ? "alpha" : "",
                     useBeta ? "beta" : "" ].join("")
  var code = [
    "function ", funcName, "(o,a,b,A,B){",
    "var ", unpackShape("o", outType), 
            unpackShape("a", aType),
            unpackShape("b", bType),
            "i,j,k;"
  ]

  if(aType[0] === "r" && bType[0] === "c") {
    code.push.apply(code, generateRowColumnLoop(outType, aType, bType, useAlpha, useBeta))
  } else {
    code.push.apply(code, generateBlockLoop(outType, aType, bType, useAlpha, useBeta))
  }

  code.push("}return ", funcName)

  //Compile function
  var proc = new Function(code.join(""))
  return proc()
}