{"name": "numjs", "version": "0.16.1", "description": "Like NumPy, in JavaScript", "main": "src/index.js", "directories": {"test": "test"}, "dependencies": {"cwise": "^1.0.8", "deasync": "^0.1.21", "lodash": "^4.17.21", "ndarray": "^1.0.18", "ndarray-fft": "^1.0.0", "ndarray-gemm": "^1.0.0", "ndarray-ops": "^1.2.2", "sharp": "^0.27.2", "typedarray-pool": "^1.1.0"}, "devDependencies": {"browserify-shim": "^3.8.12", "chai": "^3.5.0", "expect.js": "^0.3.1", "grunt": "^1.3.0", "grunt-browserify": "^5.0.0", "grunt-contrib-uglify": "latest", "grunt-gh-pages": "^2.0.0", "grunt-jsdoc": "^1.1.0", "grunt-karma": "^2.0.0", "grunt-semistandard": "^1.0.6", "grunt-simple-mocha": "^0.4.0", "ink-docstrap": "^1.1.1", "jasmine": "^2.2.1", "jasmine-core": "^2.2.0", "jit-grunt": "^0.10.0", "karma": "^2.0.2", "karma-browserify": "^5.0.1", "karma-chai": "^0.1.0", "karma-jasmine": "^0.3.5", "karma-mocha": "^0.2.2", "karma-mocha-reporter": "latest", "karma-phantomjs-launcher": "^1.0.4", "mocha": "^5.2.0", "phantomjs-prebuilt": "^2.1.16", "semistandard": "^12.0.1"}, "semistandard": {"globals": ["HTMLImageElement", "HTMLCanvasElement", "nj", "Image"]}, "scripts": {"test": "grunt test", "preversion": "grunt test && git ci dist -m 'bump version' --allow-empty ", "postversion": "npm publish && grunt doc"}, "repository": {"type": "git", "url": "git://github.com/nicolaspanel/numjs.git"}, "keywords": ["n<PERSON><PERSON>", "array", "multi", "multidimensional", "dimension", "higher", "image", "volume", "webgl", "tensor", "matrix", "linear", "algebra", "science", "numerical", "computing", "stride", "shape", "numpy"], "author": "<PERSON>", "license": "MIT", "readmeFilename": "README.md", "browser": {"./src/images/resize.js": "./src/images/resize-dom.js", "./src/images/read.js": "./src/images/read-dom.js", "./src/images/save.js": "./src/images/save-dom.js", "buffer": "./src/buffer-dom.js"}, "browserify-shim": {}, "browserify": {"transform": ["cwise", "browserify-shim"]}, "standard": {"globals": ["HTMLImageElement", "HTMLCanvasElement", "nj", "Image"]}}