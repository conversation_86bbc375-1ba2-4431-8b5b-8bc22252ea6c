#!/usr/bin/env node

/**
 * 测试各种模式的输出格式一致性
 */

async function testAllModes() {
    console.log('🎯 测试所有运行模式的输出格式\n');
    
    try {
        // 导入库
        const { default: WXInertialNavigation } = await import('./WXInertialNavigation.js');
        
        const modes = ['lite', 'standard', 'precise'];
        const results = {};
        
        for (const mode of modes) {
            console.log(`\n📊 测试 ${mode} 模式:`);
            console.log('-'.repeat(30));
            
            try {
                // 创建实例
                const nav = new WXInertialNavigation({
                    initialPosition: { x: 0, y: 0, z: 0 },
                    mode: mode,
                    sampleRate: 50
                });
                
                console.log('✅ 实例创建成功');
                
                // 获取位置信息
                const location = nav.getCurrentLocation();
                
                // 获取统计信息
                const stats = nav.getStatistics();
                
                // 获取轨迹
                const trajectory = nav.getTrajectory();
                
                // 记录结果
                results[mode] = {
                    config: nav.config,
                    location: location,
                    stats: stats,
                    trajectory: trajectory,
                    locationKeys: Object.keys(location),
                    statsKeys: Object.keys(stats),
                    locationType: typeof location,
                    statsType: typeof stats,
                    trajectoryType: Array.isArray(trajectory) ? 'array' : typeof trajectory
                };
                
                // 显示基本信息
                console.log('📋 配置:');
                console.log(`   模式: ${nav.config.mode}`);
                console.log(`   采样率: ${nav.config.sampleRate}Hz`);
                console.log(`   MLA启用: ${nav.config.enableMLA}`);
                
                console.log('📍 位置信息格式:');
                console.log(`   类型: ${typeof location}`);
                console.log(`   属性数量: ${Object.keys(location).length}`);
                console.log(`   主要属性: ${Object.keys(location).join(', ')}`);
                
                console.log('📊 统计信息格式:');
                console.log(`   类型: ${typeof stats}`);
                console.log(`   属性数量: ${Object.keys(stats).length}`);
                
                console.log('🛤️ 轨迹信息格式:');
                console.log(`   类型: ${Array.isArray(trajectory) ? 'array' : typeof trajectory}`);
                console.log(`   点数: ${trajectory.length}`);
                
            } catch (error) {
                console.error(`❌ ${mode}模式测试失败:`, error.message);
                results[mode] = { error: error.message };
            }
        }
        
        // 比较各种模式的输出格式
        console.log('\n🔍 格式一致性比较:');
        console.log('='.repeat(50));
        
        const referenceMode = 'standard';
        if (results[referenceMode] && !results[referenceMode].error) {
            console.log(`参考模式: ${referenceMode}`);
            
            for (const mode of modes) {
                if (mode !== referenceMode && results[mode] && !results[mode].error) {
                    console.log(`\n📋 ${mode}模式 vs ${referenceMode}模式:`);
                    
                    // 比较位置信息格式
                    const locMatch = arraysEqual(results[mode].locationKeys, results[referenceMode].locationKeys);
                    console.log(`   位置属性一致: ${locMatch ? '✅' : '❌'}`);
                    
                    // 比较统计信息格式
                    const statsMatch = arraysEqual(results[mode].statsKeys, results[referenceMode].statsKeys);
                    console.log(`   统计属性一致: ${statsMatch ? '✅' : '❌'}`);
                    
                    // 比较类型
                    const locTypeMatch = results[mode].locationType === results[referenceMode].locationType;
                    const statsTypeMatch = results[mode].statsType === results[referenceMode].statsType;
                    const trajTypeMatch = results[mode].trajectoryType === results[referenceMode].trajectoryType;
                    
                    console.log(`   位置类型一致: ${locTypeMatch ? '✅' : '❌'}`);
                    console.log(`   统计类型一致: ${statsTypeMatch ? '✅' : '❌'}`);
                    console.log(`   轨迹类型一致: ${trajTypeMatch ? '✅' : '❌'}`);
                    
                    if (!locMatch || !statsMatch || !locTypeMatch || !statsTypeMatch || !trajTypeMatch) {
                        console.log('   ⚠️  格式不一致详情:');
                        if (!locMatch) {
                            console.log(`     位置属性差异: ${findArrayDifferences(results[mode].locationKeys, results[referenceMode].locationKeys)}`);
                        }
                        if (!statsMatch) {
                            console.log(`     统计属性差异: ${findArrayDifferences(results[mode].statsKeys, results[referenceMode].statsKeys)}`);
                        }
                    }
                }
            }
        }
        
        // 显示各种模式的配置差异
        console.log('\n⚙️ 各种模式的配置差异:');
        console.log('='.repeat(40));
        
        for (const mode of modes) {
            if (results[mode] && !results[mode].error) {
                console.log(`\n${mode.toUpperCase()}模式配置:`);
                console.log(`   采样率: ${results[mode].config.sampleRate}Hz`);
                console.log(`   MLA启用: ${results[mode].config.enableMLA}`);
                if (results[mode].config.fusion) {
                    console.log(`   平滑因子: ${results[mode].config.fusion.smoothingFactor}`);
                    console.log(`   自适应权重: ${results[mode].config.fusion.adaptiveWeighting || false}`);
                }
                if (results[mode].config.quality) {
                    console.log(`   最小置信度: ${results[mode].config.quality.minConfidence}`);
                }
            }
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        return false;
    }
}

// 辅助函数：比较数组是否相等
function arraysEqual(arr1, arr2) {
    if (arr1.length !== arr2.length) return false;
    return arr1.every((item, index) => item === arr2[index]);
}

// 辅助函数：找出数组差异
function findArrayDifferences(arr1, arr2) {
    const diff1 = arr1.filter(item => !arr2.includes(item));
    const diff2 = arr2.filter(item => !arr1.includes(item));
    
    if (diff1.length === 0 && diff2.length === 0) return '无差异';
    
    let result = '';
    if (diff1.length > 0) result += `多出: ${diff1.join(', ')}`;
    if (diff2.length > 0) {
        if (result) result += '; ';
        result += `缺少: ${diff2.join(', ')}`;
    }
    
    return result;
}

// 运行测试
testAllModes().then(success => {
    console.log('\n' + '='.repeat(60));
    console.log(success ? '🎉 模式测试完成' : '❌ 模式测试失败');
    
    if (success) {
        console.log('\n📋 结论:');
        console.log('   各种模式的输出格式应该保持一致');
        console.log('   主要差异在于内部配置参数');
        console.log('   用户接口（API返回值）应该统一');
    }
    
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('❌ 测试运行失败:', error);
    process.exit(1);
});